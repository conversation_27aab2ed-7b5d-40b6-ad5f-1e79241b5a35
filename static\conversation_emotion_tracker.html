<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversation Emotion Tracker</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            padding-top: 30px;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            border: none;
            margin-bottom: 20px;
        }
        
        .card-header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            text-align: center;
            padding: 15px;
        }
        
        .emotion-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            text-align: center;
        }
        
        .emotion-badge {
            font-size: 1.2rem;
            padding: 8px 16px;
            border-radius: 20px;
            margin: 5px;
            display: inline-block;
            color: white;
            font-weight: bold;
        }
        
        .emotion-happy { background: linear-gradient(45deg, #FFD700, #FFA500); }
        .emotion-sad { background: linear-gradient(45deg, #4682B4, #1E90FF); }
        .emotion-angry { background: linear-gradient(45deg, #FF6347, #DC143C); }
        .emotion-excited { background: linear-gradient(45deg, #FF69B4, #FF1493); }
        .emotion-calm { background: linear-gradient(45deg, #98FB98, #90EE90); }
        .emotion-neutral { background: linear-gradient(45deg, #D3D3D3, #A9A9A9); }
        .emotion-fear { background: linear-gradient(45deg, #800080, #9932CC); }
        .emotion-surprise { background: linear-gradient(45deg, #FF8C00, #FF7F50); }
        
        .capture-btn {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: none;
            font-size: 1.5rem;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .record-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }
        
        .record-btn.recording {
            background: linear-gradient(45deg, #ff4757, #ff3838);
            animation: pulse 1.5s infinite;
        }
        
        .camera-btn {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .video-container {
            position: relative;
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
        }
        
        #videoElement {
            width: 100%;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .status-message {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
        }
        
        .status-success { background-color: #d4edda; color: #155724; }
        .status-error { background-color: #f8d7da; color: #721c24; }
        .status-info { background-color: #d1ecf1; color: #0c5460; }
        
        .emotion-history {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .emotion-item {
            background: white;
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .progress-container {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <!-- Configuration Panel -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog me-2"></i>Conversation Setup</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="patientId" class="form-label">Patient ID</label>
                            <input type="text" class="form-control" id="patientId" 
                                   value="f31a95c6-76ef-4bb2-936c-b258285682d9" 
                                   placeholder="Enter patient ID">
                        </div>
                        <div class="mb-3">
                            <label for="conversationId" class="form-label">Conversation ID</label>
                            <input type="text" class="form-control" id="conversationId" 
                                   placeholder="Enter conversation ID or leave blank">
                        </div>
                        <div class="mb-3">
                            <label for="sessionId" class="form-label">Session ID (Optional)</label>
                            <input type="text" class="form-control" id="sessionId" 
                                   placeholder="Auto-generated if empty">
                        </div>
                        <button id="generateIds" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-random me-1"></i>Generate New IDs
                        </button>
                    </div>
                </div>
                
                <!-- Current Emotions Display -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-heart me-2"></i>Current Emotions</h5>
                    </div>
                    <div class="card-body">
                        <div id="currentAudioEmotion" class="emotion-card">
                            <strong>Audio:</strong>
                            <div id="audioEmotionDisplay">No data</div>
                        </div>
                        <div id="currentImageEmotion" class="emotion-card">
                            <strong>Image:</strong>
                            <div id="imageEmotionDisplay">No data</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Capture Panel -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-video me-2"></i>Real-time Emotion Capture</h5>
                    </div>
                    <div class="card-body">
                        <!-- Video Feed -->
                        <div class="video-container mb-3">
                            <video id="videoElement" autoplay muted></video>
                            <canvas id="captureCanvas" style="display: none;"></canvas>
                        </div>
                        
                        <!-- Control Buttons -->
                        <div class="text-center mb-3">
                            <button id="startCameraBtn" class="capture-btn camera-btn">
                                <i class="fas fa-camera"></i>
                            </button>
                            <button id="captureImageBtn" class="capture-btn camera-btn" disabled>
                                <i class="fas fa-camera-retro"></i>
                            </button>
                            <button id="recordAudioBtn" class="capture-btn record-btn">
                                <i class="fas fa-microphone"></i>
                            </button>
                        </div>
                        
                        <div class="text-center">
                            <small class="text-muted">
                                <i class="fas fa-camera me-1"></i>Start Camera | 
                                <i class="fas fa-camera-retro me-1"></i>Capture Image | 
                                <i class="fas fa-microphone me-1"></i>Record Audio
                            </small>
                        </div>
                        
                        <!-- Status Messages -->
                        <div id="statusContainer"></div>
                        
                        <!-- Emotion Results -->
                        <div id="emotionResults" style="display: none;">
                            <h6 class="mt-3">Latest Analysis Results</h6>
                            <div id="latestResults"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Conversation History -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history me-2"></i>Conversation Emotion History</h5>
                        <button id="loadHistoryBtn" class="btn btn-outline-light btn-sm float-end">
                            <i class="fas fa-refresh me-1"></i>Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="emotionHistory" class="emotion-history">
                            <p class="text-muted text-center">Load conversation history to see emotion timeline</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // JWT Token - Use static token like chat_final3
        const JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.Ij0BnOgMwKFwB7VOzRdL-qdpEXNF7YvZZ8R_c-vKbcM";

        // Global variables
        let videoStream = null;
        let mediaRecorder = null;
        let audioChunks = [];
        let isRecording = false;
        let isCameraActive = false;
        
        // DOM Elements
        const patientIdInput = document.getElementById('patientId');
        const conversationIdInput = document.getElementById('conversationId');
        const sessionIdInput = document.getElementById('sessionId');
        const generateIdsBtn = document.getElementById('generateIds');
        const videoElement = document.getElementById('videoElement');
        const captureCanvas = document.getElementById('captureCanvas');
        const startCameraBtn = document.getElementById('startCameraBtn');
        const captureImageBtn = document.getElementById('captureImageBtn');
        const recordAudioBtn = document.getElementById('recordAudioBtn');
        const statusContainer = document.getElementById('statusContainer');
        const emotionResults = document.getElementById('emotionResults');
        const latestResults = document.getElementById('latestResults');
        const loadHistoryBtn = document.getElementById('loadHistoryBtn');
        const emotionHistory = document.getElementById('emotionHistory');
        const audioEmotionDisplay = document.getElementById('audioEmotionDisplay');
        const imageEmotionDisplay = document.getElementById('imageEmotionDisplay');
        
        // Utility Functions
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status-message status-${type}`;
            statusDiv.textContent = message;
            statusContainer.innerHTML = '';
            statusContainer.appendChild(statusDiv);
            
            setTimeout(() => {
                statusDiv.remove();
            }, 5000);
        }
        
        function getConversationData() {
            const patientId = patientIdInput.value.trim();
            const conversationId = conversationIdInput.value.trim() || generateUUID();
            const sessionId = sessionIdInput.value.trim();
            
            if (!patientId) {
                showStatus('Patient ID is required', 'error');
                return null;
            }
            
            // Update conversation ID if it was generated
            if (!conversationIdInput.value.trim()) {
                conversationIdInput.value = conversationId;
            }
            
            return { patientId, conversationId, sessionId };
        }
        
        // Event Listeners
        generateIdsBtn.addEventListener('click', () => {
            conversationIdInput.value = generateUUID();
            sessionIdInput.value = `session_${Date.now()}`;
            showStatus('New IDs generated', 'success');
        });
        
        // Camera Functions
        startCameraBtn.addEventListener('click', async () => {
            if (!isCameraActive) {
                try {
                    videoStream = await navigator.mediaDevices.getUserMedia({ 
                        video: { width: 640, height: 480 }, 
                        audio: false 
                    });
                    videoElement.srcObject = videoStream;
                    isCameraActive = true;
                    startCameraBtn.innerHTML = '<i class="fas fa-stop"></i>';
                    captureImageBtn.disabled = false;
                    showStatus('Camera started', 'success');
                } catch (error) {
                    console.error('Error accessing camera:', error);
                    showStatus('Error accessing camera: ' + error.message, 'error');
                }
            } else {
                if (videoStream) {
                    videoStream.getTracks().forEach(track => track.stop());
                    videoElement.srcObject = null;
                }
                isCameraActive = false;
                startCameraBtn.innerHTML = '<i class="fas fa-camera"></i>';
                captureImageBtn.disabled = true;
                showStatus('Camera stopped', 'info');
            }
        });
        
        // Image Capture
        captureImageBtn.addEventListener('click', async () => {
            const conversationData = getConversationData();
            if (!conversationData) return;
            
            try {
                // Capture image from video
                const canvas = captureCanvas;
                const context = canvas.getContext('2d');
                canvas.width = videoElement.videoWidth;
                canvas.height = videoElement.videoHeight;
                context.drawImage(videoElement, 0, 0);
                
                // Convert to base64
                const imageBase64 = canvas.toDataURL('image/jpeg', 0.8);
                
                showStatus('Analyzing facial emotion...', 'info');
                
                // Send to API
                const response = await fetch('/conversation-image/analyze-conversation-image', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${JWT_TOKEN}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        patient_id: conversationData.patientId,
                        conversation_id: conversationData.conversationId,
                        session_id: conversationData.sessionId,
                        image_base64: imageBase64,
                        timestamp: new Date().toISOString()
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    displayImageEmotion(result);
                    showStatus('Image emotion analyzed successfully!', 'success');
                } else {
                    const error = await response.json();
                    showStatus('Error: ' + error.detail, 'error');
                }
                
            } catch (error) {
                console.error('Error capturing image:', error);
                showStatus('Error capturing image: ' + error.message, 'error');
            }
        });
        
        // Audio Recording
        recordAudioBtn.addEventListener('click', async () => {
            const conversationData = getConversationData();
            if (!conversationData) return;
            
            if (!isRecording) {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    mediaRecorder = new MediaRecorder(stream);
                    audioChunks = [];
                    
                    mediaRecorder.ondataavailable = (event) => {
                        audioChunks.push(event.data);
                    };
                    
                    mediaRecorder.onstop = async () => {
                        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                        await analyzeAudioEmotion(audioBlob, conversationData);
                        
                        // Stop all tracks
                        stream.getTracks().forEach(track => track.stop());
                    };
                    
                    mediaRecorder.start();
                    isRecording = true;
                    recordAudioBtn.classList.add('recording');
                    recordAudioBtn.innerHTML = '<i class="fas fa-stop"></i>';
                    showStatus('Recording audio... Click to stop', 'info');
                    
                } catch (error) {
                    console.error('Error accessing microphone:', error);
                    showStatus('Error accessing microphone: ' + error.message, 'error');
                }
            } else {
                mediaRecorder.stop();
                isRecording = false;
                recordAudioBtn.classList.remove('recording');
                recordAudioBtn.innerHTML = '<i class="fas fa-microphone"></i>';
                showStatus('Processing audio...', 'info');
            }
        });
        
        // Audio Analysis
        async function analyzeAudioEmotion(audioBlob, conversationData) {
            try {
                // Convert blob to base64
                const reader = new FileReader();
                reader.onloadend = async () => {
                    const audioBase64 = reader.result;
                    
                    const response = await fetch('/conversation-audio/analyze-conversation-audio', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${JWT_TOKEN}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            patient_id: conversationData.patientId,
                            conversation_id: conversationData.conversationId,
                            session_id: conversationData.sessionId,
                            audio_base64: audioBase64,
                            timestamp: new Date().toISOString()
                        })
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        displayAudioEmotion(result);
                        showStatus('Audio emotion analyzed successfully!', 'success');
                    } else {
                        const error = await response.json();
                        showStatus('Error: ' + error.detail, 'error');
                    }
                };
                reader.readAsDataURL(audioBlob);
                
            } catch (error) {
                console.error('Error analyzing audio:', error);
                showStatus('Error analyzing audio: ' + error.message, 'error');
            }
        }
        
        // Display Functions
        function displayAudioEmotion(result) {
            const emotionData = result.emotion_data;
            audioEmotionDisplay.innerHTML = `
                <span class="emotion-badge emotion-${emotionData.emotion}">
                    ${emotionData.emotion.toUpperCase()}
                </span>
                <div class="mt-2">
                    <small>Confidence: ${Math.round(emotionData.confidence * 100)}%</small><br>
                    <small>Arousal: ${Math.round(emotionData.arousal * 100)}% | Valence: ${Math.round(emotionData.valence * 100)}%</small><br>
                    <small class="text-muted">Sample ID: ${result.sample_id}</small>
                </div>
            `;

            // Show success message
            showStatus(`🎤 Audio emotion detected: ${emotionData.emotion} (${Math.round(emotionData.confidence * 100)}%)`, 'success');

            updateLatestResults('Audio', result);
        }
        
        function displayImageEmotion(result) {
            const emotionData = result.emotion_data;
            imageEmotionDisplay.innerHTML = `
                <span class="emotion-badge emotion-${emotionData.emotion}">
                    ${emotionData.emotion.toUpperCase()}
                </span>
                <div class="mt-2">
                    <small>Confidence: ${Math.round(emotionData.confidence * 100)}%</small><br>
                    <small class="text-muted">Sample ID: ${result.sample_id}</small>
                </div>
            `;

            // Show success message
            showStatus(`📷 Facial emotion detected: ${emotionData.emotion} (${Math.round(emotionData.confidence * 100)}%)`, 'success');

            updateLatestResults('Image', result);
        }
        
        function updateLatestResults(type, result) {
            emotionResults.style.display = 'block';
            const resultDiv = document.createElement('div');
            resultDiv.className = 'emotion-item';
            resultDiv.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${type}:</strong>
                        <span class="emotion-badge emotion-${result.emotion_data.emotion}" style="font-size: 0.9rem; padding: 3px 8px;">
                            ${result.emotion_data.emotion}
                        </span>
                    </div>
                    <div class="text-end">
                        <small class="text-muted">${new Date(result.timestamp).toLocaleTimeString()}</small>
                    </div>
                </div>
            `;
            
            latestResults.insertBefore(resultDiv, latestResults.firstChild);
            
            // Keep only last 5 results
            while (latestResults.children.length > 5) {
                latestResults.removeChild(latestResults.lastChild);
            }
        }
        
        // Load Conversation History
        loadHistoryBtn.addEventListener('click', async () => {
            const conversationData = getConversationData();
            if (!conversationData) return;
            
            try {
                showStatus('Loading conversation history...', 'info');
                
                const response = await fetch(`/conversation-audio/conversation/${conversationData.conversationId}/emotions`, {
                    headers: {
                        'Authorization': `Bearer ${JWT_TOKEN}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    displayConversationHistory(data);
                    showStatus(`Loaded ${data.total_audio_samples + data.total_image_samples} emotion samples`, 'success');
                } else {
                    const error = await response.json();
                    showStatus('Error loading history: ' + error.detail, 'error');
                }
                
            } catch (error) {
                console.error('Error loading history:', error);
                showStatus('Error loading history: ' + error.message, 'error');
            }
        });
        
        function displayConversationHistory(data) {
            emotionHistory.innerHTML = '';
            
            if (data.total_audio_samples === 0 && data.total_image_samples === 0) {
                emotionHistory.innerHTML = '<p class="text-muted text-center">No emotion data found for this conversation.</p>';
                return;
            }
            
            // Combine and sort all emotions by timestamp
            const allEmotions = [...data.audio_emotions, ...data.image_emotions]
                .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
            
            allEmotions.forEach(emotion => {
                const emotionItem = document.createElement('div');
                emotionItem.className = 'emotion-item';
                emotionItem.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${emotion.source.toUpperCase()}:</strong>
                            <span class="emotion-badge emotion-${emotion.emotion}" style="font-size: 0.9rem; padding: 3px 8px;">
                                ${emotion.emotion}
                            </span>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">${new Date(emotion.timestamp).toLocaleString()}</small><br>
                            <small>Confidence: ${Math.round(emotion.confidence * 100)}%</small>
                        </div>
                    </div>
                    ${emotion.arousal !== undefined ? `
                        <div class="mt-1">
                            <small>Arousal: ${Math.round(emotion.arousal * 100)}% | Valence: ${Math.round(emotion.valence * 100)}%</small>
                        </div>
                    ` : ''}
                `;
                emotionHistory.appendChild(emotionItem);
            });
        }
        
        // Initialize
        window.addEventListener('load', () => {
            // Generate initial conversation ID if empty
            if (!conversationIdInput.value) {
                conversationIdInput.value = generateUUID();
            }

            console.log('🎭 Conversation Emotion Tracker Ready!');
            console.log('🔑 Using static JWT token for authentication');
        });
    </script>
</body>
</html>
