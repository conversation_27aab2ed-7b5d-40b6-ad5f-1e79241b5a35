# PowerShell script to stop, remove, and run Docker container with .env

# Define container and image names
$containerName = "prasha-chatbot"
$imageName = "prasha-chatbot"  # Change this if your image is named differently
$envFile = ".env"
$port = 8000

# Stop and remove existing container (if any)
Write-Host "🛑 Stopping and removing any existing container..."
docker stop $containerName 2>$null
docker rm $containerName 2>$null

# Check if .env exists
if (-Not (Test-Path $envFile)) {
    Write-Error "❌ .env file not found. Please make sure $envFile exists."
    exit 1
}

# Run the Docker container with .env file
Write-Host "🚀 Starting container '$containerName' from image '$imageName'..."
docker run -d `
    --name $containerName `
    --env-file $envFile `
    -p "$port`:$port" `
    $imageName

# Wait a bit before showing logs
Start-Sleep -Seconds 5

Write-Host "`n✅ Container started. Streaming logs now..."
docker logs -f $containerName
