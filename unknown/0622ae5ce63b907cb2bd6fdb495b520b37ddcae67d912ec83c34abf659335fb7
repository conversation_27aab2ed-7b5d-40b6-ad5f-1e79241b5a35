from database.database import SessionLocal
from sqlalchemy import text

db = SessionLocal()

# Check all tables in the database
print("=== All Tables in Database ===")
tables = db.execute(text("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'")).fetchall()
for table in tables:
    print(table[0])

# Check Doctor table structure
print("\n=== Doctor Table Structure ===")
doctor_columns = db.execute(text("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'doctors' ORDER BY ordinal_position")).fetchall()
for col in doctor_columns:
    print(f"{col[0]}: {col[1]}")

# Check DoctorAvailability table structure (using the correct table name)
print("\n=== DoctorAvailability Table Structure ===")
avail_tables = [t[0] for t in tables if 'avail' in t[0].lower()]
if avail_tables:
    for avail_table in avail_tables:
        print(f"Table: {avail_table}")
        avail_columns = db.execute(text(f"SELECT column_name, data_type FROM information_schema.columns WHERE table_name = '{avail_table}' ORDER BY ordinal_position")).fetchall()
        for col in avail_columns:
            print(f"{col[0]}: {col[1]}")
else:
    print("No availability table found")

# Check Rating table structure
print("\n=== Rating Table Structure ===")
rating_columns = db.execute(text("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'ratings' ORDER BY ordinal_position")).fetchall()
for col in rating_columns:
    print(f"{col[0]}: {col[1]}")

# Check Patient table structure
print("\n=== Patient Table Structure ===")
patient_columns = db.execute(text("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'patients' ORDER BY ordinal_position")).fetchall()
for col in patient_columns:
    print(f"{col[0]}: {col[1]}")

# Check if tables have data
print("\n=== Table Data Counts ===")
for table_name in tables:
    try:
        count = db.execute(text(f"SELECT COUNT(*) FROM {table_name[0]}")).fetchone()[0]
        print(f"{table_name[0]}: {count}")
    except Exception as e:
        print(f"Error counting {table_name[0]}: {str(e)}")

db.close()
