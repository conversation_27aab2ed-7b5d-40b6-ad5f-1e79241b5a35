# PowerShell script to enable S3 integration in the .env file

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host ".env file not found" -ForegroundColor Red
    exit 1
}

# Read the current .env file
$envContent = Get-Content ".env" -Raw

# Check if USE_S3 is already set
if ($envContent -match "USE_S3=True") {
    Write-Host "S3 integration is already enabled in .env file" -ForegroundColor Green
} else {
    # Update or add USE_S3
    if ($envContent -match "USE_S3=False") {
        # Replace existing setting
        $envContent = $envContent -replace "USE_S3=False", "USE_S3=True"
    } elseif ($envContent -match "USE_S3=") {
        # Replace any other value
        $envContent = $envContent -replace "USE_S3=.*", "USE_S3=True"
    } else {
        # Add the setting if it doesn't exist
        $envContent += "`nUSE_S3=True`n"
    }
    
    # Write the updated content back to the .env file
    $envContent | Out-File -FilePath ".env" -Force
    Write-Host "S3 integration enabled in .env file" -ForegroundColor Green
}

# Check if other required S3 settings are present
$missingSettings = @()

if (-not ($envContent -match "PDF_BUCKET_NAME=")) {
    $missingSettings += "PDF_BUCKET_NAME"
}

if (-not ($envContent -match "AWS_ACCESS_KEY_ID=")) {
    $missingSettings += "AWS_ACCESS_KEY_ID"
}

if (-not ($envContent -match "AWS_SECRET_ACCESS_KEY=")) {
    $missingSettings += "AWS_SECRET_ACCESS_KEY"
}

if (-not ($envContent -match "AWS_REGION=")) {
    $missingSettings += "AWS_REGION"
}

if ($missingSettings.Count -gt 0) {
    Write-Host "The following required S3 settings are missing in your .env file:" -ForegroundColor Yellow
    foreach ($setting in $missingSettings) {
        Write-Host "  - $setting" -ForegroundColor Yellow
    }
    Write-Host "Please add these settings to your .env file" -ForegroundColor Yellow
} else {
    Write-Host "All required S3 settings are present in your .env file" -ForegroundColor Green
}

Write-Host "S3 integration setup complete!" -ForegroundColor Cyan
