import psycopg2
import os
from dotenv import load_dotenv
import json

# Load environment variables
load_dotenv()

def inspect_table_schema(table_name):
    """
    Inspect the schema of a specific table in the database.
    
    Args:
        table_name: Name of the table to inspect
        
    Returns:
        Dictionary with column information
    """
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=os.getenv('postgres_host'),
            database=os.getenv('postgres_database'),
            user=os.getenv('postgres_username'),
            password=os.getenv('postgres_password'),
            port=os.getenv('postgres_port')
        )
        
        # Create a cursor
        cur = conn.cursor()
        
        # Query to get column information
        query = """
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = %s
        ORDER BY ordinal_position;
        """
        
        # Execute the query
        cur.execute(query, (table_name,))
        
        # Fetch all results
        columns = cur.fetchall()
        
        # Format the results
        result = {
            "table_name": table_name,
            "columns": [
                {
                    "name": col[0],
                    "type": col[1],
                    "nullable": col[2]
                }
                for col in columns
            ]
        }
        
        # Close cursor and connection
        cur.close()
        conn.close()
        
        return result
    
    except Exception as e:
        return {"error": str(e)}

# Tables to inspect
tables = ["patients", "doctors", "appointments", "prescriptions", "medicalhistory"]

# Inspect each table
results = {}
for table in tables:
    results[table] = inspect_table_schema(table)

# Print the results
print(json.dumps(results, indent=2))

# Save results to a file
with open("db_schema.json", "w") as f:
    json.dump(results, f, indent=2)

print("\nSchema information saved to db_schema.json")
