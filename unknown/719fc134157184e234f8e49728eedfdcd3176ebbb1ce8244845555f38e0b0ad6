from database.database import SessionL<PERSON><PERSON>
from model.model_correct import Patient
import sys

def debug_patient_validation():
    """Debug the patient validation process."""
    # Patient ID from our database
    patient_id = "f31a95c6-76ef-4bb2-936c-b258285682d9"
    
    print(f"Debugging patient validation for patient ID: {patient_id}")
    
    # Create a new database session
    db = SessionLocal()
    
    try:
        # Try to query the patient directly
        print("Attempting to query patient...")
        patient = db.query(Patient).filter(Patient.patient_id == patient_id).first()
        
        if patient:
            print(f"Patient found: {patient.first_name} {patient.last_name}")
            print(f"Patient attributes:")
            for attr in dir(patient):
                if not attr.startswith('_') and attr != 'metadata':
                    try:
                        value = getattr(patient, attr)
                        print(f"  {attr}: {value}")
                    except Exception as e:
                        print(f"  {attr}: Error accessing attribute - {str(e)}")
        else:
            print(f"Patient not found with ID: {patient_id}")
            
            # Try to get all patients to see if any exist
            all_patients = db.query(Patient).all()
            print(f"Total patients in database: {len(all_patients)}")
            
            if all_patients:
                print("First few patients:")
                for i, p in enumerate(all_patients[:3]):
                    print(f"  {i+1}. {p.patient_id}: {p.first_name} {p.last_name}")
    
    except Exception as e:
        print(f"Error during patient validation: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()
        print("Database session closed.")

if __name__ == "__main__":
    debug_patient_validation()
