import psycopg2
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def update_patients_table():
    """Update the patients table to match the model_correct.py definition."""
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=os.getenv('postgres_host'),
            database=os.getenv('postgres_database'),
            user=os.getenv('postgres_username'),
            password=os.getenv('postgres_password'),
            port=os.getenv('postgres_port')
        )
        
        # Create a cursor
        cur = conn.cursor()
        
        # Check if middle_name column exists
        cur.execute("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'patients' AND column_name = 'middle_name'
        """)
        
        if not cur.fetchone():
            print("Adding middle_name column to patients table...")
            # Add middle_name column
            cur.execute("""
            ALTER TABLE patients 
            ADD COLUMN middle_name VARCHAR(100)
            """)
            print("middle_name column added successfully.")
        else:
            print("middle_name column already exists.")
        
        # Commit the changes
        conn.commit()
        
        # Close cursor and connection
        cur.close()
        conn.close()
        
        print("Database schema updated successfully.")
        
    except Exception as e:
        print(f"Error updating database schema: {str(e)}")

if __name__ == "__main__":
    update_patients_table()
