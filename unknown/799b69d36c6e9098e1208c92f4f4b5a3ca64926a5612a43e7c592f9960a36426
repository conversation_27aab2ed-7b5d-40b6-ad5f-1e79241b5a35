import psycopg2
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Connect to the database
try:
    conn = psycopg2.connect(
        host=os.getenv('postgres_host'),
        database=os.getenv('postgres_database'),
        user=os.getenv('postgres_username'),
        password=os.getenv('postgres_password'),
        port=os.getenv('postgres_port')
    )
    
    # Create a cursor
    cur = conn.cursor()
    
    # Execute a query
    cur.execute('SELECT patient_id FROM patients LIMIT 1')
    
    # Fetch the result
    result = cur.fetchone()
    
    # Print the result
    if result:
        print(f'Patient ID: {result[0]}')
    else:
        print('No patients found')
    
    # Close the cursor and connection
    cur.close()
    conn.close()
    
except Exception as e:
    print(f'Error: {str(e)}')
