import psycopg2
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def update_patients_table():
    """Update the patients table to match the model_correct.py definition."""
    try:
        # Connect to the database
        conn = psycopg2.connect(
            host=os.getenv('postgres_host'),
            database=os.getenv('postgres_database'),
            user=os.getenv('postgres_username'),
            password=os.getenv('postgres_password'),
            port=os.getenv('postgres_port')
        )
        
        # Create a cursor
        cur = conn.cursor()
        
        # List of columns to check and add if missing
        columns_to_add = [
            ("middle_name", "VARCHAR(100)"),
            ("email", "VARCHAR(100)")
        ]
        
        for column_name, data_type in columns_to_add:
            # Check if column exists
            cur.execute(f"""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'patients' AND column_name = '{column_name}'
            """)
            
            if not cur.fetchone():
                print(f"Adding {column_name} column to patients table...")
                # Add column
                cur.execute(f"""
                ALTER TABLE patients 
                ADD COLUMN {column_name} {data_type}
                """)
                print(f"{column_name} column added successfully.")
            else:
                print(f"{column_name} column already exists.")
        
        # Commit the changes
        conn.commit()
        
        # Close cursor and connection
        cur.close()
        conn.close()
        
        print("Database schema updated successfully.")
        
    except Exception as e:
        print(f"Error updating database schema: {str(e)}")

if __name__ == "__main__":
    update_patients_table()
