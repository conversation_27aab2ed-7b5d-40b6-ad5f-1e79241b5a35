import uuid
import os
import sys
from datetime import datetime, date, time, timedelta
import random
import json
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Import models
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from model.model_correct import (
    Base, User, Patient, Doctor, Appointment, DoctorAvailability,
    Prescription, MedicalHistory, ChatMessage, EmotionAnalysis,
    DiaryEntry, OnboardingQuestion
)
from database.database import SessionLocal, engine

def create_tables():
    """Create all tables in the database."""
    logger.info("Creating tables...")
    Base.metadata.create_all(bind=engine)
    logger.info("Tables created successfully.")

def generate_uuid():
    """Generate a random UUID."""
    return str(uuid.uuid4())

def create_users(db, num_users=5):
    """Create sample users."""
    logger.info(f"Creating {num_users} users...")
    users = []
    roles = ["patient", "doctor", "admin"]
    genders = ["M", "F"]

    for i in range(num_users):
        user_id = generate_uuid()
        role = random.choice(roles)

        user = User(
            user_id=user_id,
            title="Mr." if random.choice(genders) == "M" else "Ms.",
            first_name=f"FirstName{i}",
            last_name=f"LastName{i}",
            gender=random.choice(genders),
            email=f"user{i}@example.com",
            password_hash="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "password"
            roles=role,
            is_active=True,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        db.add(user)
        users.append(user)

    db.commit()
    logger.info(f"Created {len(users)} users.")
    return users

def create_patients(db, users, num_patients=3):
    """Create sample patients."""
    logger.info(f"Creating {num_patients} patients...")
    patients = []

    # Get users with patient role
    patient_users = [user for user in users if "patient" in user.roles]

    # If not enough patient users, use any users
    if len(patient_users) < num_patients:
        patient_users = users

    for i in range(min(num_patients, len(patient_users))):
        user = patient_users[i]

        # Create treatment as JSONB
        treatment_data = {
            "primary_condition": random.choice(["Anxiety", "Depression", "Stress", "PTSD"]),
            "secondary_conditions": random.sample(["Insomnia", "Panic Attacks", "Social Anxiety"],
                                                 k=random.randint(0, 2)),
            "treatment_plan": {
                "therapy": random.choice(["CBT", "DBT", "Psychotherapy"]),
                "frequency": random.choice(["Weekly", "Bi-weekly", "Monthly"]),
                "duration": f"{random.randint(3, 12)} months"
            }
        }

        # Create interests as JSONB
        interests_data = {
            "hobbies": random.sample(["Reading", "Hiking", "Cooking", "Painting", "Music"],
                                     k=random.randint(1, 3)),
            "preferences": {
                "communication": random.choice(["Email", "Phone", "In-person"]),
                "appointment_time": random.choice(["Morning", "Afternoon", "Evening"])
            }
        }

        # Create preferences as JSONB
        preferences_data = {
            "notification": {
                "email": random.choice([True, False]),
                "sms": random.choice([True, False])
            },
            "appointment": {
                "reminder_time": random.choice(["1 day", "2 days", "1 hour"]),
                "preferred_days": random.sample(["Monday", "Wednesday", "Friday"], k=random.randint(1, 3))
            }
        }

        patient = Patient(
            patient_id=generate_uuid(),
            user_id=user.user_id,
            title=user.title,
            first_name=user.first_name,
            middle_name=None,
            last_name=user.last_name,
            dob=date(1980 + i, random.randint(1, 12), random.randint(1, 28)),
            age=random.randint(25, 65),
            gender=user.gender,
            language="English",
            religion="N/A",
            address=f"{random.randint(100, 999)} Main St, City{i}",
            phone=f"******-{random.randint(100, 999)}-{random.randint(1000, 9999)}",
            email=f"patient{i}@example.com",
            interests=json.dumps(interests_data),  # Convert to string
            treatment=json.dumps(treatment_data),  # Convert to string
            health_score=random.randint(50, 100),
            under_medications=random.choice([True, False]),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        db.add(patient)
        patients.append(patient)

    db.commit()
    logger.info(f"Created {len(patients)} patients.")
    return patients

def create_doctors(db, users, num_doctors=2):
    """Create sample doctors."""
    logger.info(f"Creating {num_doctors} doctors...")
    doctors = []
    specializations = ["Psychiatry", "Psychology", "Therapy", "Counseling"]

    # Get users with doctor role
    doctor_users = [user for user in users if "doctor" in user.roles]

    # If not enough doctor users, use any users
    if len(doctor_users) < num_doctors:
        doctor_users = users

    for i in range(min(num_doctors, len(doctor_users))):
        user = doctor_users[i]

        doctor = Doctor(
            doctor_id=generate_uuid(),
            user_id=user.user_id,
            title=f"Dr.",
            first_name=user.first_name,
            middle_name=None,
            last_name=user.last_name,
            dob=date(1970 + i, random.randint(1, 12), random.randint(1, 28)),
            age=random.randint(35, 65),
            gender=user.gender,
            language="English",
            religion="N/A",
            address=f"{random.randint(100, 999)} Medical Ave, City{i}",
            phone=f"******-{random.randint(100, 999)}-{random.randint(1000, 9999)}",
            email=f"doctor{i}@example.com",
            interests="Mental health, Research",
            specialization=random.choice(specializations),
            consultation_fee=random.randint(100, 300),
            treatment="Cognitive Behavioral Therapy, Medication Management",
            health_score=random.randint(80, 100),
            under_medications=False,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        db.add(doctor)
        doctors.append(doctor)

    db.commit()
    logger.info(f"Created {len(doctors)} doctors.")
    return doctors

def create_doctor_availability(db, doctors):
    """Create availability for doctors."""
    logger.info("Creating doctor availability...")
    availabilities = []
    days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]

    for doctor in doctors:
        # Create 3 availability slots for each doctor
        for _ in range(3):
            day = random.choice(days)
            start_hour = random.randint(8, 15)

            availability = DoctorAvailability(
                availability_id=generate_uuid(),
                doctor_id=doctor.doctor_id,
                day_of_week=day,
                start_time=time(start_hour, 0),
                end_time=time(start_hour + 4, 0)
            )

            db.add(availability)
            availabilities.append(availability)

    db.commit()
    logger.info(f"Created {len(availabilities)} availability slots.")
    return availabilities

def create_appointments(db, patients, doctors, num_appointments=5):
    """Create sample appointments."""
    logger.info(f"Creating {num_appointments} appointments...")
    appointments = []
    statuses = ["Scheduled", "Completed", "Cancelled"]
    consultation_types = ["In-person", "Virtual", "Phone"]
    visit_reasons = [
        "Initial consultation",
        "Follow-up",
        "Medication review",
        "Therapy session",
        "Emergency consultation"
    ]

    for i in range(num_appointments):
        patient = random.choice(patients)
        doctor = random.choice(doctors)

        # Create appointments in the past, present, and future
        days_offset = random.randint(-10, 20)
        appointment_date = date.today() + timedelta(days=days_offset)

        # Set status based on date
        if days_offset < 0:
            status = "Completed"
        elif days_offset == 0:
            status = random.choice(["Scheduled", "Completed"])
        else:
            status = "Scheduled"

        appointment = Appointment(
            appointment_id=generate_uuid(),
            patient_id=patient.patient_id,
            doctor_id=doctor.doctor_id,
            appointment_date=appointment_date,
            appointment_time=time(random.randint(9, 17), random.choice([0, 30])),
            visit_reason=random.choice(visit_reasons),
            consultation_type=random.choice(consultation_types),
            status=status,
            notes=f"Notes for appointment {i+1}" if random.choice([True, False]) else None,
            created_at=datetime.now() - timedelta(days=random.randint(5, 30)),
            updated_at=datetime.now()
        )

        db.add(appointment)
        appointments.append(appointment)

    db.commit()
    logger.info(f"Created {len(appointments)} appointments.")
    return appointments

def create_prescriptions(db, patients, doctors, num_prescriptions=8):
    """Create sample prescriptions."""
    logger.info(f"Creating {num_prescriptions} prescriptions...")
    prescriptions = []
    medications = [
        "Sertraline", "Fluoxetine", "Escitalopram", "Venlafaxine",
        "Bupropion", "Alprazolam", "Lorazepam", "Clonazepam"
    ]
    dosages = ["10mg", "20mg", "50mg", "100mg"]
    statuses = ["Active", "Completed", "Discontinued"]

    for i in range(num_prescriptions):
        patient = random.choice(patients)
        doctor = random.choice(doctors)

        start_date = date.today() - timedelta(days=random.randint(30, 180))

        # Some prescriptions have end dates, some don't
        if random.choice([True, False]):
            end_date = start_date + timedelta(days=random.randint(30, 90))
            status = random.choice(["Completed", "Discontinued"])
        else:
            end_date = None
            status = "Active"

        prescription = Prescription(
            prescription_id=generate_uuid(),
            patient_id=patient.patient_id,
            doctor_id=doctor.doctor_id,
            medication_name=random.choice(medications),
            dosage=random.choice(dosages),
            instructions=f"Take {random.choice(['once', 'twice'])} daily with food.",
            start_date=start_date,
            end_date=end_date,
            status=status,
            created_at=datetime.now() - timedelta(days=random.randint(30, 180)),
            updated_at=datetime.now()
        )

        db.add(prescription)
        prescriptions.append(prescription)

    db.commit()
    logger.info(f"Created {len(prescriptions)} prescriptions.")
    return prescriptions

def create_medical_history(db, patients, doctors, num_records=10):
    """Create sample medical history records."""
    logger.info(f"Creating {num_records} medical history records...")
    records = []
    diagnoses = [
        "Major Depressive Disorder",
        "Generalized Anxiety Disorder",
        "Panic Disorder",
        "Social Anxiety Disorder",
        "Post-Traumatic Stress Disorder",
        "Bipolar Disorder",
        "Obsessive-Compulsive Disorder",
        "Attention Deficit Hyperactivity Disorder"
    ]
    treatments = [
        "Cognitive Behavioral Therapy",
        "Medication Management",
        "Psychotherapy",
        "Dialectical Behavior Therapy",
        "Mindfulness-Based Therapy",
        "Group Therapy"
    ]

    for i in range(num_records):
        patient = random.choice(patients)
        doctor = random.choice(doctors)

        diagnosed_date = date.today() - timedelta(days=random.randint(30, 365))

        record = MedicalHistory(
            history_id=generate_uuid(),
            patient_id=patient.patient_id,
            diagnosis=random.choice(diagnoses),
            treatment=random.choice(treatments),
            diagnosed_date=diagnosed_date,
            doctor_id=doctor.doctor_id,
            additional_notes=f"Additional notes for record {i+1}" if random.choice([True, False]) else None,
            created_at=datetime.now() - timedelta(days=random.randint(30, 365)),
            updated_at=datetime.now()
        )

        db.add(record)
        records.append(record)

    db.commit()
    logger.info(f"Created {len(records)} medical history records.")
    return records

def create_chat_messages(db, patients, num_messages=20):
    """Create sample chat messages."""
    logger.info(f"Creating {num_messages} chat messages...")
    messages = []
    ai_doctor_id = "00000000-0000-0000-0000-000000000000"  # AI Doctor ID

    patient_messages = [
        "Hello, I'm not feeling well today.",
        "I've been having trouble sleeping lately.",
        "My medication is causing some side effects.",
        "I'm feeling anxious about my upcoming appointment.",
        "Can you help me with my depression?",
        "I'm having a panic attack right now.",
        "I forgot to take my medication yesterday.",
        "I'm feeling much better today.",
        "When is my next appointment?",
        "Do I need to increase my dosage?"
    ]

    doctor_messages = [
        "I'm sorry to hear that. Can you tell me more about your symptoms?",
        "How long have you been experiencing these issues?",
        "What kind of side effects are you experiencing?",
        "It's normal to feel anxious. Let's talk about what's bothering you.",
        "I'm here to help. Tell me more about how you're feeling.",
        "Try some deep breathing exercises. Inhale for 4 counts, hold for 7, exhale for 8.",
        "That's okay. Just make sure to take it today and continue your regular schedule.",
        "I'm glad to hear that! What do you think has been helping?",
        "Let me check your schedule and get back to you.",
        "Let's discuss your current dosage and how it's affecting you."
    ]

    for i in range(num_messages):
        patient = random.choice(patients)

        # Alternate between patient and doctor messages
        if i % 2 == 0:
            sender_id = str(patient.patient_id)
            receiver_id = ai_doctor_id
            message_text = random.choice(patient_messages)
        else:
            sender_id = ai_doctor_id
            receiver_id = str(patient.patient_id)
            message_text = random.choice(doctor_messages)

        # Create timestamp with some randomness
        timestamp = datetime.now() - timedelta(days=random.randint(0, 30),
                                              hours=random.randint(0, 23),
                                              minutes=random.randint(0, 59))

        message = ChatMessage(
            chat_message_id=generate_uuid(),
            sender_id=sender_id,
            receiver_id=receiver_id,
            message_text=message_text,
            extracted_keywords=",".join(random.sample(["anxiety", "depression", "sleep", "medication", "feeling"],
                                                     k=random.randint(0, 3))),
            timestamp=timestamp
        )

        db.add(message)
        messages.append(message)

    db.commit()
    logger.info(f"Created {len(messages)} chat messages.")
    return messages

def create_emotion_analysis(db, chat_messages):
    """Create emotion analysis for chat messages."""
    logger.info("Creating emotion analysis for chat messages...")
    analyses = []
    emotions = ["happy", "sad", "angry", "anxious", "neutral", "surprised", "fearful"]

    for message in chat_messages:
        # Only analyze patient messages
        if message.sender_id != "00000000-0000-0000-0000-000000000000":
            emotion = random.choice(emotions)
            confidence = round(random.uniform(0.5, 0.95), 2)

            analysis = EmotionAnalysis(
                emotion_id=generate_uuid(),
                chat_message_id=message.chat_message_id,
                patient_id=message.sender_id,
                emotion_category=emotion,
                confidence_score=confidence,
                analyzed_at=message.timestamp + timedelta(seconds=random.randint(1, 60))
            )

            db.add(analysis)
            analyses.append(analysis)

    db.commit()
    logger.info(f"Created {len(analyses)} emotion analyses.")
    return analyses

def create_diary_entries(db, patients, num_entries=15):
    """Create sample diary entries."""
    logger.info(f"Creating {num_entries} diary entries...")
    entries = []

    diary_notes = [
        "Today was a good day. I felt more energetic than usual.",
        "I'm struggling with my anxiety today. Everything feels overwhelming.",
        "Had a productive therapy session. Learned new coping strategies.",
        "Didn't sleep well last night. Feeling tired and irritable.",
        "Started my new medication today. Hoping it helps with my symptoms.",
        "Spent time with friends. Social interaction really improved my mood.",
        "Practiced mindfulness meditation for 20 minutes. Feeling calmer.",
        "Had a panic attack at work. Need to discuss this with my doctor.",
        "Feeling more hopeful today. The treatment seems to be working.",
        "Difficult day. My depression symptoms were worse than usual."
    ]

    for i in range(num_entries):
        patient = random.choice(patients)

        # Create timestamp with some randomness
        created_at = datetime.now() - timedelta(days=random.randint(0, 60),
                                               hours=random.randint(0, 23))

        entry = DiaryEntry(
            event_id=generate_uuid(),
            patient_id=str(patient.patient_id),
            notes=random.choice(diary_notes),
            created_at=created_at
        )

        db.add(entry)
        entries.append(entry)

    db.commit()
    logger.info(f"Created {len(entries)} diary entries.")
    return entries

def create_onboarding_questions(db, patients, num_questions_per_patient=5):
    """Create sample onboarding questions."""
    logger.info(f"Creating onboarding questions for {len(patients)} patients...")
    questions = []

    question_templates = [
        {"question": "How would you rate your overall mental health?",
         "category": "general_health",
         "answers": ["Excellent", "Good", "Fair", "Poor", "Very poor"]},
        {"question": "Have you ever been diagnosed with a mental health condition?",
         "category": "medical_history",
         "answers": ["Yes", "No", "Prefer not to say"]},
        {"question": "Are you currently taking any medications for mental health?",
         "category": "medications",
         "answers": ["Yes", "No"]},
        {"question": "How often do you experience anxiety?",
         "category": "symptoms",
         "answers": ["Never", "Rarely", "Sometimes", "Often", "Always"]},
        {"question": "How often do you experience depression?",
         "category": "symptoms",
         "answers": ["Never", "Rarely", "Sometimes", "Often", "Always"]},
        {"question": "Do you have trouble sleeping?",
         "category": "symptoms",
         "answers": ["Never", "Rarely", "Sometimes", "Often", "Always"]},
        {"question": "Have you ever had thoughts of harming yourself?",
         "category": "risk_assessment",
         "answers": ["Never", "Rarely", "Sometimes", "Often", "Always"]},
        {"question": "What are your goals for treatment?",
         "category": "treatment_goals",
         "answers": ["Reduce anxiety", "Manage depression", "Improve sleep", "Cope with trauma", "General well-being"]},
        {"question": "Do you prefer in-person or virtual appointments?",
         "category": "preferences",
         "answers": ["In-person", "Virtual", "No preference"]},
        {"question": "How did you hear about our services?",
         "category": "general",
         "answers": ["Friend/Family", "Doctor referral", "Internet search", "Social media", "Other"]}
    ]

    for patient in patients:
        # Select random questions for each patient
        selected_questions = random.sample(question_templates, min(num_questions_per_patient, len(question_templates)))

        for q_template in selected_questions:
            question = OnboardingQuestion(
                question_id=generate_uuid(),
                patient_id=str(patient.patient_id),
                question=q_template["question"],
                answer=random.choice(q_template["answers"]),
                category=q_template["category"],
                timestamp=datetime.now() - timedelta(days=random.randint(1, 60))
            )

            db.add(question)
            questions.append(question)

    db.commit()
    logger.info(f"Created {len(questions)} onboarding questions.")
    return questions

def main():
    """Main function to set up the database."""
    try:
        # Create a database session
        db = SessionLocal()

        # Create tables
        create_tables()

        # Create sample data
        users = create_users(db, num_users=10)
        patients = create_patients(db, users, num_patients=5)
        doctors = create_doctors(db, users, num_doctors=3)
        create_doctor_availability(db, doctors)
        create_appointments(db, patients, doctors, num_appointments=15)
        create_prescriptions(db, patients, doctors, num_prescriptions=20)
        create_medical_history(db, patients, doctors, num_records=25)
        chat_messages = create_chat_messages(db, patients, num_messages=50)
        create_emotion_analysis(db, chat_messages)
        create_diary_entries(db, patients, num_entries=30)
        create_onboarding_questions(db, patients, num_questions_per_patient=8)

        # Print sample IDs for testing
        sample_patient = random.choice(patients)
        sample_doctor = random.choice(doctors)

        logger.info("\n=== SAMPLE IDs FOR TESTING ===")
        logger.info(f"Sample Patient ID: {sample_patient.patient_id}")
        logger.info(f"Sample Doctor ID: {sample_doctor.doctor_id}")
        logger.info("==============================\n")

        # Close the session
        db.close()

        logger.info("Database setup completed successfully!")

    except Exception as e:
        logger.error(f"Error setting up database: {str(e)}")
        raise

if __name__ == "__main__":
    main()
