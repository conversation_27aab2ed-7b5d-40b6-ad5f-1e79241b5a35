{"patients": {"table_name": "patients", "columns": [{"name": "patient_id", "type": "uuid", "nullable": "NO"}, {"name": "user_id", "type": "uuid", "nullable": "NO"}, {"name": "title", "type": "character varying", "nullable": "YES"}, {"name": "first_name", "type": "character varying", "nullable": "YES"}, {"name": "last_name", "type": "character varying", "nullable": "YES"}, {"name": "dob", "type": "timestamp with time zone", "nullable": "YES"}, {"name": "age", "type": "integer", "nullable": "YES"}, {"name": "gender", "type": "character varying", "nullable": "YES"}, {"name": "language", "type": "character varying", "nullable": "YES"}, {"name": "religion", "type": "character varying", "nullable": "YES"}, {"name": "address", "type": "character varying", "nullable": "YES"}, {"name": "phone", "type": "character varying", "nullable": "YES"}, {"name": "health_score", "type": "integer", "nullable": "YES"}, {"name": "under_medications", "type": "boolean", "nullable": "YES"}, {"name": "created_at", "type": "timestamp with time zone", "nullable": "YES"}, {"name": "updated_at", "type": "timestamp with time zone", "nullable": "YES"}, {"name": "region", "type": "character varying", "nullable": "YES"}, {"name": "isOnboarded", "type": "boolean", "nullable": "YES"}, {"name": "onboardingCompletedAt", "type": "timestamp with time zone", "nullable": "YES"}, {"name": "timezone", "type": "character varying", "nullable": "YES"}, {"name": "preferences", "type": "jsonb", "nullable": "YES"}, {"name": "interests", "type": "jsonb", "nullable": "YES"}, {"name": "treatment", "type": "jsonb", "nullable": "YES"}]}, "doctors": {"table_name": "doctors", "columns": [{"name": "doctor_id", "type": "uuid", "nullable": "NO"}, {"name": "user_id", "type": "uuid", "nullable": "NO"}, {"name": "title", "type": "character varying", "nullable": "YES"}, {"name": "first_name", "type": "character varying", "nullable": "NO"}, {"name": "middle_name", "type": "character varying", "nullable": "YES"}, {"name": "last_name", "type": "character varying", "nullable": "NO"}, {"name": "dob", "type": "date", "nullable": "NO"}, {"name": "age", "type": "integer", "nullable": "YES"}, {"name": "gender", "type": "USER-DEFINED", "nullable": "NO"}, {"name": "language", "type": "character varying", "nullable": "YES"}, {"name": "religion", "type": "character varying", "nullable": "YES"}, {"name": "address", "type": "text", "nullable": "YES"}, {"name": "phone", "type": "character varying", "nullable": "YES"}, {"name": "email", "type": "character varying", "nullable": "YES"}, {"name": "interests", "type": "text", "nullable": "YES"}, {"name": "specialization", "type": "text", "nullable": "YES"}, {"name": "consultation_fee", "type": "numeric", "nullable": "NO"}, {"name": "treatment", "type": "text", "nullable": "NO"}, {"name": "health_score", "type": "integer", "nullable": "YES"}, {"name": "under_medications", "type": "boolean", "nullable": "YES"}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": "YES"}]}, "appointments": {"table_name": "appointments", "columns": [{"name": "appointment_id", "type": "uuid", "nullable": "NO"}, {"name": "patient_id", "type": "uuid", "nullable": "YES"}, {"name": "doctor_id", "type": "uuid", "nullable": "YES"}, {"name": "appointment_date", "type": "date", "nullable": "NO"}, {"name": "appointment_time", "type": "time without time zone", "nullable": "NO"}, {"name": "visit_reason", "type": "text", "nullable": "YES"}, {"name": "consultation_type", "type": "character varying", "nullable": "YES"}, {"name": "status", "type": "USER-DEFINED", "nullable": "YES"}, {"name": "notes", "type": "text", "nullable": "YES"}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": "YES"}]}, "prescriptions": {"table_name": "prescriptions", "columns": [{"name": "prescription_id", "type": "uuid", "nullable": "NO"}, {"name": "patient_id", "type": "uuid", "nullable": "YES"}, {"name": "doctor_id", "type": "uuid", "nullable": "YES"}, {"name": "medication_name", "type": "character varying", "nullable": "NO"}, {"name": "dosage", "type": "character varying", "nullable": "NO"}, {"name": "instructions", "type": "text", "nullable": "YES"}, {"name": "start_date", "type": "date", "nullable": "NO"}, {"name": "end_date", "type": "date", "nullable": "YES"}, {"name": "status", "type": "USER-DEFINED", "nullable": "YES"}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": "YES"}]}, "medicalhistory": {"table_name": "medicalhistory", "columns": [{"name": "history_id", "type": "uuid", "nullable": "NO"}, {"name": "patient_id", "type": "uuid", "nullable": "YES"}, {"name": "diagnosis", "type": "text", "nullable": "NO"}, {"name": "treatment", "type": "text", "nullable": "YES"}, {"name": "diagnosed_date", "type": "date", "nullable": "NO"}, {"name": "doctor_id", "type": "uuid", "nullable": "YES"}, {"name": "additional_notes", "type": "text", "nullable": "YES"}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": "YES"}]}}