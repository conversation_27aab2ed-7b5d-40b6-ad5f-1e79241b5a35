<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Upload and Processing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .file-input {
            margin: 15px 0;
        }
        .btn {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        #status, #serviceStatus {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .hidden {
            display: none;
        }
        details {
            margin: 10px 0;
            padding: 5px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        summary {
            cursor: pointer;
            font-weight: bold;
            padding: 5px;
        }
        details ul {
            max-height: 200px;
            overflow-y: auto;
            margin: 5px 0;
            padding-left: 25px;
        }
        #searchContainer {
            margin-top: 30px;
        }
        #searchResults {
            margin-top: 20px;
        }
        .result-item {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .result-content {
            margin-bottom: 10px;
        }
        .result-metadata {
            font-size: 0.9em;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        .loading:after {
            content: "...";
            animation: dots 1.5s steps(5, end) infinite;
        }
        @keyframes dots {
            0%, 20% { content: "."; }
            40% { content: ".."; }
            60%, 100% { content: "..."; }
        }
    </style>
</head>
<body>
    <h1>PDF Upload and Processing</h1>

    <div class="container" id="serviceStatusContainer">
        <h2>Service Status</h2>
        <button class="btn" id="checkStatusBtn">Check Service Status</button>
        <div id="serviceStatus" class="hidden"></div>
    </div>

    <div class="container" id="uploadContainer">
        <h2>Upload PDF</h2>
        <form id="uploadForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="title">Title (optional):</label>
                <input type="text" id="title" name="title" placeholder="Enter a title for the PDF">
            </div>

            <div class="form-group">
                <label for="description">Description (optional):</label>
                <textarea id="description" name="description" placeholder="Enter a description for the PDF"></textarea>
            </div>

            <div class="form-group file-input">
                <label for="pdfFile">Select PDF file:</label>
                <input type="file" id="pdfFile" name="file" accept=".pdf" required>
            </div>

            <button type="submit" class="btn" id="uploadBtn">Upload and Process</button>
        </form>

        <div id="status" class="hidden"></div>
        <div id="processingDetails" class="hidden">
            <h3>Processing Details</h3>
            <p><strong>PDF ID:</strong> <span id="pdfId"></span></p>
            <p><strong>Number of Pages:</strong> <span id="numPages"></span></p>
            <p><strong>Processing Time:</strong> <span id="processingTime"></span> seconds</p>
            <p id="s3UrlContainer" style="display: none;"><strong>S3 URL:</strong> <a id="s3Url" href="#" target="_blank"></a></p>
            <div>
                <h4>Text Sample:</h4>
                <pre id="textSample" style="white-space: pre-wrap; background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;"></pre>
            </div>
        </div>
    </div>

    <div class="container" id="searchContainer">
        <h2>Search Processed PDFs</h2>
        <div class="form-group">
            <label for="searchQuery">Search Query:</label>
            <input type="text" id="searchQuery" placeholder="Enter search terms">
        </div>

        <button class="btn" id="searchBtn">Search</button>

        <div id="searchStatus" class="hidden"></div>
        <div id="searchResults"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const uploadForm = document.getElementById('uploadForm');
            const statusDiv = document.getElementById('status');
            const processingDetails = document.getElementById('processingDetails');
            const searchBtn = document.getElementById('searchBtn');
            const searchQuery = document.getElementById('searchQuery');
            const searchResults = document.getElementById('searchResults');
            const searchStatus = document.getElementById('searchStatus');
            const checkStatusBtn = document.getElementById('checkStatusBtn');
            const serviceStatus = document.getElementById('serviceStatus');

            // No authentication required

            // Service status check
            checkStatusBtn.addEventListener('click', async function() {
                checkStatusBtn.disabled = true;
                serviceStatus.innerHTML = '<div class="loading">Checking service status</div>';
                serviceStatus.className = 'info';
                serviceStatus.classList.remove('hidden');

                try {
                    const response = await fetch('/pdf/status');
                    const result = await response.json();

                    if (response.ok) {
                        // Format the status information
                        let statusHtml = '<h3>Service Status</h3>';

                        // Vector store status
                        statusHtml += `<p><strong>Vector Store:</strong> ${result.vector_store_status}</p>`;

                        // OpenAI API key status
                        statusHtml += `<p><strong>OpenAI API Key:</strong> ${result.openai_api_key_status}</p>`;

                        // S3 status
                        statusHtml += '<div><strong>S3 Status:</strong> ';
                        if (result.s3_status.enabled) {
                            statusHtml += `<span style="color: green;">Enabled</span> (Bucket: ${result.s3_status.bucket}, Region: ${result.s3_status.region})</p>`;

                            // S3 files
                            if (result.s3_status.file_count > 0) {
                                statusHtml += '<details>';
                                statusHtml += `<summary>S3 Files (${result.s3_status.file_count})</summary>`;
                                statusHtml += '<ul>';
                                result.s3_status.files.forEach(file => {
                                    statusHtml += `<li>${file}</li>`;
                                });
                                statusHtml += '</ul>';
                                statusHtml += '</details>';
                            } else {
                                statusHtml += '<p>No files in S3 bucket.</p>';
                            }
                        } else {
                            statusHtml += '<span style="color: orange;">Disabled</span></p>';
                        }
                        statusHtml += '</div>';

                        // Local storage
                        statusHtml += '<div><strong>Local Storage:</strong>';

                        // FAISS directory
                        statusHtml += `<p>FAISS Index: ${result.faiss_directory.exists ? 'Exists' : 'Does not exist'} (${result.faiss_directory.file_count} files)</p>`;
                        if (result.faiss_directory.file_count > 0) {
                            statusHtml += '<details>';
                            statusHtml += `<summary>FAISS Files (${result.faiss_directory.file_count})</summary>`;
                            statusHtml += '<ul>';
                            result.faiss_directory.files.forEach(file => {
                                statusHtml += `<li>${file}</li>`;
                            });
                            statusHtml += '</ul>';
                            statusHtml += '</details>';
                        }

                        // Temp directory
                        statusHtml += `<p>Temp Directory: ${result.temp_directory.exists ? 'Exists' : 'Does not exist'} (${result.temp_directory.file_count} files)</p>`;
                        if (result.temp_directory.file_count > 0) {
                            statusHtml += '<details>';
                            statusHtml += `<summary>Temp Files (${result.temp_directory.file_count})</summary>`;
                            statusHtml += '<ul>';
                            result.temp_directory.files.forEach(file => {
                                statusHtml += `<li>${file}</li>`;
                            });
                            statusHtml += '</ul>';
                            statusHtml += '</details>';
                        }
                        statusHtml += '</div>';

                        // Timestamp
                        statusHtml += `<p><em>Last updated: ${new Date(result.timestamp).toLocaleString()}</em></p>`;

                        serviceStatus.innerHTML = statusHtml;
                        serviceStatus.className = 'success';
                    } else {
                        serviceStatus.textContent = `Error: ${result.detail || 'Unknown error'}`;
                        serviceStatus.className = 'error';
                    }
                } catch (error) {
                    serviceStatus.textContent = `Error: ${error.message}`;
                    serviceStatus.className = 'error';
                } finally {
                    checkStatusBtn.disabled = false;
                }
            });

            // Upload form submission
            uploadForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = new FormData(uploadForm);
                const uploadBtn = document.getElementById('uploadBtn');

                // Validate file
                const fileInput = document.getElementById('pdfFile');
                if (!fileInput.files[0]) {
                    showStatus('Please select a PDF file', 'error');
                    return;
                }

                if (!fileInput.files[0].name.toLowerCase().endsWith('.pdf')) {
                    showStatus('File must be a PDF', 'error');
                    return;
                }

                // Disable button and show loading
                uploadBtn.disabled = true;
                showStatus('Uploading and processing PDF...', 'info');

                try {
                    const response = await fetch('/pdf/upload', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (response.ok) {
                        showStatus('PDF uploaded successfully and processing started', 'success');

                        // Show processing details
                        document.getElementById('pdfId').textContent = result.pdf_id;
                        document.getElementById('numPages').textContent = result.num_pages || 'Processing...';
                        document.getElementById('processingTime').textContent = result.processing_time || 'Processing...';
                        document.getElementById('textSample').textContent = result.extracted_text_sample || 'Processing...';

                        // Show S3 URL if available
                        const s3UrlContainer = document.getElementById('s3UrlContainer');
                        const s3UrlLink = document.getElementById('s3Url');
                        if (result.s3_url) {
                            s3UrlLink.href = result.s3_url;
                            s3UrlLink.textContent = result.s3_url;
                            s3UrlContainer.style.display = 'block';
                        } else {
                            s3UrlContainer.style.display = 'none';
                        }

                        processingDetails.classList.remove('hidden');

                        // Reset form
                        uploadForm.reset();
                    } else {
                        showStatus(`Error: ${result.detail || 'Unknown error'}`, 'error');
                    }
                } catch (error) {
                    showStatus(`Error: ${error.message}`, 'error');
                } finally {
                    uploadBtn.disabled = false;
                }
            });

            // Search functionality
            searchBtn.addEventListener('click', async function() {
                const query = searchQuery.value.trim();

                if (!query) {
                    showSearchStatus('Please enter a search query', 'error');
                    return;
                }

                searchBtn.disabled = true;
                showSearchStatus('Searching...', 'info');
                searchResults.innerHTML = '<div class="loading">Searching</div>';

                try {
                    const response = await fetch(`/pdf/search?query=${encodeURIComponent(query)}`);
                    const result = await response.json();

                    if (response.ok) {
                        showSearchStatus(`Found ${result.total_results} results for "${result.query}"`, 'success');
                        displaySearchResults(result.results);
                    } else {
                        showSearchStatus(`Error: ${result.detail || 'Unknown error'}`, 'error');
                        searchResults.innerHTML = '';
                    }
                } catch (error) {
                    showSearchStatus(`Error: ${error.message}`, 'error');
                    searchResults.innerHTML = '';
                } finally {
                    searchBtn.disabled = false;
                }
            });

            // Helper functions
            function showStatus(message, type) {
                statusDiv.textContent = message;
                statusDiv.className = type;
                statusDiv.classList.remove('hidden');
            }

            function showSearchStatus(message, type) {
                searchStatus.textContent = message;
                searchStatus.className = type;
                searchStatus.classList.remove('hidden');
            }

            function displaySearchResults(results) {
                searchResults.innerHTML = '';

                if (results.length === 0) {
                    searchResults.innerHTML = '<p>No results found.</p>';
                    return;
                }

                results.forEach(result => {
                    const resultItem = document.createElement('div');
                    resultItem.className = 'result-item';

                    const content = document.createElement('div');
                    content.className = 'result-content';
                    content.textContent = result.content;

                    const metadata = document.createElement('div');
                    metadata.className = 'result-metadata';

                    // Format metadata
                    const metaItems = [];
                    if (result.metadata.title) metaItems.push(`<strong>Title:</strong> ${result.metadata.title}`);
                    if (result.metadata.pdf_id) metaItems.push(`<strong>PDF ID:</strong> ${result.metadata.pdf_id}`);
                    if (result.metadata.processed_at) {
                        const date = new Date(result.metadata.processed_at).toLocaleString();
                        metaItems.push(`<strong>Processed:</strong> ${date}`);
                    }

                    metadata.innerHTML = metaItems.join(' | ');

                    resultItem.appendChild(content);
                    resultItem.appendChild(metadata);
                    searchResults.appendChild(resultItem);
                });
            }
        });
    </script>
</body>
</html>
