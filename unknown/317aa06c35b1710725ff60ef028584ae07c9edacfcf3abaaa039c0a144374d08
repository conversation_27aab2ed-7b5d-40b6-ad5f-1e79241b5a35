import uuid
import os
import sys
from datetime import datetime, date, time, timedelta
import random
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Import database connection
from database.database import SessionLocal, engine

def generate_uuid():
    """Generate a random UUID."""
    return str(uuid.uuid4())

def create_sample_data():
    """Create sample data for testing using direct SQL to avoid model mismatches."""
    db = SessionLocal()
    
    try:
        # Create a user
        user_id = generate_uuid()
        db.execute(
            text("""
            INSERT INTO users (user_id, title, first_name, last_name, gender, email, password_hash, roles, is_active, created_at, updated_at)
            VALUES (:user_id, :title, :first_name, :last_name, :gender, :email, :password_hash, :roles, :is_active, :created_at, :updated_at)
            """),
            {
                "user_id": user_id,
                "title": "Mr.",
                "first_name": "<PERSON>",
                "last_name": "Doe",
                "gender": "M",
                "email": "<EMAIL>",
                "password_hash": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "password"
                "roles": "patient",
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
        )
        
        # Create a patient
        patient_id = generate_uuid()
        db.execute(
            text("""
            INSERT INTO patients (
                patient_id, user_id, title, first_name, last_name, dob, age, gender, 
                language, religion, address, phone, health_score, under_medications, 
                created_at, updated_at, interests, treatment
            )
            VALUES (
                :patient_id, :user_id, :title, :first_name, :last_name, :dob, :age, :gender,
                :language, :religion, :address, :phone, :health_score, :under_medications,
                :created_at, :updated_at, :interests, :treatment
            )
            """),
            {
                "patient_id": patient_id,
                "user_id": user_id,
                "title": "Mr.",
                "first_name": "John",
                "last_name": "Doe",
                "dob": date(1980, 1, 1),
                "age": 43,
                "gender": "M",
                "language": "English",
                "religion": "N/A",
                "address": "123 Main St",
                "phone": "******-123-4567",
                "health_score": 85,
                "under_medications": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "interests": '{"hobbies": ["Reading", "Hiking", "Music"]}',
                "treatment": '{"therapy": "Cognitive Behavioral Therapy"}'
            }
        )
        
        # Create a doctor user
        doctor_user_id = generate_uuid()
        db.execute(
            text("""
            INSERT INTO users (user_id, title, first_name, last_name, gender, email, password_hash, roles, is_active, created_at, updated_at)
            VALUES (:user_id, :title, :first_name, :last_name, :gender, :email, :password_hash, :roles, :is_active, :created_at, :updated_at)
            """),
            {
                "user_id": doctor_user_id,
                "title": "Dr.",
                "first_name": "Jane",
                "last_name": "Smith",
                "gender": "F",
                "email": "<EMAIL>",
                "password_hash": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "password"
                "roles": "doctor",
                "is_active": True,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
        )
        
        # Create a doctor
        doctor_id = generate_uuid()
        db.execute(
            text("""
            INSERT INTO doctors (
                doctor_id, user_id, title, first_name, middle_name, last_name, dob, age, gender,
                language, religion, address, phone, email, interests, specialization, consultation_fee,
                treatment, health_score, under_medications, created_at, updated_at
            )
            VALUES (
                :doctor_id, :user_id, :title, :first_name, :middle_name, :last_name, :dob, :age, :gender,
                :language, :religion, :address, :phone, :email, :interests, :specialization, :consultation_fee,
                :treatment, :health_score, :under_medications, :created_at, :updated_at
            )
            """),
            {
                "doctor_id": doctor_id,
                "user_id": doctor_user_id,
                "title": "Dr.",
                "first_name": "Jane",
                "middle_name": None,
                "last_name": "Smith",
                "dob": date(1975, 5, 15),
                "age": 48,
                "gender": "F",
                "language": "English",
                "religion": "N/A",
                "address": "456 Medical Ave",
                "phone": "******-987-6543",
                "email": "<EMAIL>",
                "interests": "Mental health, Research",
                "specialization": "Psychiatry",
                "consultation_fee": 200.00,
                "treatment": "Cognitive Behavioral Therapy, Medication Management",
                "health_score": 90,
                "under_medications": False,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
        )
        
        # Create doctor availability
        for day in ["Monday", "Wednesday", "Friday"]:
            availability_id = generate_uuid()
            db.execute(
                text("""
                INSERT INTO doctors_availability (availability_id, doctor_id, day_of_week, start_time, end_time)
                VALUES (:availability_id, :doctor_id, :day_of_week, :start_time, :end_time)
                """),
                {
                    "availability_id": availability_id,
                    "doctor_id": doctor_id,
                    "day_of_week": day,
                    "start_time": time(9, 0),
                    "end_time": time(17, 0)
                }
            )
        
        # Create an appointment
        appointment_id = generate_uuid()
        db.execute(
            text("""
            INSERT INTO appointments (
                appointment_id, patient_id, doctor_id, appointment_date, appointment_time,
                visit_reason, consultation_type, status, notes, created_at, updated_at
            )
            VALUES (
                :appointment_id, :patient_id, :doctor_id, :appointment_date, :appointment_time,
                :visit_reason, :consultation_type, :status, :notes, :created_at, :updated_at
            )
            """),
            {
                "appointment_id": appointment_id,
                "patient_id": patient_id,
                "doctor_id": doctor_id,
                "appointment_date": date.today() + timedelta(days=7),
                "appointment_time": time(10, 30),
                "visit_reason": "Follow-up consultation",
                "consultation_type": "Virtual",
                "status": "Scheduled",
                "notes": "Regular follow-up appointment",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
        )
        
        # Create a prescription
        prescription_id = generate_uuid()
        db.execute(
            text("""
            INSERT INTO prescriptions (
                prescription_id, patient_id, doctor_id, medication_name, dosage,
                instructions, start_date, end_date, status, created_at, updated_at
            )
            VALUES (
                :prescription_id, :patient_id, :doctor_id, :medication_name, :dosage,
                :instructions, :start_date, :end_date, :status, :created_at, :updated_at
            )
            """),
            {
                "prescription_id": prescription_id,
                "patient_id": patient_id,
                "doctor_id": doctor_id,
                "medication_name": "Sertraline",
                "dosage": "50mg",
                "instructions": "Take once daily with food",
                "start_date": date.today() - timedelta(days=30),
                "end_date": date.today() + timedelta(days=60),
                "status": "Active",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
        )
        
        # Create medical history
        history_id = generate_uuid()
        db.execute(
            text("""
            INSERT INTO medicalhistory (
                history_id, patient_id, diagnosis, treatment, diagnosed_date,
                doctor_id, additional_notes, created_at, updated_at
            )
            VALUES (
                :history_id, :patient_id, :diagnosis, :treatment, :diagnosed_date,
                :doctor_id, :additional_notes, :created_at, :updated_at
            )
            """),
            {
                "history_id": history_id,
                "patient_id": patient_id,
                "diagnosis": "Major Depressive Disorder",
                "treatment": "Cognitive Behavioral Therapy and medication",
                "diagnosed_date": date.today() - timedelta(days=180),
                "doctor_id": doctor_id,
                "additional_notes": "Patient responding well to treatment",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
        )
        
        # Create chat messages
        ai_doctor_id = "00000000-0000-0000-0000-000000000000"  # AI Doctor ID
        
        # Patient message
        chat_message_id1 = generate_uuid()
        db.execute(
            text("""
            INSERT INTO chat_messages (
                chat_message_id, sender_id, receiver_id, message_text, extracted_keywords, timestamp
            )
            VALUES (
                :chat_message_id, :sender_id, :receiver_id, :message_text, :extracted_keywords, :timestamp
            )
            """),
            {
                "chat_message_id": chat_message_id1,
                "sender_id": str(patient_id),
                "receiver_id": ai_doctor_id,
                "message_text": "I've been feeling better since starting the new medication.",
                "extracted_keywords": "medication,feeling,better",
                "timestamp": datetime.now() - timedelta(days=5)
            }
        )
        
        # AI Doctor response
        chat_message_id2 = generate_uuid()
        db.execute(
            text("""
            INSERT INTO chat_messages (
                chat_message_id, sender_id, receiver_id, message_text, extracted_keywords, timestamp
            )
            VALUES (
                :chat_message_id, :sender_id, :receiver_id, :message_text, :extracted_keywords, :timestamp
            )
            """),
            {
                "chat_message_id": chat_message_id2,
                "sender_id": ai_doctor_id,
                "receiver_id": str(patient_id),
                "message_text": "That's great to hear! Have you experienced any side effects?",
                "extracted_keywords": "side effects,medication",
                "timestamp": datetime.now() - timedelta(days=5, minutes=5)
            }
        )
        
        # Create emotion analysis
        emotion_id = generate_uuid()
        db.execute(
            text("""
            INSERT INTO emotion_analysis (
                emotion_id, chat_message_id, patient_id, emotion_category, confidence_score, analyzed_at
            )
            VALUES (
                :emotion_id, :chat_message_id, :patient_id, :emotion_category, :confidence_score, :analyzed_at
            )
            """),
            {
                "emotion_id": emotion_id,
                "chat_message_id": chat_message_id1,
                "patient_id": str(patient_id),
                "emotion_category": "happy",
                "confidence_score": 0.85,
                "analyzed_at": datetime.now() - timedelta(days=5, minutes=1)
            }
        )
        
        # Create diary entry
        diary_id = generate_uuid()
        db.execute(
            text("""
            INSERT INTO diary_entries (
                event_id, patient_id, notes, created_at
            )
            VALUES (
                :event_id, :patient_id, :notes, :created_at
            )
            """),
            {
                "event_id": diary_id,
                "patient_id": str(patient_id),
                "notes": "Today was a good day. I felt more energetic and positive.",
                "created_at": datetime.now() - timedelta(days=3)
            }
        )
        
        # Create onboarding questions
        for i, question_text in enumerate([
            "How would you rate your overall mental health?",
            "Have you ever been diagnosed with a mental health condition?",
            "Are you currently taking any medications for mental health?"
        ]):
            question_id = generate_uuid()
            db.execute(
                text("""
                INSERT INTO onboarding_questions (
                    question_id, patient_id, question, answer, category, timestamp
                )
                VALUES (
                    :question_id, :patient_id, :question, :answer, :category, :timestamp
                )
                """),
                {
                    "question_id": question_id,
                    "patient_id": str(patient_id),
                    "question": question_text,
                    "answer": ["Good", "Yes", "Yes"][i],
                    "category": ["general_health", "medical_history", "medications"][i],
                    "timestamp": datetime.now() - timedelta(days=60)
                }
            )
        
        # Commit all changes
        db.commit()
        
        # Print IDs for testing
        logger.info("\n=== SAMPLE IDs FOR TESTING ===")
        logger.info(f"Patient ID: {patient_id}")
        logger.info(f"Doctor ID: {doctor_id}")
        logger.info("==============================\n")
        
        return {
            "patient_id": patient_id,
            "doctor_id": doctor_id
        }
    
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating sample data: {str(e)}")
        raise
    
    finally:
        db.close()

def main():
    """Main function to set up the database."""
    try:
        # Create sample data
        ids = create_sample_data()
        
        logger.info("Database setup completed successfully!")
        return ids
        
    except Exception as e:
        logger.error(f"Error setting up database: {str(e)}")
        raise

if __name__ == "__main__":
    main()
