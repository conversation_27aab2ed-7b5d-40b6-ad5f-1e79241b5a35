import uuid
import os
import sys
from datetime import datetime, date, time, timedelta
import random
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Import models
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from model.model_correct import (
    Base, User, Patient, Doctor, Appointment, DoctorAvailability,
    Prescription, MedicalHistory, ChatMessage, EmotionAnalysis,
    DiaryEntry, OnboardingQuestion
)
from database.database import SessionLocal, engine

def create_tables():
    """Create all tables in the database."""
    logger.info("Creating tables...")
    Base.metadata.create_all(bind=engine)
    logger.info("Tables created successfully.")

def generate_uuid():
    """Generate a random UUID."""
    return str(uuid.uuid4())

def create_sample_data():
    """Create sample data for testing."""
    db = SessionLocal()
    
    try:
        # Create a user
        user_id = generate_uuid()
        user = User(
            user_id=user_id,
            title="Mr.",
            first_name="John",
            last_name="Doe",
            gender="M",
            email="<EMAIL>",
            password_hash="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "password"
            roles="patient",
            is_active=True,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(user)
        db.flush()
        
        # Create a patient
        patient_id = generate_uuid()
        patient = Patient(
            patient_id=patient_id,
            user_id=user.user_id,
            title="Mr.",
            first_name="John",
            middle_name=None,
            last_name="Doe",
            dob=date(1980, 1, 1),
            age=43,
            gender="M",
            language="English",
            religion="N/A",
            address="123 Main St",
            phone="******-123-4567",
            email="<EMAIL>",
            interests="Reading, Hiking, Music",
            treatment="Cognitive Behavioral Therapy",
            health_score=85,
            under_medications=True,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(patient)
        
        # Create a doctor user
        doctor_user_id = generate_uuid()
        doctor_user = User(
            user_id=doctor_user_id,
            title="Dr.",
            first_name="Jane",
            last_name="Smith",
            gender="F",
            email="<EMAIL>",
            password_hash="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "password"
            roles="doctor",
            is_active=True,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(doctor_user)
        db.flush()
        
        # Create a doctor
        doctor_id = generate_uuid()
        doctor = Doctor(
            doctor_id=doctor_id,
            user_id=doctor_user.user_id,
            title="Dr.",
            first_name="Jane",
            middle_name=None,
            last_name="Smith",
            dob=date(1975, 5, 15),
            age=48,
            gender="F",
            language="English",
            religion="N/A",
            address="456 Medical Ave",
            phone="******-987-6543",
            email="<EMAIL>",
            interests="Mental health, Research",
            specialization="Psychiatry",
            consultation_fee=200.00,
            treatment="Cognitive Behavioral Therapy, Medication Management",
            health_score=90,
            under_medications=False,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(doctor)
        
        # Create doctor availability
        for day in ["Monday", "Wednesday", "Friday"]:
            availability_id = generate_uuid()
            availability = DoctorAvailability(
                availability_id=availability_id,
                doctor_id=doctor.doctor_id,
                day_of_week=day,
                start_time=time(9, 0),
                end_time=time(17, 0)
            )
            db.add(availability)
        
        # Create an appointment
        appointment_id = generate_uuid()
        appointment = Appointment(
            appointment_id=appointment_id,
            patient_id=patient.patient_id,
            doctor_id=doctor.doctor_id,
            appointment_date=date.today() + timedelta(days=7),
            appointment_time=time(10, 30),
            visit_reason="Follow-up consultation",
            consultation_type="Virtual",
            status="Scheduled",
            notes="Regular follow-up appointment",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(appointment)
        
        # Create a prescription
        prescription_id = generate_uuid()
        prescription = Prescription(
            prescription_id=prescription_id,
            patient_id=patient.patient_id,
            doctor_id=doctor.doctor_id,
            medication_name="Sertraline",
            dosage="50mg",
            instructions="Take once daily with food",
            start_date=date.today() - timedelta(days=30),
            end_date=date.today() + timedelta(days=60),
            status="Active",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(prescription)
        
        # Create medical history
        history_id = generate_uuid()
        medical_history = MedicalHistory(
            history_id=history_id,
            patient_id=patient.patient_id,
            diagnosis="Major Depressive Disorder",
            treatment="Cognitive Behavioral Therapy and medication",
            diagnosed_date=date.today() - timedelta(days=180),
            doctor_id=doctor.doctor_id,
            additional_notes="Patient responding well to treatment",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(medical_history)
        
        # Create chat messages
        ai_doctor_id = "00000000-0000-0000-0000-000000000000"  # AI Doctor ID
        
        # Patient message
        chat_message_id1 = generate_uuid()
        message1 = ChatMessage(
            chat_message_id=chat_message_id1,
            sender_id=str(patient.patient_id),
            receiver_id=ai_doctor_id,
            message_text="I've been feeling better since starting the new medication.",
            extracted_keywords="medication,feeling,better",
            timestamp=datetime.now() - timedelta(days=5)
        )
        db.add(message1)
        
        # AI Doctor response
        chat_message_id2 = generate_uuid()
        message2 = ChatMessage(
            chat_message_id=chat_message_id2,
            sender_id=ai_doctor_id,
            receiver_id=str(patient.patient_id),
            message_text="That's great to hear! Have you experienced any side effects?",
            extracted_keywords="side effects,medication",
            timestamp=datetime.now() - timedelta(days=5, minutes=5)
        )
        db.add(message2)
        
        # Create emotion analysis
        emotion_id = generate_uuid()
        emotion = EmotionAnalysis(
            emotion_id=emotion_id,
            chat_message_id=message1.chat_message_id,
            patient_id=str(patient.patient_id),
            emotion_category="happy",
            confidence_score=0.85,
            analyzed_at=datetime.now() - timedelta(days=5, minutes=1)
        )
        db.add(emotion)
        
        # Create diary entry
        diary_id = generate_uuid()
        diary = DiaryEntry(
            event_id=diary_id,
            patient_id=str(patient.patient_id),
            notes="Today was a good day. I felt more energetic and positive.",
            created_at=datetime.now() - timedelta(days=3)
        )
        db.add(diary)
        
        # Create onboarding questions
        for i, question_text in enumerate([
            "How would you rate your overall mental health?",
            "Have you ever been diagnosed with a mental health condition?",
            "Are you currently taking any medications for mental health?"
        ]):
            question_id = generate_uuid()
            question = OnboardingQuestion(
                question_id=question_id,
                patient_id=str(patient.patient_id),
                question=question_text,
                answer=["Good", "Yes", "Yes"][i],
                category=["general_health", "medical_history", "medications"][i],
                timestamp=datetime.now() - timedelta(days=60)
            )
            db.add(question)
        
        # Commit all changes
        db.commit()
        
        # Print IDs for testing
        logger.info("\n=== SAMPLE IDs FOR TESTING ===")
        logger.info(f"Patient ID: {patient.patient_id}")
        logger.info(f"Doctor ID: {doctor.doctor_id}")
        logger.info("==============================\n")
        
        return {
            "patient_id": patient.patient_id,
            "doctor_id": doctor.doctor_id
        }
    
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating sample data: {str(e)}")
        raise
    
    finally:
        db.close()

def main():
    """Main function to set up the database."""
    try:
        # Create tables
        create_tables()
        
        # Create sample data
        ids = create_sample_data()
        
        logger.info("Database setup completed successfully!")
        return ids
        
    except Exception as e:
        logger.error(f"Error setting up database: {str(e)}")
        raise

if __name__ == "__main__":
    main()
