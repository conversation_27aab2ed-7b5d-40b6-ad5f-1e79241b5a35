<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Treatment Journey</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        .header {
            background: linear-gradient(135deg, #4a6fdc 0%, #3a5bbf 100%);
            color: white;
            padding: 25px 0;
            text-align: center;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 15px;
        }
        .insights-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .insight-card {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 25px;
            transition: transform 0.3s ease;
            border-top: 5px solid transparent;
        }
        .insight-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }
        /* Color coding for different insight types */
        .insight-card.progress, .insight-card.treatment {
            border-top-color: #4a6fdc; /* Blue */
        }
        .insight-card.recommendation {
            border-top-color: #28a745; /* Green */
        }
        .insight-card.milestone, .insight-card.congratulations {
            border-top-color: #ffc107; /* Yellow */
        }
        .insight-card.reminder, .insight-card.medication, .insight-card.medication-management {
            border-top-color: #fd7e14; /* Orange */
        }
        .insight-card.encouragement, .insight-card.positive-emotions, .insight-card.emotions {
            border-top-color: #e83e8c; /* Pink */
        }
        .insight-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .insight-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #f0f4ff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 1.8rem;
            color: #4a6fdc;
            box-shadow: 0 3px 8px rgba(74, 111, 220, 0.2);
        }
        .insight-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin: 0;
        }
        .insight-description {
            color: #555;
            margin-bottom: 15px;
            line-height: 1.7;
        }
        .patient-info {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #4a6fdc;
        }
        .patient-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .patient-info-item {
            padding: 12px 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .patient-info-label {
            font-weight: 600;
            color: #4a6fdc;
            margin-bottom: 5px;
        }
        .badge-high {
            background-color: #dc3545;
            color: white;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-left: 10px;
            font-weight: 500;
        }
        .badge-medium {
            background-color: #fd7e14;
            color: white;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-left: 10px;
            font-weight: 500;
        }
        .badge-low {
            background-color: #28a745;
            color: white;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-left: 10px;
            font-weight: 500;
        }
        .insight-type {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 4px;
            display: flex;
            align-items: center;
        }
        .insight-type i {
            margin-right: 5px;
            font-size: 0.8rem;
        }
        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            text-align: center;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4a6fdc;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error-message {
            color: #dc3545;
            margin-top: 10px;
        }
        .action-steps {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #4a6fdc;
        }
        .action-steps h5 {
            font-size: 1.1rem;
            color: #4a6fdc;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .action-steps h5 i {
            margin-right: 10px;
        }
        .action-steps ul {
            padding-left: 25px;
            margin-bottom: 0;
        }
        .action-steps li {
            margin-bottom: 12px;
            position: relative;
            padding-left: 5px;
        }

        /* Special styling for the congratulations card */
        .insight-card.congratulations {
            border-top-color: #ffc107; /* Yellow */
            background-color: #fffbf0;
        }

        .insight-card.congratulations .insight-icon {
            background-color: #fff8e1;
            color: #ffc107;
        }

        /* Special styling for the treatment card */
        .insight-card.treatment {
            border-top-color: #4a6fdc; /* Blue */
            background-color: #f5f8ff;
        }

        .insight-card.treatment .insight-icon {
            background-color: #e6eeff;
            color: #4a6fdc;
        }
        .progress-section {
            margin-top: 20px;
        }
        .progress-title {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: #333;
        }
        .progress-bar-container {
            height: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
            margin-bottom: 5px;
            overflow: hidden;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4a6fdc 0%, #3a5bbf 100%);
            border-radius: 5px;
            transition: width 1s ease;
        }
        .progress-label {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: #6c757d;
        }
        .next-appointment {
            background-color: #f0f4ff;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            display: flex;
            align-items: center;
        }
        .next-appointment i {
            font-size: 1.5rem;
            color: #4a6fdc;
            margin-right: 15px;
        }
        .health-summary {
            display: flex;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        .health-metric {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 12px 15px;
            margin-right: 15px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            min-width: 120px;
        }
        .health-metric i {
            margin-right: 10px;
            color: #4a6fdc;
        }
        .health-metric-value {
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-heartbeat me-2"></i> Your Treatment Journey</h1>
        <p>Personalized insights to help you on your path to better health</p>
    </div>

    <div class="container">
        <div id="patientInfoContainer" class="patient-info">
            <!-- Patient info will be inserted here -->
            <div class="spinner"></div>
            <p class="text-center">Loading patient information...</p>
        </div>

        <div id="loadingIndicator" class="loading">
            <div class="spinner"></div>
            <h3>Generating your personalized insights...</h3>
            <p>We're analyzing your health data to provide you with meaningful insights.</p>
        </div>

        <div id="insightsContainer" class="insights-container"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get patient ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            let patientId = urlParams.get('patientId');

            // If no patient ID in URL, use the example patient ID
            if (!patientId) {
                patientId = 'f31a95c6-76ef-4bb2-936c-b258285682d9';
            }

            // Try different token sources in order of preference
            const token = localStorage.getItem('STATIC_JWT_TOKEN') ||
                          localStorage.getItem('token') ||
                          sessionStorage.getItem('token') ||
                          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.3oZ2Ubh5rLBdHvQHd5Qr9GJczA5MXcxaVx5H5xLwvZ4';

            // Fetch insights
            fetchInsights(patientId, token);
        });

        function fetchInsights(patientId, token) {
            console.log(`Fetching insights for patient ID: ${patientId}`);

            // Fetch insights
            fetch(`/treatment-insights/patient/${patientId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Hide loading indicator
                document.getElementById('loadingIndicator').style.display = 'none';

                // Display insights
                displayInsights(data);

                // Display patient info
                displayPatientInfo(data.patient || { patient_id: patientId });
            })
            .catch(error => {
                console.error('Error fetching insights:', error);
                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('insightsContainer').innerHTML = `
                    <div class="alert alert-danger w-100">
                        <h4>Error Loading Insights</h4>
                        <p>${error.message}</p>
                        <button class="btn btn-outline-danger mt-2" onclick="location.reload()">Try Again</button>
                    </div>
                `;
            });
        }

        function displayPatientInfo(patient) {
            const patientInfoContainer = document.getElementById('patientInfoContainer');

            // If we don't have patient data, show a placeholder
            if (!patient || Object.keys(patient).length === 0) {
                patientInfoContainer.innerHTML = `
                    <h3>Your Health Journey</h3>
                    <p>Welcome to your personalized treatment insights</p>
                `;
                return;
            }

            // Create patient info HTML
            let patientInfoHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h3>${patient.first_name ? `${patient.first_name} ${patient.last_name || ''}` : 'Your'} Health Journey</h3>
                        <p>${patient.age ? `Age: ${patient.age} • ` : ''}${patient.gender ? `Gender: ${patient.gender}` : 'Your personalized health insights'}</p>

                        <div class="health-summary" id="healthSummary">
                            <!-- Health metrics -->
            `;

            // Add health score if available
            if (patient.health_score !== undefined) {
                const healthScore = Math.round(parseFloat(patient.health_score) || 0);
                patientInfoHTML += `
                    <div class="health-metric">
                        <i class="fas fa-heartbeat"></i>
                        <div>
                            <div>Health Score</div>
                            <div class="health-metric-value">${healthScore}/100</div>
                        </div>
                    </div>
                `;
            }

            // Add more metrics if available
            if (patient.mood) {
                patientInfoHTML += `
                    <div class="health-metric">
                        <i class="fas fa-smile"></i>
                        <div>
                            <div>Mood</div>
                            <div class="health-metric-value">${patient.mood}</div>
                        </div>
                    </div>
                `;
            }

            if (patient.under_medications) {
                patientInfoHTML += `
                    <div class="health-metric">
                        <i class="fas fa-pills"></i>
                        <div>
                            <div>Medications</div>
                            <div class="health-metric-value">Active</div>
                        </div>
                    </div>
                `;
            }

            // Add sleep metric if available
            if (patient.sleep_hours) {
                patientInfoHTML += `
                    <div class="health-metric">
                        <i class="fas fa-moon"></i>
                        <div>
                            <div>Sleep</div>
                            <div class="health-metric-value">${patient.sleep_hours} hrs/day</div>
                        </div>
                    </div>
                `;
            }

            patientInfoHTML += `
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="progress-section" id="progressSection">
                            <div class="progress-title">Treatment Progress</div>
                            <div class="progress-bar-container">
                                <div class="progress-bar" style="width: ${patient.health_score || 75}%"></div>
                            </div>
                            <div class="progress-label">
                                <span>Started</span>
                                <span>${patient.health_score || 75}%</span>
                                <span>Goal</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            patientInfoContainer.innerHTML = patientInfoHTML;
        }

        function displayInsights(data) {
            const insightsContainer = document.getElementById('insightsContainer');
            const insights = data.insights || [];

            if (!insights || insights.length === 0) {
                insightsContainer.innerHTML = `
                    <div class="alert alert-info w-100">
                        <h4>No Insights Available</h4>
                        <p>We don't have any insights for you at the moment.</p>
                    </div>
                `;
                return;
            }

            // Clear container
            insightsContainer.innerHTML = '';

            // Add each insight
            insights.forEach(insight => {
                // Handle nested data structure if present
                const insightData = insight.data && insight.data.insight ? insight.data.insight : insight;

                // Determine icon based on insight type
                let iconClass = 'fas fa-info-circle';
                let insightType = insightData.type || insightData.category || 'general';

                switch (insightType.toLowerCase()) {
                    case 'progress':
                    case 'mental health assessment':
                    case 'treatment':
                        iconClass = 'fas fa-chart-line';
                        break;
                    case 'recommendation':
                    case 'treatment recommendation':
                    case 'therapy selection':
                        iconClass = 'fas fa-lightbulb';
                        break;
                    case 'milestone':
                    case 'congratulations':
                        iconClass = 'fas fa-flag-checkered';
                        break;
                    case 'reminder':
                    case 'medication management':
                    case 'medication':
                        iconClass = 'fas fa-pills';
                        break;
                    case 'encouragement':
                    case 'positive emotions':
                    case 'emotions':
                        iconClass = 'fas fa-heart';
                        break;
                    case 'risk assessment':
                        iconClass = 'fas fa-shield-alt';
                        break;
                    case 'clinical_insight':
                        iconClass = 'fas fa-stethoscope';
                        break;
                }

                // Determine priority badge
                let priorityBadge = '';
                const priority = insightData.priority || 0;

                if (priority >= 4) {
                    priorityBadge = '<span class="badge-high">High Priority</span>';
                } else if (priority >= 2) {
                    priorityBadge = '<span class="badge-medium">Medium Priority</span>';
                } else {
                    priorityBadge = '<span class="badge-low">Low Priority</span>';
                }

                // Create insight card
                const insightCard = document.createElement('div');

                // Ensure proper class name formatting for CSS matching
                let cardClass = insightType.toLowerCase().replace(/\s+/g, '-');

                // Special handling for specific insight types
                if (insightData.title && insightData.title.includes("Congratulations")) {
                    cardClass = "congratulations";
                    // Set icon class for congratulations
                    iconClass = "fas fa-award";
                } else if (insightData.title && insightData.title.includes("Treatment")) {
                    cardClass = "treatment";
                    // If it's "Congratulations with Treatment!"
                    if (insightData.title && insightData.title.includes("Congratulations with Treatment")) {
                        cardClass = "congratulations";
                        iconClass = "fas fa-award";
                    }
                } else if (insightData.title && insightData.title.includes("Medication")) {
                    cardClass = "medication";
                } else if (insightData.title && insightData.title.includes("Emotions")) {
                    cardClass = "emotions";
                }

                insightCard.className = `insight-card ${cardClass}`;

                // Format the display type name properly
                const displayType = insightData.type || insightData.category || 'General';
                const formattedType = displayType.charAt(0).toUpperCase() + displayType.slice(1).toLowerCase();

                insightCard.innerHTML = `
                    <div class="insight-header">
                        <div class="insight-icon">
                            <i class="${iconClass}"></i>
                        </div>
                        <div>
                            <h3 class="insight-title">${insightData.title || 'Insight'} ${priorityBadge}</h3>
                            <div class="insight-type"><i class="${iconClass}"></i> ${formattedType}</div>
                        </div>
                    </div>
                    <p class="insight-description">${insightData.description || ''}</p>
                `;

                // Add evidence and suggested action if available
                if (insightData.evidence || insightData.suggested_action) {
                    let evidenceActionHTML = `<div class="action-steps">`;

                    if (insightData.evidence) {
                        evidenceActionHTML += `
                            <div class="mb-3">
                                <h5><i class="fas fa-clipboard-check"></i> Evidence</h5>
                                <p>${insightData.evidence}</p>
                            </div>
                        `;
                    }

                    if (insightData.suggested_action) {
                        evidenceActionHTML += `
                            <div>
                                <h5><i class="fas fa-tasks"></i> Recommended Action</h5>
                                <p>${insightData.suggested_action}</p>
                            </div>
                        `;
                    }

                    evidenceActionHTML += `</div>`;
                    insightCard.innerHTML += evidenceActionHTML;
                }

                // Add action steps if available (for backward compatibility)
                if (insightData.action_steps && insightData.action_steps.length > 0) {
                    let actionStepsHTML = `
                        <div class="action-steps">
                            <h5><i class="fas fa-tasks"></i> Recommended Actions</h5>
                            <ul>
                    `;

                    insightData.action_steps.forEach(step => {
                        actionStepsHTML += `<li>${step}</li>`;
                    });

                    actionStepsHTML += `
                            </ul>
                        </div>
                    `;

                    insightCard.innerHTML += actionStepsHTML;
                }

                insightsContainer.appendChild(insightCard);
            });
        }
    </script>
</body>
</html>
