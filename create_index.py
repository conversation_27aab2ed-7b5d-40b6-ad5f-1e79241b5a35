import os
from datetime import datetime
from typing import Dict
import PyPDF2
from dotenv import load_dotenv

from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import FAISS

# Load environment variables
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
assert OPENAI_API_KEY, "Set your OPENAI_API_KEY in .env file."

# Paths and names
pdf_path = r"D:\Books\PraSha Sync.pdf"
index_name = "faiss"
index_dir = os.path.join("Data/local_faiss_indexes", index_name)
os.makedirs(index_dir, exist_ok=True)

# Step 1: Extract text from PDF
def extract_text(pdf_path: str) -> str:
    text = ""
    with open(pdf_path, 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        for page in reader.pages:
            text += page.extract_text() or ""
    return text

# Step 2: Chunk the text
def chunk_text(text: str):
    splitter = RecursiveCharacterTextSplitter(chunk_size=400, chunk_overlap=50)
    return splitter.split_text(text)

# Step 3: Create and save FAISS index
def create_faiss_index(chunks: list, metadata: Dict):
    embeddings = OpenAIEmbeddings(
        openai_api_key=OPENAI_API_KEY,
        model="text-embedding-3-small",
        dimensions=1536
    )
    vector_store = FAISS.from_texts(chunks, embeddings, metadatas=[metadata]*len(chunks))
    vector_store.save_local(index_dir)
    print(f"FAISS index saved to: {index_dir}")

# Run everything
if __name__ == "__main__":
    if not os.path.exists(pdf_path):
        print(f"PDF not found: {pdf_path}")
        exit(1)

    text = extract_text(pdf_path)
    if not text:
        print("No text found in PDF.")
        exit(1)

    chunks = chunk_text(text)
    metadata = {
        "source": pdf_path,
        "type": "PDF",
        "date": datetime.now().isoformat()
    }

    create_faiss_index(chunks, metadata)
    print("Index creation completed successfully.")
    print(f"Total chunks created: {len(chunks)}")