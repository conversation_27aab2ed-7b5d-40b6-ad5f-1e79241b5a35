"""
Emotion Middleware for Chat Integration
Captures and analyzes audio/image emotions during chat conversations
"""

import asyncio
import base64
import json
import logging
import os
import tempfile
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import requests

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EmotionMiddleware:
    """Middleware to handle emotion analysis during chat conversations."""
    
    def __init__(self):
        self.emotion_cache = {}  # Cache recent emotions per conversation
        self.emotion_threshold = 0.7  # Minimum confidence to include in LLM context
        self.cache_duration = 300  # 5 minutes cache duration
        
    async def analyze_audio_emotion(self, audio_base64: str, patient_id: str, conversation_id: str) -> Optional[Dict]:
        """Analyze audio emotion and return result if significant."""
        try:
            logger.info(f"🎤 Analyzing audio emotion for patient {patient_id}")
            
            # Call the emotion recognition API
            url = "http://localhost:8000/conversation-audio/analyze-conversation-audio"
            payload = {
                "patient_id": patient_id,
                "conversation_id": conversation_id,
                "audio_base64": audio_base64,
                "timestamp": datetime.now().isoformat()
            }
            
            response = requests.post(url, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                emotion_data = result.get("emotion_data", {})
                
                emotion = emotion_data.get("emotion", "neutral")
                confidence = emotion_data.get("confidence", 0.0)
                arousal = emotion_data.get("arousal", 0.5)
                valence = emotion_data.get("valence", 0.5)
                
                logger.info(f"🎯 Audio emotion detected: {emotion} (confidence: {confidence:.2f})")
                
                # Only return if confidence is above threshold and emotion is not neutral
                if confidence >= self.emotion_threshold and emotion != "neutral":
                    emotion_context = self._generate_audio_emotion_context(emotion, confidence, arousal, valence)
                    
                    # Cache the emotion
                    self._cache_emotion(conversation_id, "audio", emotion_context)
                    
                    return {
                        "type": "audio",
                        "emotion": emotion,
                        "confidence": confidence,
                        "arousal": arousal,
                        "valence": valence,
                        "context": emotion_context
                    }
                    
            return None
            
        except Exception as e:
            logger.error(f"❌ Audio emotion analysis failed: {str(e)}")
            return None
    
    async def analyze_image_emotion(self, image_base64: str, patient_id: str, conversation_id: str) -> Optional[Dict]:
        """Analyze image emotion and return result if significant."""
        try:
            logger.info(f"📷 Analyzing image emotion for patient {patient_id}")
            
            # Call the emotion recognition API
            url = "http://localhost:8000/conversation-image/analyze-conversation-image"
            payload = {
                "patient_id": patient_id,
                "conversation_id": conversation_id,
                "image_base64": image_base64,
                "timestamp": datetime.now().isoformat()
            }
            
            response = requests.post(url, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                emotion_data = result.get("emotion_data", {})
                
                emotion = emotion_data.get("emotion", "neutral")
                confidence = emotion_data.get("confidence", 0.0)
                
                logger.info(f"🎯 Image emotion detected: {emotion} (confidence: {confidence:.2f})")
                
                # Only return if confidence is above threshold and emotion is not neutral
                if confidence >= self.emotion_threshold and emotion != "neutral":
                    emotion_context = self._generate_image_emotion_context(emotion, confidence)
                    
                    # Cache the emotion
                    self._cache_emotion(conversation_id, "image", emotion_context)
                    
                    return {
                        "type": "image",
                        "emotion": emotion,
                        "confidence": confidence,
                        "context": emotion_context
                    }
                    
            return None
            
        except Exception as e:
            logger.error(f"❌ Image emotion analysis failed: {str(e)}")
            return None
    
    def get_emotion_context_for_llm(self, conversation_id: str) -> str:
        """Get recent emotion context to include in LLM prompt."""
        try:
            cached_emotions = self.emotion_cache.get(conversation_id, {})
            
            if not cached_emotions:
                return ""
            
            # Check if emotions are still fresh (within cache duration)
            current_time = datetime.now().timestamp()
            fresh_emotions = []
            
            for emotion_type, emotion_data in cached_emotions.items():
                if current_time - emotion_data["timestamp"] < self.cache_duration:
                    fresh_emotions.append(emotion_data["context"])
            
            if fresh_emotions:
                emotion_context = "\n".join(fresh_emotions)
                logger.info(f"🧠 Adding emotion context to LLM: {emotion_context}")
                return f"\n\nCURRENT PATIENT EMOTIONAL STATE:\n{emotion_context}\n"
            
            return ""
            
        except Exception as e:
            logger.error(f"❌ Error getting emotion context: {str(e)}")
            return ""
    
    def _generate_audio_emotion_context(self, emotion: str, confidence: float, arousal: float, valence: float) -> str:
        """Generate human-readable context for audio emotion."""
        
        # Map emotions to descriptive text
        emotion_descriptions = {
            "happy": "sounds cheerful and upbeat",
            "sad": "sounds melancholic and down",
            "angry": "sounds frustrated or irritated", 
            "fear": "sounds anxious or worried",
            "disgust": "sounds displeased or uncomfortable",
            "surprise": "sounds surprised or startled",
            "neutral": "sounds calm and neutral"
        }
        
        # Add arousal/valence context
        arousal_desc = "high energy" if arousal > 0.6 else "low energy" if arousal < 0.4 else "moderate energy"
        valence_desc = "positive mood" if valence > 0.6 else "negative mood" if valence < 0.4 else "neutral mood"
        
        base_desc = emotion_descriptions.get(emotion, f"sounds {emotion}")
        
        return f"Patient {base_desc} with {arousal_desc} and {valence_desc} (voice analysis confidence: {confidence:.0%})"
    
    def _generate_image_emotion_context(self, emotion: str, confidence: float) -> str:
        """Generate human-readable context for image emotion."""
        
        # Map emotions to descriptive text
        emotion_descriptions = {
            "happy": "looks happy and smiling",
            "sad": "looks sad or downcast",
            "angry": "looks angry or tense",
            "fear": "looks worried or anxious",
            "disgust": "looks displeased or uncomfortable",
            "surprise": "looks surprised",
            "neutral": "has a neutral expression"
        }
        
        base_desc = emotion_descriptions.get(emotion, f"looks {emotion}")
        
        return f"Patient {base_desc} (facial analysis confidence: {confidence:.0%})"
    
    def _cache_emotion(self, conversation_id: str, emotion_type: str, context: str):
        """Cache emotion data for later use."""
        if conversation_id not in self.emotion_cache:
            self.emotion_cache[conversation_id] = {}
        
        self.emotion_cache[conversation_id][emotion_type] = {
            "context": context,
            "timestamp": datetime.now().timestamp()
        }
        
        logger.info(f"💾 Cached {emotion_type} emotion for conversation {conversation_id}")
    
    def clear_emotion_cache(self, conversation_id: str):
        """Clear emotion cache for a conversation."""
        if conversation_id in self.emotion_cache:
            del self.emotion_cache[conversation_id]
            logger.info(f"🗑️ Cleared emotion cache for conversation {conversation_id}")

# Global emotion middleware instance
emotion_middleware = EmotionMiddleware()

async def process_audio_emotion_if_present(data: Dict, patient_id: str, conversation_id: str) -> Optional[Dict]:
    """Process audio emotion if audio data is present in the message."""
    if "audio_base64" in data and data["audio_base64"]:
        return await emotion_middleware.analyze_audio_emotion(
            data["audio_base64"], 
            patient_id, 
            conversation_id
        )
    return None

async def process_image_emotion_if_present(data: Dict, patient_id: str, conversation_id: str) -> Optional[Dict]:
    """Process image emotion if image data is present in the message."""
    if "image_base64" in data and data["image_base64"]:
        return await emotion_middleware.analyze_image_emotion(
            data["image_base64"], 
            patient_id, 
            conversation_id
        )
    return None

def get_emotion_context_for_prompt(conversation_id: str) -> str:
    """Get emotion context to add to LLM prompt."""
    return emotion_middleware.get_emotion_context_for_llm(conversation_id)

def clear_conversation_emotions(conversation_id: str):
    """Clear emotions for a conversation when it ends."""
    emotion_middleware.clear_emotion_cache(conversation_id)
