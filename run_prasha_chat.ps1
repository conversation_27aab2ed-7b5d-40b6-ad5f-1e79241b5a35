# PowerShell script to pull and run the Prasha Chat AI Docker image from ECR

# Set AWS region
$AWS_REGION = "us-east-1"
# $ECR_REPOSITORY = "463470954735.dkr.ecr.us-east-1.amazonaws.com/prasha-chat-final1:latest"

$ECR_REPOSITORY = "183295433240.dkr.ecr.us-east-1.amazonaws.com/prasha-chat-final:latest"
Write-Host "=== Prasha Chat AI Docker Runner ===" -ForegroundColor Cyan
Write-Host "This script will pull and run the Prasha Chat AI Docker image from ECR" -ForegroundColor Cyan

# Step 1: Authenticate with AWS ECR
Write-Host "`n[Step 1/4] Authenticating with AWS ECR..." -ForegroundColor Yellow
$loginCmd = aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin 463470954735.dkr.ecr.us-east-1.amazonaws.com

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to authenticate with AWS ECR. Please check your AWS credentials." -ForegroundColor Red
    exit 1
}
Write-Host "✅ Successfully authenticated with AWS ECR" -ForegroundColor Green

# Step 2: Pull the Docker image
Write-Host "`n[Step 2/4] Pulling Docker image: $ECR_REPOSITORY" -ForegroundColor Yellow
docker pull $ECR_REPOSITORY

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to pull Docker image. Please check your network connection and ECR permissions." -ForegroundColor Red
    exit 1
}
Write-Host "✅ Successfully pulled Docker image" -ForegroundColor Green

# Step 3: Check if container exists (running or stopped) and remove it
Write-Host "`n[Step 3/4] Checking for existing containers..." -ForegroundColor Yellow
$CONTAINER_ID = docker ps -a -q --filter name=prasha-chat-final
if ($CONTAINER_ID) {
    Write-Host "Found existing container with ID: $CONTAINER_ID. Removing it..." -ForegroundColor Yellow
    docker stop $CONTAINER_ID 2>$null
    docker rm $CONTAINER_ID
    Write-Host "✅ Removed existing container" -ForegroundColor Green
} else {
    Write-Host "No existing container found" -ForegroundColor Green
}

# Step 4: Run the Docker container with environment variables
Write-Host "`n[Step 4/4] Running Docker container..." -ForegroundColor Yellow

# Load environment variables from .env file if it exists
if (Test-Path .env) {
    Write-Host "Loading environment variables from .env file" -ForegroundColor Green
    Get-Content .env | ForEach-Object {
        if ($_ -match "^\s*([^#][^=]+)=(.*)$") {
            $key = $matches[1].Trim()
            $value = $matches[2].Trim()
            Set-Item -Path "env:$key" -Value $value
        }
    }
}

# Set default values for environment variables if not already set
if (-not $env:OPENAI_API_KEY) { $env:OPENAI_API_KEY = "***********************************************************************************************************************************************************************" }
if (-not $env:postgres_username) { $env:postgres_username = "postgres" }
if (-not $env:postgres_password) { $env:postgres_password = "Prashaind2025" }
if (-not $env:postgres_host) { $env:postgres_host = "authenctication.cmb684u0s8ql.us-east-1.rds.amazonaws.com" }
if (-not $env:postgres_port) { $env:postgres_port = "5432" }
if (-not $env:postgres_database) { $env:postgres_database = "postgres" }
if (-not $env:HF_API_KEY) { $env:HF_API_KEY = "*************************************" }
if (-not $env:HF_API_URL) { $env:HF_API_URL = "https://router.huggingface.co/hf-inference/models/SamLowe/roberta-base-go_emotions" }
if (-not $env:JWT_SECRET) { $env:JWT_SECRET = "e3ddffa7eb26539eb449c2f9fbd5bd0a566cf00bef73f37e015d826e0b602f0d" }
if (-not $env:JWT_ALGORITHM) { $env:JWT_ALGORITHM = "HS256" }
if (-not $env:STATIC_JWT_TOKEN) { $env:STATIC_JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.3oZ2Ubh5rLBdHvQHd5Qr9GJczA5MXcxaVx5H5xLwvZ4" }
if (-not $env:USE_S3) { $env:USE_S3 = "True" }
if (-not $env:PDF_BUCKET_NAME) { $env:PDF_BUCKET_NAME = "prasha-healthcare-pdf" }
if (-not $env:AWS_ACCESS_KEY_ID) { $env:AWS_ACCESS_KEY_ID = "********************" }
if (-not $env:AWS_SECRET_ACCESS_KEY) { $env:AWS_SECRET_ACCESS_KEY = "THEjwqZmsdhhAQpL9uSHfySvSGM8tZPaji6TqEvb" }
if (-not $env:AWS_REGION) { $env:AWS_REGION = "us-east-1" }
if (-not $env:AWS_DEFAULT_REGION) { $env:AWS_DEFAULT_REGION = "us-east-1" }

# Run the container
docker run -d `
    --name prasha-chat-final `
    -p 8001:8000 `
    -e OPENAI_API_KEY="$env:OPENAI_API_KEY" `
    -e postgres_username="$env:postgres_username" `
    -e postgres_password="$env:postgres_password" `
    -e postgres_host="$env:postgres_host" `
    -e postgres_port="$env:postgres_port" `
    -e postgres_database="$env:postgres_database" `
    -e HF_API_KEY="$env:HF_API_KEY" `
    -e HF_API_URL="$env:HF_API_URL" `
    -e JWT_SECRET="$env:JWT_SECRET" `
    -e JWT_ALGORITHM="$env:JWT_ALGORITHM" `
    -e STATIC_JWT_TOKEN="$env:STATIC_JWT_TOKEN" `
    -e USE_S3="$env:USE_S3" `
    -e PDF_BUCKET_NAME="$env:PDF_BUCKET_NAME" `
    -e AWS_ACCESS_KEY_ID="$env:AWS_ACCESS_KEY_ID" `
    -e AWS_SECRET_ACCESS_KEY="$env:AWS_SECRET_ACCESS_KEY" `
    -e AWS_REGION="$env:AWS_REGION" `
    -e AWS_DEFAULT_REGION="$env:AWS_DEFAULT_REGION" `
    $ECR_REPOSITORY

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to run Docker container. Please check the error message above." -ForegroundColor Red
    exit 1
}

$CONTAINER_ID = docker ps -q --filter name=prasha-chat-final
Write-Host "✅ Successfully started container with ID: $CONTAINER_ID" -ForegroundColor Green

# Get the container's IP address
$CONTAINER_IP = docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' $CONTAINER_ID
$HOST_IP = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias Ethernet*).IPAddress | Select-Object -First 1

Write-Host "`n=== Prasha Chat AI is now running ===" -ForegroundColor Cyan
Write-Host "Local URL: http://localhost:8001" -ForegroundColor Green
Write-Host "Network URL: http://$HOST_IP`:8001" -ForegroundColor Green
Write-Host "Container IP: $CONTAINER_IP" -ForegroundColor Green
Write-Host "`nTo view logs: docker logs -f $CONTAINER_ID" -ForegroundColor Yellow
Write-Host "To stop container: docker stop $CONTAINER_ID" -ForegroundColor Yellow
