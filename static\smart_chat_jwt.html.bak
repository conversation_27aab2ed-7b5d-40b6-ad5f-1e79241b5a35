<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Mental Health Chat with JWT</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .chat-container {
            max-width: 800px;
            margin: 30px auto;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .chat-header {
            background-color: #4a6fa5;
            color: white;
            padding: 15px;
            text-align: center;
        }
        .chat-messages {
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            background-color: white;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 18px;
            max-width: 75%;
            position: relative;
        }
        .user-message {
            background-color: #e9f0ff;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }
        .ai-message {
            background-color: #f0f0f0;
            margin-right: auto;
            border-bottom-left-radius: 5px;
        }
        .message-time {
            font-size: 0.7rem;
            color: #888;
            margin-top: 5px;
            text-align: right;
        }
        .chat-input {
            display: flex;
            padding: 15px;
            background-color: #f0f0f0;
        }
        .chat-input input {
            flex-grow: 1;
            padding: 10px 15px;
            border: none;
            border-radius: 20px;
            margin-right: 10px;
        }
        .chat-input button {
            border: none;
            border-radius: 20px;
            padding: 10px 15px;
            background-color: #4a6fa5;
            color: white;
            cursor: pointer;
        }
        .chat-input button:hover {
            background-color: #3a5a8f;
        }
        .keywords {
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
        }
        .keyword-tag {
            display: inline-block;
            background-color: #e1e8f0;
            padding: 2px 8px;
            border-radius: 12px;
            margin-right: 5px;
            margin-bottom: 5px;
            font-size: 0.75rem;
        }
        .login-container {
            max-width: 500px;
            margin: 100px auto;
            padding: 20px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .status-bar {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 5px;
            display: none;
        }
        .status-bar.success {
            background-color: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <div id="login-section" class="login-container">
        <h2 class="text-center mb-4">Smart Mental Health Chat with JWT</h2>
        <div id="status-bar" class="status-bar">
            Connection status will appear here
        </div>
        <div class="mb-3">
            <label for="patient-id" class="form-label">Enter Patient ID:</label>
            <input type="text" class="form-control" id="patient-id" value="a07b24f2-5eda-4dd7-b5c4-ddef1226b8a2" placeholder="e.g., a07b24f2-5eda-4dd7-b5c4-ddef1226b8a2">
            <small class="form-text text-muted">Using static JWT token for authentication</small>
        </div>
        <button id="login-button" class="btn btn-primary w-100">Start Chat</button>
    </div>

    <div id="chat-section" class="chat-container" style="display: none;">
        <div class="chat-header">
            <h2>Smart Mental Health Chat</h2>
            <p id="patient-name">Patient ID: Not set</p>
            <p id="connection-status">Connection: Not connected</p>
        </div>
        <div id="chat-messages" class="chat-messages"></div>
        <div class="chat-input">
            <input type="text" id="message-input" placeholder="Type your message here...">
            <button id="send-button">Send</button>
        </div>
    </div>

    <script>
        // Static JWT token for authentication - must match the STATIC_JWT_TOKEN in .env file
        const jwtToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.3oZ2Ubh5rLBdHvQHd5Qr9GJczA5MXcxaVx5H5xLwvZ4';
        
        let ws = null;
        let patientId = '';
        
        // DOM elements
        const statusBar = document.getElementById('status-bar');
        const loginSection = document.getElementById('login-section');
        const chatSection = document.getElementById('chat-section');
        const patientIdInput = document.getElementById('patient-id');
        const patientNameDisplay = document.getElementById('patient-name');
        const connectionStatusDisplay = document.getElementById('connection-status');
        const chatMessages = document.getElementById('chat-messages');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        
        // Event listeners
        document.getElementById('login-button').addEventListener('click', startChat);
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Functions
        function showStatus(message, isError = true) {
            statusBar.textContent = message;
            statusBar.style.display = 'block';
            
            if (isError) {
                statusBar.classList.remove('success');
            } else {
                statusBar.classList.add('success');
            }
        }
        
        function startChat() {
            patientId = patientIdInput.value.trim();
            
            if (!patientId) {
                showStatus('Please enter a patient ID');
                return;
            }
            
            // Update UI
            patientNameDisplay.textContent = `Patient ID: ${patientId}`;
            connectionStatusDisplay.textContent = 'Connection: Connecting...';
            
            // Connect to WebSocket
            connectWebSocket();
            
            // Show chat section
            loginSection.style.display = 'none';
            chatSection.style.display = 'block';
            
            // Add welcome message
            addMessage('Welcome to Smart Mental Health Chat. Connecting to server...', 'ai');
        }
        
        function connectWebSocket() {
            // Close existing connection if any
            if (ws) {
                ws.close();
            }
            
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/smart-chat/${patientId}?token=${jwtToken}`;
            
            console.log(`Connecting to WebSocket: ${wsUrl}`);
            
            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = () => {
                    console.log('WebSocket connection opened');
                    connectionStatusDisplay.textContent = 'Connection: Connected';
                    addMessage('Connected to chat server! How can I help you today?', 'ai');
                };
                
                ws.onmessage = (event) => {
                    console.log('Received message:', event.data);
                    try {
                        const data = JSON.parse(event.data);
                        addMessage(data.response, 'ai', data.extracted_keywords);
                    } catch (error) {
                        console.error('Error parsing message:', error);
                        addMessage('Error processing server response', 'ai');
                    }
                };
                
                ws.onclose = (event) => {
                    console.log(`WebSocket closed: Code ${event.code}, Reason: ${event.reason}`);
                    connectionStatusDisplay.textContent = `Connection: Disconnected (${event.code})`;
                    addMessage(`Disconnected from server (Code: ${event.code}). Please refresh the page to reconnect.`, 'ai');
                };
                
                ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    connectionStatusDisplay.textContent = 'Connection: Error';
                    addMessage('Error connecting to server. Please refresh the page and try again.', 'ai');
                };
            } catch (error) {
                console.error('Error creating WebSocket:', error);
                connectionStatusDisplay.textContent = 'Connection: Failed';
                addMessage('Failed to connect to server. Please refresh the page and try again.', 'ai');
            }
        }
        
        function sendMessage() {
            const message = messageInput.value.trim();
            
            if (!message || !ws || ws.readyState !== WebSocket.OPEN) {
                return;
            }
            
            // Add message to chat
            addMessage(message, 'user');
            
            // Send message to server
            ws.send(JSON.stringify({ text: message }));
            
            // Clear input
            messageInput.value = '';
        }
        
        function addMessage(message, sender, keywords = []) {
            const messageElement = document.createElement('div');
            messageElement.classList.add('message', sender === 'user' ? 'user-message' : 'ai-message');
            
            // Message text
            const messageText = document.createElement('div');
            messageText.textContent = message;
            messageElement.appendChild(messageText);
            
            // Keywords (for AI messages only)
            if (sender === 'ai' && keywords && keywords.length > 0) {
                const keywordsElement = document.createElement('div');
                keywordsElement.classList.add('keywords');
                
                keywords.forEach(keyword => {
                    const keywordTag = document.createElement('span');
                    keywordTag.classList.add('keyword-tag');
                    keywordTag.textContent = keyword;
                    keywordsElement.appendChild(keywordTag);
                });
                
                messageElement.appendChild(keywordsElement);
            }
            
            // Timestamp
            const timeElement = document.createElement('div');
            timeElement.classList.add('message-time');
            const now = new Date();
            timeElement.textContent = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            messageElement.appendChild(timeElement);
            
            chatMessages.appendChild(messageElement);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    </script>
</body>
</html>
