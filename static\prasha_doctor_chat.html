<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .chat-container {
            max-width: 800px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            scroll-behavior: smooth;
        }
        .message {
            margin-bottom: 1rem;
            max-width: 75%;
            position: relative;
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .user-message {
            margin-left: auto;
            background-color: #8b5cf6;
            color: white;
            border-radius: 0.75rem;
            border-bottom-right-radius: 0.25rem;
        }
        .ai-message {
            margin-right: auto;
            background-color: #fed7aa;
            color: black;
            border-radius: 0.75rem;
            border-bottom-left-radius: 0.25rem;
        }
        .message-time {
            font-size: 0.7rem;
            color: rgba(156, 163, 175, 1);
            margin-top: 0.25rem;
            text-align: right;
        }
        .typing-indicator {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 0.75rem;
            max-width: 75%;
            background-color: #fed7aa;
            margin-right: auto;
            border-bottom-left-radius: 0.25rem;
        }
        .typing-indicator span {
            height: 8px;
            width: 8px;
            background-color: #888;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
            animation: typing 1.4s infinite ease-in-out;
        }
        .typing-indicator span:nth-child(1) { animation-delay: 0s; }
        .typing-indicator span:nth-child(2) { animation-delay: 0.2s; }
        .typing-indicator span:nth-child(3) { animation-delay: 0.4s; }
        @keyframes typing {
            0% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
            100% { transform: translateY(0); }
        }
        .hidden {
            display: none;
        }
        .recording-indicator {
            color: #ef4444;
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        #mic-button {
            min-width: 40px;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="flex items-center justify-between p-4 border-b">
            <button id="back-button" class="text-gray-700">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h2 class="text-lg font-semibold">Prasha Doctor</h2>
            <div class="w-6"></div> <!-- Spacer to balance layout -->
        </div>

        <div id="chat-messages" class="chat-messages">
            <!-- Messages will be added here -->
            <div id="empty-state" class="flex justify-center items-center h-full">
                <h1 class="text-gray-500">Start a conversation with Prasha Doctor</h1>
            </div>
        </div>

        <div id="typing-indicator" class="typing-indicator hidden">
            <span></span>
            <span></span>
            <span></span>
        </div>

        <div class="flex items-center p-3 border-t bg-white">
            <input
                type="text"
                id="message-input"
                placeholder="Message..."
                class="flex-1 p-2 rounded-xl bg-gray-100"
            />
            <button id="mic-button" class="ml-2 text-gray-700 p-2 border border-gray-300 rounded-full">
                <i class="fas fa-microphone"></i>
                <span class="sr-only">Record audio</span>
            </button>
            <button
                id="send-button"
                class="ml-2 bg-purple-600 p-2 rounded-full text-white"
            >
                <i class="fas fa-arrow-circle-right"></i>
            </button>
        </div>
    </div>

    <script>
        // Static JWT token for authentication
        const jwtToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.3oZ2Ubh5rLBdHvQHd5Qr9GJczA5MXcxaVx5H5xLwvZ4';

        // Remote server URL
        const remoteServerUrl = 'http://*************:8000';

        // Patient ID - in a real app, this would come from authentication
        const patientId = 'f31a95c6-76ef-4bb2-936c-b258285682d9';

        // DOM elements
        const chatMessages = document.getElementById('chat-messages');
        const emptyState = document.getElementById('empty-state');
        const typingIndicator = document.getElementById('typing-indicator');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const micButton = document.getElementById('mic-button');
        const backButton = document.getElementById('back-button');

        // Variables
        let ws = null;
        let isRecording = false;
        let mediaRecorder;
        let audioChunks = [];

        // Event listeners
        document.addEventListener('DOMContentLoaded', initializeChat);
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        micButton.addEventListener('click', toggleRecording);
        backButton.addEventListener('click', () => {
            // In a real app, this would navigate back
            alert('Back button clicked. Would navigate back in a real app.');
        });

        // Initialize chat
        function initializeChat() {
            // Initially disable buttons until connection is established
            sendButton.disabled = true;
            micButton.disabled = true;

            // Add visual indication that buttons are disabled
            sendButton.classList.add('opacity-50');
            micButton.classList.add('opacity-50');

            connectWebSocket();
        }

        // Connect to WebSocket
        function connectWebSocket() {
            // Close existing connection if any
            if (ws) {
                ws.close();
            }

            try {
                // Create WebSocket URL with token as query parameter
                const protocol = remoteServerUrl.startsWith('https') ? 'wss:' : 'ws:';
                const host = remoteServerUrl.replace(/^https?:\/\//, '');
                const wsUrl = `${protocol}//${host}/smart-chat/${patientId}?token=${jwtToken}`;

                console.log(`Connecting to WebSocket: ${wsUrl}`);

                // Create WebSocket
                ws = new WebSocket(wsUrl);

                // WebSocket event handlers
                ws.onopen = () => {
                    console.log('WebSocket connection opened');

                    // Enable buttons
                    sendButton.disabled = false;
                    micButton.disabled = false;

                    // Remove visual indication that buttons are disabled
                    sendButton.classList.remove('opacity-50');
                    micButton.classList.remove('opacity-50');

                    // Add welcome message
                    addMessage('Connected to chat server! How can I help you today?', 'ai');
                };

                ws.onmessage = (event) => {
                    console.log('Received message:', event.data);
                    try {
                        const data = JSON.parse(event.data);

                        // Hide typing indicator
                        typingIndicator.classList.add('hidden');

                        if (data.transcription) {
                            // This is a transcription response
                            console.log('Received transcription:', data.transcription);

                            // Find the placeholder message and replace it with the transcription
                            const placeholderMessages = document.querySelectorAll('.user-message');
                            const placeholderMessage = Array.from(placeholderMessages).find(
                                msg => msg.querySelector('.message-text').textContent.includes('🎤 Recording...')
                            );

                            if (placeholderMessage) {
                                // Replace the placeholder text with the transcription
                                placeholderMessage.querySelector('.message-text').textContent = data.transcription;
                            } else {
                                // If we can't find the placeholder, add a new message
                                addMessage(data.transcription, 'user');
                            }
                        } else if (data.response) {
                            // This is a normal response
                            addMessage(data.response, 'ai');
                        } else if (data.error) {
                            // This is an error
                            console.error('Server error:', data.error);
                            alert(`Error: ${data.error}`);
                        }
                    } catch (error) {
                        console.error('Error parsing message:', error);
                    }
                };

                ws.onclose = (event) => {
                    console.log(`WebSocket closed: Code ${event.code}, Reason: ${event.reason}`);

                    // Disable buttons
                    sendButton.disabled = true;
                    micButton.disabled = true;

                    // Add visual indication that buttons are disabled
                    sendButton.classList.add('opacity-50');
                    micButton.classList.add('opacity-50');

                    // Show connection lost message
                    addMessage('Connection lost. Attempting to reconnect...', 'ai');

                    // Attempt to reconnect if not a normal closure
                    if (event.code !== 1000 && event.code !== 1001) {
                        setTimeout(connectWebSocket, 3000);
                    }
                };

                ws.onerror = (error) => {
                    console.error('WebSocket error:', error);

                    // Disable buttons
                    sendButton.disabled = true;
                    micButton.disabled = true;

                    // Add visual indication that buttons are disabled
                    sendButton.classList.add('opacity-50');
                    micButton.classList.add('opacity-50');

                    // Show error message
                    addMessage('Connection error. Please refresh the page.', 'ai');
                };
            } catch (error) {
                console.error('Error creating WebSocket:', error);
                alert(`Error connecting to server: ${error.message}`);
            }
        }

        // Send text message
        function sendMessage() {
            const message = messageInput.value.trim();

            if (!message) {
                return;
            }

            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('Not connected to server. Please wait or refresh the page.');
                return;
            }

            // Add message to chat
            addMessage(message, 'user');

            // Show typing indicator
            typingIndicator.classList.remove('hidden');

            // Send message to server
            try {
                ws.send(JSON.stringify({ text: message }));
                console.log('Message sent:', message);
            } catch (error) {
                console.error('Error sending message:', error);
                alert('Error sending message. Please try again.');
                typingIndicator.classList.add('hidden');
            }

            // Clear input
            messageInput.value = '';
        }

        // Add message to chat
        function addMessage(message, sender) {
            // Hide empty state if visible
            if (!emptyState.classList.contains('hidden')) {
                emptyState.classList.add('hidden');
            }

            const messageElement = document.createElement('div');
            messageElement.classList.add('message', 'p-3', sender === 'user' ? 'user-message' : 'ai-message');

            // Message text
            const messageText = document.createElement('div');
            messageText.classList.add('message-text');
            messageText.textContent = message;
            messageElement.appendChild(messageText);

            // Timestamp
            const timeElement = document.createElement('div');
            timeElement.classList.add('message-time');
            const now = new Date();
            timeElement.textContent = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            messageElement.appendChild(timeElement);

            chatMessages.appendChild(messageElement);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            return messageElement;
        }

        // Voice recording functions
        function toggleRecording() {
            if (isRecording) {
                stopRecording();
            } else {
                startRecording();
            }
        }

        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];

                mediaRecorder.ondataavailable = (event) => {
                    audioChunks.push(event.data);
                };

                mediaRecorder.onstop = sendAudio;

                mediaRecorder.start();
                isRecording = true;
                micButton.innerHTML = '<i class="fas fa-stop recording-indicator"></i>';

                // Add a placeholder message
                addMessage('🎤 Recording...', 'user');
            } catch (error) {
                console.error('Microphone error:', error);
                alert(`Microphone error: ${error.message}`);
            }
        }

        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                micButton.innerHTML = '<i class="fas fa-microphone"></i>';
            }
        }

        function sendAudio() {
            const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
            const reader = new FileReader();

            // Show typing indicator
            typingIndicator.classList.remove('hidden');

            reader.onload = () => {
                const base64Audio = reader.result.split(',')[1];

                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({ audio: base64Audio }));
                    console.log('Audio sent to server');
                } else {
                    console.error('WebSocket not connected');
                    alert('WebSocket not connected. Please refresh the page.');
                    typingIndicator.classList.add('hidden');
                }
            };

            reader.readAsDataURL(audioBlob);
        }
    </script>
</body>
</html>
