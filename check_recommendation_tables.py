from database.database import SessionLocal
from sqlalchemy import text

db = SessionLocal()

# Check Doctor table structure
print("=== Doctor Table Structure ===")
doctor_columns = db.execute(text("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'doctors' ORDER BY ordinal_position")).fetchall()
for col in doctor_columns:
    print(f"{col[0]}: {col[1]}")

# Check DoctorAvailability table structure
print("\n=== DoctorAvailability Table Structure ===")
avail_columns = db.execute(text("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'doctor_availability' ORDER BY ordinal_position")).fetchall()
for col in avail_columns:
    print(f"{col[0]}: {col[1]}")

# Check Rating table structure
print("\n=== Rating Table Structure ===")
rating_columns = db.execute(text("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'ratings' ORDER BY ordinal_position")).fetchall()
for col in rating_columns:
    print(f"{col[0]}: {col[1]}")

# Check if tables have data
print("\n=== Table Data Counts ===")
doctor_count = db.execute(text("SELECT COUNT(*) FROM doctors")).fetchone()[0]
print(f"Doctors: {doctor_count}")

avail_count = db.execute(text("SELECT COUNT(*) FROM doctor_availability")).fetchone()[0]
print(f"Doctor Availability: {avail_count}")

rating_count = db.execute(text("SELECT COUNT(*) FROM ratings")).fetchone()[0]
print(f"Ratings: {rating_count}")

db.close()
