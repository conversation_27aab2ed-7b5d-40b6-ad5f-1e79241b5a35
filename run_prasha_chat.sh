#!/bin/bash
# <PERSON><PERSON>t to pull and run the Prasha Chat AI Docker image from ECR

# Set AWS region
AWS_REGION="us-east-1"
ECR_REPOSITORY="463470954735.dkr.ecr.us-east-1.amazonaws.com/prasha-chat-ai1:latest"

echo "=== Prasha Chat AI Docker Runner ==="
echo "This script will pull and run the Prasha Chat AI Docker image from ECR"

# Step 1: Authenticate with AWS ECR
echo -e "\n[Step 1/4] Authenticating with AWS ECR..."
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin 463470954735.dkr.ecr.us-east-1.amazonaws.com

if [ $? -ne 0 ]; then
    echo "❌ Failed to authenticate with AWS ECR. Please check your AWS credentials."
    exit 1
fi
echo "✅ Successfully authenticated with AWS ECR"

# Step 2: Pull the Docker image
echo -e "\n[Step 2/4] Pulling Docker image: $ECR_REPOSITORY"
docker pull $ECR_REPOSITORY

if [ $? -ne 0 ]; then
    echo "❌ Failed to pull Docker image. Please check your network connection and ECR permissions."
    exit 1
fi
echo "✅ Successfully pulled Docker image"

# Step 3: Check if container exists (running or stopped) and remove it
echo -e "\n[Step 3/4] Checking for existing containers..."
CONTAINER_ID=$(docker ps -a -q --filter name=prasha-chat-ai)
if [ ! -z "$CONTAINER_ID" ]; then
    echo "Found existing container with ID: $CONTAINER_ID. Removing it..."
    docker stop $CONTAINER_ID 2>/dev/null || true
    docker rm $CONTAINER_ID
    echo "✅ Removed existing container"
else
    echo "No existing container found"
fi

# Step 4: Run the Docker container with environment variables
echo -e "\n[Step 4/4] Running Docker container..."

# Load environment variables from .env file if it exists
if [ -f .env ]; then
    echo "Loading environment variables from .env file"
    source .env
fi

# Run the container
docker run -d \
    --name prasha-chat-ai \
    -p 8001:8000 \
    -e OPENAI_API_KEY="${OPENAI_API_KEY:-***********************************************************************************************************************************************************************}" \
    -e postgres_username="${postgres_username:-postgres}" \
    -e postgres_password="${postgres_password:-Prashaind2025}" \
    -e postgres_host="${postgres_host:-authenticationdb.cyvysmg4w1i4.us-east-1.rds.amazonaws.com}" \
    -e postgres_port="${postgres_port:-5432}" \
    -e postgres_database="${postgres_database:-postgres}" \
    -e HF_API_KEY="${HF_API_KEY:-*************************************}" \
    -e HF_API_URL="${HF_API_URL:-https://router.huggingface.co/hf-inference/models/SamLowe/roberta-base-go_emotions}" \
    -e JWT_SECRET="${JWT_SECRET:-e3ddffa7eb26539eb449c2f9fbd5bd0a566cf00bef73f37e015d826e0b602f0d}" \
    -e JWT_ALGORITHM="${JWT_ALGORITHM:-HS256}" \
    -e STATIC_JWT_TOKEN="${STATIC_JWT_TOKEN:-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.3oZ2Ubh5rLBdHvQHd5Qr9GJczA5MXcxaVx5H5xLwvZ4}" \
    -e USE_S3="${USE_S3:-True}" \
    -e PDF_BUCKET_NAME="${PDF_BUCKET_NAME:-prasha-health-pdf}" \
    -e AWS_ACCESS_KEY_ID="${AWS_ACCESS_KEY_ID:-********************}" \
    -e AWS_SECRET_ACCESS_KEY="${AWS_SECRET_ACCESS_KEY:-VfdkOsD22gvPwSoaIMylHSpxWF1N5DJLrKH6cp4/}" \
    -e AWS_REGION="${AWS_REGION:-us-east-1}" \
    -e AWS_DEFAULT_REGION="${AWS_DEFAULT_REGION:-us-east-1}" \
    $ECR_REPOSITORY

if [ $? -ne 0 ]; then
    echo "❌ Failed to run Docker container. Please check the error message above."
    exit 1
fi

CONTAINER_ID=$(docker ps -q --filter name=prasha-chat-ai)
echo "✅ Successfully started container with ID: $CONTAINER_ID"

# Get the container's IP address
CONTAINER_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' $CONTAINER_ID)
HOST_IP=$(hostname -I | awk '{print $1}')

echo -e "\n=== Prasha Chat AI is now running ==="
echo "Local URL: http://localhost:8001"
echo "Network URL: http://$HOST_IP:8001"
echo "Container IP: $CONTAINER_IP"
echo -e "\nTo view logs: docker logs -f $CONTAINER_ID"
echo "To stop container: docker stop $CONTAINER_ID"
