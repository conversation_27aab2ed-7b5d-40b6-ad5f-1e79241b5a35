import os
import uuid
import base64
import logging
import numpy as np
import librosa
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from database.database import SessionLocal
from model.model_correct import AudioEmotion, ImageEmotion
import tempfile
import io

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
audio_emotion_router = APIRouter()

# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Audio emotion recognition using basic features
def analyze_audio_emotion(audio_data, sample_rate=22050):
    """
    Analyze emotion from audio data using basic audio features.
    Returns emotion, confidence, arousal, and valence.
    """
    try:
        # Extract basic audio features
        # 1. Zero Crossing Rate (related to speech/music distinction)
        zcr = librosa.feature.zero_crossing_rate(audio_data)[0]
        zcr_mean = np.mean(zcr)
        
        # 2. Spectral Centroid (brightness of sound)
        spectral_centroids = librosa.feature.spectral_centroid(y=audio_data, sr=sample_rate)[0]
        spectral_centroid_mean = np.mean(spectral_centroids)
        
        # 3. MFCCs (Mel-frequency cepstral coefficients)
        mfccs = librosa.feature.mfcc(y=audio_data, sr=sample_rate, n_mfcc=13)
        mfccs_mean = np.mean(mfccs, axis=1)
        
        # 4. RMS Energy (loudness)
        rms = librosa.feature.rms(y=audio_data)[0]
        rms_mean = np.mean(rms)
        
        # 5. Tempo
        tempo, _ = librosa.beat.beat_track(y=audio_data, sr=sample_rate)
        
        # Simple rule-based emotion classification
        # This is a basic implementation - in production, you'd use a trained ML model
        
        # Normalize features for decision making
        energy_level = rms_mean
        brightness = spectral_centroid_mean / 4000  # Normalize around 4kHz
        speech_like = zcr_mean
        
        # Basic emotion classification rules
        if energy_level > 0.1 and brightness > 0.8:
            if tempo > 120:
                emotion = "excited"
                arousal = 0.8
                valence = 0.7
            else:
                emotion = "happy"
                arousal = 0.6
                valence = 0.8
        elif energy_level < 0.05:
            if brightness < 0.4:
                emotion = "sad"
                arousal = 0.2
                valence = 0.2
            else:
                emotion = "calm"
                arousal = 0.3
                valence = 0.6
        elif speech_like > 0.1 and energy_level > 0.08:
            emotion = "angry"
            arousal = 0.9
            valence = 0.1
        else:
            emotion = "neutral"
            arousal = 0.5
            valence = 0.5
        
        # Calculate confidence based on feature consistency
        confidence = min(0.9, max(0.3, energy_level * 2 + brightness * 0.5))
        
        logger.info(f"Audio emotion analysis: {emotion} (confidence: {confidence:.2f}, arousal: {arousal:.2f}, valence: {valence:.2f})")
        
        return emotion, confidence, arousal, valence
        
    except Exception as e:
        logger.error(f"Error in audio emotion analysis: {str(e)}")
        # Return neutral emotion as fallback
        return "neutral", 0.5, 0.5, 0.5

@audio_emotion_router.post("/analyze-audio")
async def analyze_audio_emotion_endpoint(
    audio_file: UploadFile = File(...),
    session_id: str = Form(...),
    db: Session = Depends(get_db)
):
    """
    Analyze emotion from uploaded audio file.
    """
    try:
        # Validate file type
        if not audio_file.content_type.startswith('audio/'):
            raise HTTPException(status_code=400, detail="File must be an audio file")
        
        # Read audio file
        audio_content = await audio_file.read()
        
        # Save to temporary file for librosa processing
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
            temp_file.write(audio_content)
            temp_file_path = temp_file.name
        
        try:
            # Load audio with librosa
            audio_data, sample_rate = librosa.load(temp_file_path, sr=22050)
            
            # Analyze emotion
            emotion, confidence, arousal, valence = analyze_audio_emotion(audio_data, sample_rate)
            
            # Generate unique sample ID
            sample_id = str(uuid.uuid4())
            
            # Save to database
            audio_emotion = AudioEmotion(
                sample_id=sample_id,
                session_id=session_id,
                timestamp=datetime.now(),
                emotion=emotion,
                confidence=confidence,
                arousal=arousal,
                valence=valence
            )
            
            db.add(audio_emotion)
            db.commit()
            
            logger.info(f"Audio emotion saved: {sample_id} - {emotion}")
            
            return {
                "sample_id": sample_id,
                "session_id": session_id,
                "emotion": emotion,
                "confidence": confidence,
                "arousal": arousal,
                "valence": valence,
                "timestamp": audio_emotion.timestamp.isoformat(),
                "status": "success"
            }
            
        finally:
            # Clean up temporary file
            os.unlink(temp_file_path)
            
    except Exception as e:
        logger.error(f"Error processing audio emotion: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing audio: {str(e)}")

@audio_emotion_router.post("/analyze-audio-base64")
async def analyze_audio_base64_endpoint(
    audio_base64: str = Form(...),
    session_id: str = Form(...),
    db: Session = Depends(get_db)
):
    """
    Analyze emotion from base64 encoded audio data.
    """
    try:
        # Decode base64 audio
        audio_bytes = base64.b64decode(audio_base64)
        
        # Save to temporary file for librosa processing
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
            temp_file.write(audio_bytes)
            temp_file_path = temp_file.name
        
        try:
            # Load audio with librosa
            audio_data, sample_rate = librosa.load(temp_file_path, sr=22050)
            
            # Analyze emotion
            emotion, confidence, arousal, valence = analyze_audio_emotion(audio_data, sample_rate)
            
            # Generate unique sample ID
            sample_id = str(uuid.uuid4())
            
            # Save to database
            audio_emotion = AudioEmotion(
                sample_id=sample_id,
                session_id=session_id,
                timestamp=datetime.now(),
                emotion=emotion,
                confidence=confidence,
                arousal=arousal,
                valence=valence
            )
            
            db.add(audio_emotion)
            db.commit()
            
            logger.info(f"Audio emotion saved: {sample_id} - {emotion}")
            
            return {
                "sample_id": sample_id,
                "session_id": session_id,
                "emotion": emotion,
                "confidence": confidence,
                "arousal": arousal,
                "valence": valence,
                "timestamp": audio_emotion.timestamp.isoformat(),
                "status": "success"
            }
            
        finally:
            # Clean up temporary file
            os.unlink(temp_file_path)
            
    except Exception as e:
        logger.error(f"Error processing base64 audio emotion: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing audio: {str(e)}")

@audio_emotion_router.get("/session/{session_id}")
def get_session_emotions(session_id: str, db: Session = Depends(get_db)):
    """
    Get all audio emotions for a session.
    """
    try:
        emotions = db.query(AudioEmotion).filter(
            AudioEmotion.session_id == session_id
        ).order_by(AudioEmotion.timestamp.desc()).all()
        
        return {
            "session_id": session_id,
            "total_samples": len(emotions),
            "emotions": [
                {
                    "sample_id": emotion.sample_id,
                    "emotion": emotion.emotion,
                    "confidence": emotion.confidence,
                    "arousal": emotion.arousal,
                    "valence": emotion.valence,
                    "timestamp": emotion.timestamp.isoformat()
                }
                for emotion in emotions
            ]
        }
        
    except Exception as e:
        logger.error(f"Error retrieving session emotions: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving emotions")

@audio_emotion_router.get("/health")
def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "audio_emotion_recognition",
        "features": ["audio_analysis", "emotion_detection", "arousal_valence"]
    }
