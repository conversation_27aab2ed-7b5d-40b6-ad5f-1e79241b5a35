<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Optimized PDF Upload</title>
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #f5f5f5;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        h1, h2, h3, h4 {
            color: var(--dark-color);
            margin-bottom: 15px;
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: var(--primary-color);
        }

        p {
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        input[type="file"],
        input[type="text"],
        input[type="number"],
        select,
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }

        textarea {
            height: 100px;
            resize: vertical;
        }

        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #357ab8;
        }

        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        .hidden {
            display: none;
        }

        #status {
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }

        .status-info {
            background-color: #e3f2fd;
            border-left: 5px solid var(--info-color);
        }

        .status-success {
            background-color: #e8f5e9;
            border-left: 5px solid var(--success-color);
        }

        .status-error {
            background-color: #ffebee;
            border-left: 5px solid var(--danger-color);
        }

        .status-warning {
            background-color: #fff8e1;
            border-left: 5px solid var(--warning-color);
        }

        .radio-group {
            display: flex;
            gap: 20px;
        }

        .radio-group label {
            display: flex;
            align-items: center;
            font-weight: normal;
            cursor: pointer;
        }

        .radio-group input[type="radio"] {
            margin-right: 5px;
            width: auto;
        }

        #processingDetails {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .detail-item {
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .detail-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .detail-label {
            font-weight: 600;
            margin-right: 10px;
        }

        .index-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            margin-right: 10px;
        }

        .general-badge {
            background-color: #007bff;
        }

        .dietician-badge {
            background-color: #dc3545;
        }

        #serviceStatus {
            margin-top: 20px;
        }

        .status-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .status-table th, .status-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .status-table th {
            background-color: #f5f5f5;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-active {
            background-color: var(--success-color);
        }

        .status-inactive {
            background-color: var(--danger-color);
        }

        .refresh-btn {
            background-color: var(--info-color);
            margin-left: 10px;
        }

        .refresh-btn:hover {
            background-color: #138496;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .radio-group {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <h1>Optimized PDF Processing</h1>

    <div class="container" id="uploadContainer">
        <h2>Upload PDF for Optimized Index</h2>
        <p>Upload PDFs to create optimized HNSW indexes for general and dietician specialties. These indexes will be used by the AI chat system to provide relevant information to patients.</p>

        <form id="uploadForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="pdfFile">PDF File:</label>
                <input type="file" id="pdfFile" name="file" accept=".pdf" required>
            </div>

            <div class="form-group">
                <label for="indexType">Index Type:</label>
                <select id="indexType" name="index_type" required>
                    <option value="">-- Select Index Type --</option>
                    <option value="general_index">General OPD</option>
                    <option value="dietician_index">Dietician</option>
                </select>
                <small>Indexes will be stored in the S3 bucket structure: faiss_index/[index_type]/</small>
            </div>

            <div class="form-group">
                <label for="title">Title (optional):</label>
                <input type="text" id="title" name="title" placeholder="Enter a title for the PDF">
            </div>

            <div class="form-group">
                <label for="description">Description (optional):</label>
                <textarea id="description" name="description" placeholder="Enter a description for the PDF"></textarea>
            </div>

            <div class="form-group">
                <label for="chunkSize">Chunk Size (tokens):</label>
                <input type="number" id="chunkSize" name="chunk_size" value="500" min="100" max="1000">
            </div>

            <div class="form-group">
                <label for="chunkOverlap">Chunk Overlap (tokens):</label>
                <input type="number" id="chunkOverlap" name="chunk_overlap" value="50" min="0" max="200">
            </div>

            <button type="submit" id="uploadBtn" class="btn">Upload and Process PDF</button>
        </form>

        <div id="status" class="hidden"></div>
    </div>

    <div class="container" id="processingContainer">
        <h2>Processing Status</h2>
        <p>Check the status of a PDF processing job by entering the PDF ID below.</p>

        <div class="form-group">
            <label for="pdfId">PDF ID:</label>
            <input type="text" id="pdfId" placeholder="Enter PDF ID">
            <button id="checkStatusBtn" class="btn">Check Status</button>
        </div>

        <div id="processingDetails" class="hidden"></div>
    </div>

    <div class="container" id="indexStatusContainer">
        <h2>Index Status</h2>
        <p>Check the status of the optimized indexes.</p>

        <button id="checkIndexStatusBtn" class="btn">Check Index Status</button>

        <div id="serviceStatus" class="hidden"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const uploadForm = document.getElementById('uploadForm');
            const statusDiv = document.getElementById('status');
            const processingDetails = document.getElementById('processingDetails');
            const checkStatusBtn = document.getElementById('checkStatusBtn');
            const serviceStatus = document.getElementById('serviceStatus');
            const indexTypeSelect = document.getElementById('indexType');

            // Upload form submission
            uploadForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = new FormData(uploadForm);
                const uploadBtn = document.getElementById('uploadBtn');

                // Disable button and show loading
                uploadBtn.disabled = true;
                showStatus('Uploading PDF...', 'info');

                try {
                    const response = await fetch('/pdf/optimized/upload', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (response.ok) {
                        showStatus(`PDF uploaded successfully. Processing started with ID: ${result.pdf_id}`, 'success');

                        // Set the PDF ID in the check status form
                        document.getElementById('pdfId').value = result.pdf_id;

                        // Reset form
                        uploadForm.reset();

                        // Select the index type that was just used
                        indexTypeSelect.value = result.index_type;
                    } else {
                        const errorDetail = result.detail || 'Unknown error';
                        showStatus(`Error: ${errorDetail}`, 'error');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    showStatus(`Error: ${error.message}`, 'error');
                } finally {
                    uploadBtn.disabled = false;
                }
            });

            // Check status button
            checkStatusBtn.addEventListener('click', async function() {
                const pdfId = document.getElementById('pdfId').value.trim();

                if (!pdfId) {
                    showStatus('Please enter a PDF ID', 'error');
                    return;
                }

                try {
                    const response = await fetch(`/pdf/optimized/processing/${pdfId}`);

                    if (response.ok) {
                        const result = await response.json();
                        displayProcessingDetails(result);
                    } else {
                        const error = await response.json();
                        showStatus(`Error: ${error.detail || 'PDF not found'}`, 'error');
                        processingDetails.classList.add('hidden');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    showStatus(`Error: ${error.message}`, 'error');
                }
            });

            // Check index status button
            document.getElementById('checkIndexStatusBtn').addEventListener('click', async function() {
                try {
                    const response = await fetch('/pdf/optimized/index/status');

                    if (response.ok) {
                        const result = await response.json();
                        displayIndexStatus(result);
                    } else {
                        const error = await response.json();
                        showStatus(`Error: ${error.detail || 'Could not get index status'}`, 'error');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    showStatus(`Error: ${error.message}`, 'error');
                }
            });

            // Helper function to show status messages
            function showStatus(message, type) {
                statusDiv.textContent = message;
                statusDiv.className = '';
                statusDiv.classList.add(`status-${type}`);
                statusDiv.classList.remove('hidden');
            }

            // Helper function to display processing details
            function displayProcessingDetails(data) {
                processingDetails.innerHTML = '';
                processingDetails.classList.remove('hidden');

                const details = [
                    { label: 'PDF ID', value: data.pdf_id },
                    { label: 'Index Type', value: getIndexTypeBadge(data.index_type) },
                    { label: 'Status', value: data.status },
                    { label: 'Number of Pages', value: data.num_pages },
                    { label: 'Processing Time', value: `${data.processing_time.toFixed(2)} seconds` },
                    { label: 'S3 URL', value: data.s3_url ? `<a href="${data.s3_url}" target="_blank">${data.s3_url}</a>` : 'N/A' },
                    { label: 'Completed At', value: data.completed_at || 'N/A' },
                    { label: 'Error', value: data.error || 'None' },
                    { label: 'Text Sample', value: `<div style="max-height: 200px; overflow-y: auto; background-color: #f5f5f5; padding: 10px; border-radius: 4px;">${data.extracted_text_sample}</div>` }
                ];

                details.forEach(detail => {
                    const detailItem = document.createElement('div');
                    detailItem.className = 'detail-item';
                    detailItem.innerHTML = `<span class="detail-label">${detail.label}:</span> ${detail.value}`;
                    processingDetails.appendChild(detailItem);
                });
            }

            // Helper function to display index status
            function displayIndexStatus(data) {
                serviceStatus.innerHTML = '';
                serviceStatus.classList.remove('hidden');

                const table = document.createElement('table');
                table.className = 'status-table';

                // Create table header
                const thead = document.createElement('thead');
                thead.innerHTML = `
                    <tr>
                        <th>Index Type</th>
                        <th>Status</th>
                        <th>S3 Path</th>
                        <th>Document Count</th>
                        <th>PDF Count</th>
                        <th>Last Updated</th>
                    </tr>
                `;
                table.appendChild(thead);

                // Create table body
                const tbody = document.createElement('tbody');

                // Add row for general index
                tbody.innerHTML += createIndexStatusRow('General OPD', data.general_index);

                // Add row for dietician index
                tbody.innerHTML += createIndexStatusRow('Dietician', data.dietician_index);

                table.appendChild(tbody);
                serviceStatus.appendChild(table);

                // Add detailed information for each index
                for (const [indexType, status] of Object.entries(data)) {
                    if (indexType === 'timestamp') continue;

                    const indexName = indexType === 'general_index' ? 'General OPD' : 'Dietician';
                    const detailsDiv = document.createElement('div');
                    detailsDiv.style.marginTop = '20px';
                    detailsDiv.style.padding = '15px';
                    detailsDiv.style.backgroundColor = '#f8f9fa';
                    detailsDiv.style.borderRadius = '4px';
                    detailsDiv.style.border = '1px solid #ddd';

                    let detailsHTML = `<h3>${indexName} Details</h3>`;

                    // Add file sizes if available
                    if (status.faiss_size || status.pkl_size) {
                        detailsHTML += `<p><strong>File Sizes:</strong> `;
                        if (status.faiss_size) {
                            detailsHTML += `index.faiss: ${formatFileSize(status.faiss_size)} `;
                        }
                        if (status.pkl_size) {
                            detailsHTML += `index.pkl: ${formatFileSize(status.pkl_size)}`;
                        }
                        detailsHTML += `</p>`;
                    }

                    // Add PDF samples if available
                    if (status.pdf_samples && status.pdf_samples.length > 0) {
                        detailsHTML += `<p><strong>Sample PDFs:</strong></p>`;
                        detailsHTML += `<ul>`;
                        status.pdf_samples.forEach(pdf => {
                            detailsHTML += `<li>${pdf}</li>`;
                        });
                        detailsHTML += `</ul>`;
                    }

                    // Add directory info if index doesn't exist but directory does
                    if (!status.exists && status.directory_exists) {
                        detailsHTML += `<p><strong>Note:</strong> Directory exists but no index files found. ${status.object_count || 0} objects in directory.</p>`;
                    }

                    detailsDiv.innerHTML = detailsHTML;
                    serviceStatus.appendChild(detailsDiv);
                }

                // Add timestamp
                const timestamp = document.createElement('div');
                timestamp.style.marginTop = '10px';
                timestamp.style.fontSize = '14px';
                timestamp.style.color = '#666';
                timestamp.textContent = `Last checked: ${new Date(data.timestamp).toLocaleString()}`;
                serviceStatus.appendChild(timestamp);
            }

            // Helper function to create a row for index status
            function createIndexStatusRow(name, status) {
                const statusIndicator = status.exists ?
                    '<span class="status-indicator status-active"></span> Active' :
                    '<span class="status-indicator status-inactive"></span> Not Found';

                const s3Path = status.s3_path || 'N/A';
                const documentCount = status.document_count || 'N/A';
                const pdfCount = status.pdf_count || 'N/A';
                const lastUpdated = status.last_updated ? new Date(status.last_updated).toLocaleString() : 'N/A';

                return `
                    <tr>
                        <td>${name}</td>
                        <td>${statusIndicator}</td>
                        <td>${s3Path}</td>
                        <td>${documentCount}</td>
                        <td>${pdfCount}</td>
                        <td>${lastUpdated}</td>
                    </tr>
                `;
            }

            // Helper function to format file size
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';

                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));

                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            }

            // Helper function to get index type badge
            function getIndexTypeBadge(indexType) {
                const indexNames = {
                    'general_index': 'General OPD',
                    'dietician_index': 'Dietician'
                };

                const badgeClass = indexType === 'general_index' ? 'general-badge' : 'dietician-badge';
                return `<span class="index-badge ${badgeClass}">${indexNames[indexType] || indexType}</span>`;
            }
        });
    </script>
</body>
</html>
