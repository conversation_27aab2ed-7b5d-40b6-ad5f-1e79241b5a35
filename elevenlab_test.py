import asyncio
from elevenlabs.client import ElevenLabs

client = ElevenLabs(
    api_key="***************************************************",
)

async def generate_audio():
    audio = await client.audio.generate(  # ✅ use audio.generate
        text="Hello <PERSON><PERSON><PERSON>, this is <PERSON><PERSON>. How are you feeling today?",
        voice="<PERSON>",
        model="eleven_multilingual_v2"
    )
    with open("output.mp3", "wb") as f:
        f.write(audio)

asyncio.run(generate_audio())
