import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Print environment variables
print("Environment Variables:")
print(f"USE_S3: {os.getenv('USE_S3')}")
print(f"PDF_BUCKET_NAME: {os.getenv('PDF_BUCKET_NAME')}")
print(f"AWS_ACCESS_KEY_ID: {os.getenv('AWS_ACCESS_KEY_ID')}")
print(f"AWS_SECRET_ACCESS_KEY: {os.getenv('AWS_SECRET_ACCESS_KEY')}")
print(f"AWS_REGION: {os.getenv('AWS_REGION')}")

# Check if USE_S3 is True
use_s3 = os.getenv("USE_S3", "False").lower() == "true"
print(f"\nUSE_S3 (parsed): {use_s3}")
