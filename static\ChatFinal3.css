/* ChatFinal3.css - React Component Styles */

.chat-container {
  max-width: 1200px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.chat-header {
  background-color: #4a6fa5;
  color: white;
  padding: 15px 20px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-header h2 {
  margin: 0;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
}

.persona-indicator {
  font-size: 0.8rem;
  padding: 3px 8px;
  border-radius: 12px;
  margin-left: 10px;
  background-color: #e9ecef;
}

.persona-psychologist {
  background-color: #6f42c1;
  color: white;
}

.persona-dietician {
  background-color: #fd7e14;
  color: white;
}

.connection-status {
  font-weight: bold;
}

.connected {
  color: #28a745;
}

.disconnected {
  color: #dc3545;
}

.connecting {
  color: #ffc107;
}

.status-bar {
  padding: 5px 15px;
  background-color: #e9ecef;
  font-size: 0.8rem;
  color: #666;
  text-align: center;
  transition: opacity 0.5s ease-in-out;
}

.status-bar.fade-out {
  opacity: 0;
}

.chat-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  width: 30%;
  background-color: #f0f2f5;
  padding: 20px;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
}

.patient-info {
  flex: 1;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.patient-info h3 {
  margin-top: 0;
  font-size: 1.2rem;
  color: #4a6fa5;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 10px;
}

.info-section {
  margin-bottom: 15px;
}

.info-section h4 {
  font-size: 1rem;
  color: #495057;
  margin-bottom: 8px;
}

.info-item {
  margin-bottom: 8px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 5px;
  font-size: 0.9rem;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #fafafa;
}

.message {
  margin-bottom: 15px;
  padding: 12px 15px;
  border-radius: 15px;
  max-width: 80%;
  position: relative;
}

.user-message {
  background-color: #4a6fa5;
  color: white;
  margin-left: auto;
  border-bottom-right-radius: 5px;
}

.ai-message {
  background-color: #e9ecef;
  color: #333;
  margin-right: auto;
  border-bottom-left-radius: 5px;
}

.message-time {
  font-size: 0.7rem;
  opacity: 0.7;
  margin-top: 5px;
}

.message-keywords {
  margin-top: 8px;
  font-size: 0.8rem;
  opacity: 0.8;
}

.keyword-tag {
  display: inline-block;
  background-color: rgba(74, 111, 165, 0.1);
  color: #4a6fa5;
  padding: 2px 6px;
  border-radius: 10px;
  margin-right: 5px;
  font-size: 0.7rem;
}

.audio-controls {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.audio-btn {
  background: none;
  border: 1px solid #4a6fa5;
  color: #4a6fa5;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  transition: all 0.2s;
}

.audio-btn:hover {
  background-color: #4a6fa5;
  color: white;
}

.audio-btn.playing {
  background-color: #4a6fa5;
  color: white;
}

/* Streaming styles */
.streaming-message {
  border-left: 3px solid #007bff;
  background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
}

.streaming-text {
  min-height: 20px;
}

.streaming-indicator {
  color: #007bff;
  font-size: 0.9em;
  margin-top: 8px;
  font-style: italic;
}

.streaming-indicator i {
  margin-right: 5px;
}

/* Animation for streaming text */
@keyframes streamingPulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.streaming-message .streaming-text {
  animation: streamingPulse 1.5s ease-in-out infinite;
}

/* Remove animation when streaming is complete */
.message:not(.streaming-message) .streaming-text {
  animation: none;
}

.chat-input {
  padding: 15px 20px;
  background-color: #fff;
  border-top: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 10px;
}

.chat-input input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid #ced4da;
  border-radius: 25px;
  outline: none;
  font-size: 1rem;
}

.chat-input input:focus {
  border-color: #4a6fa5;
  box-shadow: 0 0 0 0.2rem rgba(74, 111, 165, 0.25);
}

.chat-input input:disabled {
  background-color: #f8f9fa;
  opacity: 0.6;
}

.voice-selection-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 5px;
}

.voice-select {
  width: auto;
  padding: 5px;
  border-radius: 20px;
  border: 1px solid #ced4da;
  background-color: #f8f9fa;
  color: #4a6fa5;
  font-size: 0.9rem;
  cursor: pointer;
  outline: none;
  transition: all 0.2s;
  margin-bottom: 2px;
}

.voice-helper-text {
  font-size: 0.7rem;
  color: #6c757d;
  text-align: center;
  white-space: nowrap;
  margin: 0;
  padding: 0;
}

.btn-send {
  background-color: #4a6fa5;
  color: white;
  border: none;
  border-radius: 50%;
  width: 45px;
  height: 45px;
  margin-left: 10px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-send:hover:not(:disabled) {
  background-color: #3a5a8f;
}

.btn-send:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.btn-mic {
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 50%;
  width: 45px;
  height: 45px;
  margin-left: 10px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-mic:hover:not(:disabled) {
  background-color: #5a6268;
}

.btn-mic:disabled {
  background-color: #adb5bd;
  cursor: not-allowed;
}

.btn-mic.recording {
  background-color: #dc3545;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
  100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

/* Utility classes */
.d-flex {
  display: flex;
}

.justify-content-center {
  justify-content: center;
}

.align-items-center {
  align-items: center;
}

.h-100 {
  height: 100%;
}

.me-2 {
  margin-right: 0.5rem;
}

.text-muted {
  color: #6c757d;
}

.small {
  font-size: 0.875em;
}

.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border 0.75s linear infinite;
}

.spinner-border.text-primary {
  color: #0d6efd;
}

@keyframes spinner-border {
  to { transform: rotate(360deg); }
}

.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}
