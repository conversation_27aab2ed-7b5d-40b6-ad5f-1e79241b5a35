import React, { useState } from 'react';
import ChatFinal3 from './ChatFinal3.jsx';

/**
 * Test component to demonstrate voice selection functionality
 * in the ChatFinal3 React component
 */
const ChatFinal3VoiceTest = () => {
  const [selectedPersona, setSelectedPersona] = useState('psychologist');
  const [testPatientId] = useState('f31a95c6-76ef-4bb2-936c-b258285682d9');
  const [testToken] = useState('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.Ij0BnOgMwKFwB7VOzRdL-qdpEXNF7YvZZ8R_c-vKbcM');

  const handlePersonaChange = (persona) => {
    setSelectedPersona(persona);
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🎵 ChatFinal3 Voice Selection Test</h1>
      
      <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
        <h3>🎯 Test Instructions</h3>
        <ol>
          <li><strong>Select a specialist</strong> below to test different default voices</li>
          <li><strong>Connect to chat</strong> and listen to the welcome message</li>
          <li><strong>Try voice selection</strong> in the chat interface:
            <ul>
              <li><strong>"Default"</strong> - Uses specialist-specific voice</li>
              <li><strong>Custom voices</strong> - Override with your choice</li>
            </ul>
          </li>
          <li><strong>Check browser console</strong> for voice selection logs</li>
        </ol>
      </div>

      <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#e7f3ff', borderRadius: '8px' }}>
        <h3>👨‍⚕️ Select Healthcare Specialist</h3>
        <div style={{ display: 'flex', gap: '10px', marginBottom: '10px' }}>
          <button
            onClick={() => handlePersonaChange('psychologist')}
            style={{
              padding: '10px 20px',
              backgroundColor: selectedPersona === 'psychologist' ? '#6f42c1' : '#e9ecef',
              color: selectedPersona === 'psychologist' ? 'white' : '#333',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            🧠 Psychologist (Dr. Ori)
          </button>
          <button
            onClick={() => handlePersonaChange('dietician')}
            style={{
              padding: '10px 20px',
              backgroundColor: selectedPersona === 'dietician' ? '#fd7e14' : '#e9ecef',
              color: selectedPersona === 'dietician' ? 'white' : '#333',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            🥗 Dietician (Dr. Maya)
          </button>
        </div>
        
        <div style={{ fontSize: '0.9rem', color: '#6c757d' }}>
          <strong>Default Voices:</strong>
          <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
            <li><strong>Psychologist:</strong> Onyx (Male, Deep)</li>
            <li><strong>Dietician:</strong> Shimmer (Female, Clear)</li>
          </ul>
        </div>
      </div>

      <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#fff3cd', borderRadius: '8px' }}>
        <h3>🎵 Voice Selection Features</h3>
        <ul>
          <li><strong>Smart Defaults:</strong> Each specialist has a default voice that matches their role</li>
          <li><strong>Custom Override:</strong> Users can choose any voice for any specialist</li>
          <li><strong>Welcome Message:</strong> Uses the selected voice from the first interaction</li>
          <li><strong>Consistent Experience:</strong> Voice choice persists throughout the conversation</li>
          <li><strong>Visual Feedback:</strong> Helper text shows which default voice is being used</li>
        </ul>
      </div>

      <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#d1ecf1', borderRadius: '8px' }}>
        <h3>🧪 Testing Checklist</h3>
        <ul>
          <li>✅ <strong>Default Voice Selection:</strong> Select "Default" and verify correct voice for each specialist</li>
          <li>✅ <strong>Custom Voice Selection:</strong> Choose specific voices and verify they're used</li>
          <li>✅ <strong>Welcome Message Voice:</strong> Listen to welcome message with chosen voice</li>
          <li>✅ <strong>Voice Switching:</strong> Change voices mid-conversation</li>
          <li>✅ <strong>Console Logging:</strong> Check browser console for voice selection logs</li>
          <li>✅ <strong>Helper Text:</strong> Verify helper text updates when "Default" is selected</li>
        </ul>
      </div>

      <div style={{ border: '2px solid #4a6fa5', borderRadius: '10px', overflow: 'hidden' }}>
        <div style={{ 
          backgroundColor: '#4a6fa5', 
          color: 'white', 
          padding: '10px 15px', 
          fontSize: '1.1rem',
          fontWeight: 'bold'
        }}>
          🎵 ChatFinal3 with Voice Selection - {selectedPersona === 'psychologist' ? 'Psychologist' : 'Dietician'}
        </div>
        
        <ChatFinal3
          patientId={testPatientId}
          persona={selectedPersona}
          token={testToken}
          conversationId={null} // Always start new conversation for testing
        />
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
        <h3>📊 Expected Console Logs</h3>
        <pre style={{ backgroundColor: '#e9ecef', padding: '10px', borderRadius: '5px', fontSize: '0.8rem' }}>
{`🎵 Authenticating with voice: onyx (selected: default)
🌊 Streaming started - Persona: psychologist, Voice: onyx
🎵 Sending message with voice: shimmer (selected: shimmer)
🎵 Sending audio with voice: nova (selected: nova)`}
        </pre>
      </div>
    </div>
  );
};

export default ChatFinal3VoiceTest;
