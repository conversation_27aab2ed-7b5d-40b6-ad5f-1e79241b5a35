<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Health Insights</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: #4a6fa5;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 15px;
        }
        .insights-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .insight-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            transition: transform 0.3s ease;
        }
        .insight-card:hover {
            transform: translateY(-5px);
        }
        .insight-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .insight-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e3f2fd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: #4a6fa5;
        }
        .insight-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }
        .insight-description {
            color: #555;
            margin-bottom: 15px;
        }
        .chart-container {
            height: 200px;
            margin-top: 15px;
        }
        .progress-container {
            margin-top: 15px;
        }
        .progress {
            height: 10px;
            border-radius: 5px;
        }
        .recommendations-list {
            list-style-type: none;
            padding: 0;
        }
        .recommendations-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .recommendations-list li:last-child {
            border-bottom: none;
        }
        .recommendation-category {
            font-weight: 600;
            color: #4a6fa5;
            margin-right: 10px;
        }
        .emotion-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .emotion-item {
            background-color: #f0f2f5;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9rem;
        }
        .calendar-item {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            background-color: #f0f2f5;
        }
        .calendar-date {
            font-weight: 600;
            color: #4a6fa5;
        }
        .login-container {
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .error-message {
            color: #dc3545;
            margin-top: 10px;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4a6fa5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-chart-line me-2"></i> Your Health Insights</h1>
        <p>Personalized insights to help you understand and improve your health</p>
    </div>

    <div class="container">
        <div id="loginForm" class="login-container">
            <h2 class="mb-4">View Your Health Insights</h2>
            <div class="mb-3">
                <label for="patientId" class="form-label">Patient ID</label>
                <input type="text" class="form-control" id="patientId" placeholder="Enter your patient ID" value="f31a95c6-76ef-4bb2-936c-b258285682d9">
            </div>
            <button id="loginBtn" class="btn btn-primary w-100">View My Insights</button>
            <div id="loginError" class="error-message"></div>
        </div>

        <div id="insightsView" style="display: none;">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Your Personal Health Insights</h2>
                <button id="refreshBtn" class="btn btn-outline-primary">
                    <i class="fas fa-sync-alt me-2"></i> Refresh Insights
                </button>
            </div>

            <div id="loadingIndicator" class="loading">
                <div class="spinner"></div>
            </div>

            <div id="insightsContainer" class="insights-container"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const insightsView = document.getElementById('insightsView');
            const patientIdInput = document.getElementById('patientId');
            const loginBtn = document.getElementById('loginBtn');
            const loginError = document.getElementById('loginError');
            const refreshBtn = document.getElementById('refreshBtn');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const insightsContainer = document.getElementById('insightsContainer');

            // Hardcoded JWT token - no need for user to enter it
            const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.3oZ2Ubh5rLBdHvQHd5Qr9GJczA5MXcxaVx5H5xLwvZ4';

            // Check if we have stored patient ID
            const storedPatientId = localStorage.getItem('patientId');

            if (storedPatientId) {
                patientIdInput.value = storedPatientId;
                // Auto-login if we have a stored patient ID
                fetchInsights(storedPatientId);
            }

            loginBtn.addEventListener('click', function() {
                const patientId = patientIdInput.value.trim();

                if (!patientId) {
                    loginError.textContent = 'Please enter your patient ID';
                    return;
                }

                // Store patient ID for next time
                localStorage.setItem('patientId', patientId);

                fetchInsights(patientId);
            });

            refreshBtn.addEventListener('click', function() {
                const patientId = localStorage.getItem('patientId');

                if (patientId) {
                    fetchInsights(patientId);
                }
            });

            function fetchInsights(patientId) {
                // Show loading indicator
                loginForm.style.display = 'none';
                insightsView.style.display = 'block';
                loadingIndicator.style.display = 'flex';
                insightsContainer.innerHTML = '';

                // Fetch insights from API using hardcoded JWT token
                fetch(`/insights/patient/${patientId}`, {
                    headers: {
                        'Authorization': `Bearer ${JWT_TOKEN}`
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loading indicator
                    loadingIndicator.style.display = 'none';

                    // Display insights
                    displayInsights(data.insights);
                })
                .catch(error => {
                    console.error('Error fetching insights:', error);
                    loadingIndicator.style.display = 'none';
                    insightsContainer.innerHTML = `
                        <div class="alert alert-danger w-100">
                            <h4>Error Loading Insights</h4>
                            <p>${error.message}</p>
                            <button class="btn btn-outline-danger mt-2" onclick="location.reload()">Try Again</button>
                        </div>
                    `;

                    // Show login form again if authentication failed
                    if (error.message.includes('401') || error.message.includes('403')) {
                        loginForm.style.display = 'block';
                        insightsView.style.display = 'none';
                        loginError.textContent = 'Authentication failed. Please try again.';

                        // Clear stored patient ID
                        localStorage.removeItem('patientId');
                    }
                });
            }

            function displayInsights(insights) {
                if (!insights || insights.length === 0) {
                    insightsContainer.innerHTML = `
                        <div class="alert alert-info w-100">
                            <h4>No Insights Available</h4>
                            <p>We don't have any insights for you at the moment. Check back later!</p>
                        </div>
                    `;
                    return;
                }

                // Clear container
                insightsContainer.innerHTML = '';

                // Add each insight
                insights.forEach(insight => {
                    const insightCard = document.createElement('div');
                    insightCard.className = 'insight-card';

                    // Determine icon based on insight type
                    let iconClass = 'fas fa-info-circle';
                    switch (insight.type) {
                        case 'chart':
                            iconClass = 'fas fa-chart-line';
                            break;
                        case 'progress':
                            iconClass = 'fas fa-tasks';
                            break;
                        case 'emotion':
                            iconClass = 'fas fa-smile';
                            break;
                        case 'calendar':
                            iconClass = 'fas fa-calendar-alt';
                            break;
                        case 'recommendations':
                            iconClass = 'fas fa-lightbulb';
                            break;
                    }

                    // Create header
                    const header = `
                        <div class="insight-header">
                            <div class="insight-icon">
                                <i class="${iconClass}"></i>
                            </div>
                            <h3 class="insight-title">${insight.title}</h3>
                        </div>
                        <p class="insight-description">${insight.description}</p>
                    `;

                    // Create content based on insight type
                    let content = '';

                    if (insight.type === 'chart' && insight.data && insight.data.dates && insight.data.scores) {
                        const chartId = `chart-${Math.random().toString(36).substr(2, 9)}`;
                        content = `<div class="chart-container"><canvas id="${chartId}"></canvas></div>`;

                        // Add chart initialization after DOM is updated
                        setTimeout(() => {
                            const ctx = document.getElementById(chartId).getContext('2d');
                            new Chart(ctx, {
                                type: 'line',
                                data: {
                                    labels: insight.data.dates,
                                    datasets: [{
                                        label: 'Health Score',
                                        data: insight.data.scores,
                                        backgroundColor: 'rgba(74, 111, 165, 0.2)',
                                        borderColor: 'rgba(74, 111, 165, 1)',
                                        borderWidth: 2,
                                        tension: 0.3,
                                        pointBackgroundColor: 'rgba(74, 111, 165, 1)'
                                    }]
                                },
                                options: {
                                    scales: {
                                        y: {
                                            beginAtZero: false,
                                            min: Math.max(0, Math.min(...insight.data.scores) - 10),
                                            max: Math.min(100, Math.max(...insight.data.scores) + 10)
                                        }
                                    },
                                    responsive: true,
                                    maintainAspectRatio: false
                                }
                            });
                        }, 0);
                    } else if (insight.type === 'progress' && insight.data && insight.data.adherence_rate !== null) {
                        const adherenceRate = insight.data.adherence_rate;
                        const progressColor = adherenceRate >= 90 ? 'success' :
                                            adherenceRate >= 70 ? 'info' :
                                            adherenceRate >= 50 ? 'warning' : 'danger';

                        content = `
                            <div class="progress-container">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Medication Adherence</span>
                                    <span>${adherenceRate}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-${progressColor}" role="progressbar"
                                        style="width: ${adherenceRate}%" aria-valuenow="${adherenceRate}"
                                        aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="mt-3">
                                    <small>Taken: ${insight.data.total_doses - insight.data.missed_doses} of ${insight.data.total_doses} doses</small>
                                </div>
                            </div>
                        `;
                    } else if (insight.type === 'recommendations' && insight.data && insight.data.recommendations) {
                        content = '<ul class="recommendations-list">';
                        insight.data.recommendations.forEach(rec => {
                            content += `
                                <li>
                                    <span class="recommendation-category">${rec.category}:</span>
                                    ${rec.recommendation}
                                </li>
                            `;
                        });
                        content += '</ul>';
                    } else if (insight.type === 'emotion' && insight.data) {
                        content = `
                            <div>
                                <div class="mb-2">
                                    <strong>Dominant Emotion:</strong> ${insight.data.dominant_emotion}
                                </div>
                            `;

                        if (insight.data.emotion_distribution && Object.keys(insight.data.emotion_distribution).length > 0) {
                            content += '<div class="emotion-container">';
                            for (const [emotion, value] of Object.entries(insight.data.emotion_distribution)) {
                                content += `
                                    <div class="emotion-item">
                                        ${emotion}: ${typeof value === 'number' ? Math.round(value * 100) + '%' : value}
                                    </div>
                                `;
                            }
                            content += '</div>';
                        }

                        content += '</div>';
                    } else if (insight.type === 'calendar' && insight.data && insight.data.appointments) {
                        content = '<div>';
                        insight.data.appointments.slice(0, 3).forEach(appt => {
                            content += `
                                <div class="calendar-item">
                                    <div class="calendar-date">${appt.date} at ${appt.time}</div>
                                    <div>Doctor: ${appt.doctor}</div>
                                    <div>Reason: ${appt.reason || 'Not specified'}</div>
                                </div>
                            `;
                        });
                        content += '</div>';
                    }

                    insightCard.innerHTML = header + content;
                    insightsContainer.appendChild(insightCard);
                });
            }
        });
    </script>
</body>
</html>
