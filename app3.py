from fastapi import <PERSON><PERSON><PERSON>, Depends
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
import os
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the chat_final3 service
from services.chat_final3 import chat_final_router3
from model.model_correct import SessionLocal

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

app = FastAPI(title="Healthcare Chat Final3 Test API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Include the chat_final3 router
app.include_router(
    chat_final_router3,
    tags=["Chat Final3 Service"]
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Serve the React component test page
@app.get("/")
def serve_chat_test():
    return FileResponse(os.path.join("static", "chat_final3_test.html"))

# Serve the React JSX component (for development)
@app.get("/chat-component")
def serve_chat_component():
    return FileResponse(os.path.join("static", "ChatFinal3.jsx"))

# Serve the CSS file
@app.get("/chat-styles")
def serve_chat_styles():
    return FileResponse(os.path.join("static", "ChatFinal3.css"))

# Health check endpoint
@app.get("/health")
def health_check():
    return {"status": "healthy", "service": "chat_final3_test"}

# Debug endpoint to check if chat service is loaded
@app.get("/debug/routes")
def debug_routes():
    """Debug endpoint to show all available routes."""
    routes = []
    for route in app.routes:
        if hasattr(route, 'path') and hasattr(route, 'methods'):
            routes.append({
                "path": route.path,
                "methods": list(route.methods) if route.methods else ["WebSocket"]
            })
    return {"routes": routes}

# Test WebSocket endpoint
@app.get("/test-websocket")
def test_websocket():
    """Test if WebSocket endpoint is available."""
    return {
        "websocket_url": "ws://localhost:8000/chat-final3/{patient_id}/{persona}",
        "test_url": "ws://localhost:8000/chat-final3/f31a95c6-76ef-4bb2-936c-b258285682d9/psychologist",
        "instructions": "Use this URL to test WebSocket connection"
    }

# Favicon handler
@app.get("/favicon.ico")
async def favicon():
    """Serve favicon.ico file."""
    favicon_path = os.path.join("static", "favicon.ico")
    if os.path.exists(favicon_path):
        return FileResponse(favicon_path, media_type="image/x-icon")
    else:
        # Return a simple 1x1 transparent ICO if file doesn't exist
        from fastapi.responses import Response
        ico_data = b'\x00\x00\x01\x00\x01\x00\x01\x01\x00\x00\x01\x00\x20\x00\x68\x00\x00\x00\x16\x00\x00\x00\x28\x00\x00\x00\x01\x00\x00\x00\x02\x00\x00\x00\x01\x00\x20\x00\x00\x00\x00\x00\x04\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
        return Response(content=ico_data, media_type="image/x-icon")

if __name__ == "__main__":
    import uvicorn

    # Check if environment variables are set
    azure_key = os.getenv("azure_openai_api_key") or os.getenv("AZURE_OPENAI_API_KEY")
    azure_endpoint = os.getenv("azure_openai_endpoint") or os.getenv("AZURE_OPENAI_ENDPOINT")

    print("🚀 Starting Healthcare Chat Final3 Test Server...")
    print("📱 Chat Interface: http://localhost:8000")
    print("🔧 Health Check: http://localhost:8000/health")
    print("🛠️ Debug Routes: http://localhost:8000/debug/routes")
    print("🔌 WebSocket Test: http://localhost:8000/test-websocket")
    print("📄 React Component: http://localhost:8000/chat-component")
    print("🎨 CSS Styles: http://localhost:8000/chat-styles")
    print(f"🔑 Azure OpenAI Key: {'✅ Set' if azure_key else '❌ Missing'}")
    print(f"🌐 Azure OpenAI Endpoint: {'✅ Set' if azure_endpoint else '❌ Missing'}")
    print("🔌 WebSocket URL: ws://localhost:8000/chat-final3/{patient_id}/{persona}")

    logger.info("Starting FastAPI server with chat_final3 service")
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
