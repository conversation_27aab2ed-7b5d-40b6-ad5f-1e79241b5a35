# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

env
venv

# Docker
.dockerignore
docker-compose.override.yml

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Project specific
auth/__pycache__/
database/__pycache__/
model/__pycache__/
services/__pycache__/
other_files/
logs/
temp/
*.log

# Test files
api.md
static/smart_chat.html
static/minimal_websocket_test.html
static/smart_chat_jwt.html
static/smart_chat_simple.html
static/test_smart_chat_jwt.html
test_*.py
test_*.txt
websocket_test*.html

# Simple WebSocket Test
simple_websocket_test.py

# Simple WebSocket Test Output
simple_websocket_test_output.txt
test_no_filter.txt
test_no_filter_output.txt
test_no_filter.py
test_no_filter_output_filtered.txt
test_simple.py
test_websocket.py
test_recommendation_performance.py


# AWS
.aws/
aws-credentials.json
aws-secrets.json
aws-secrets-setup.ps1
aws-ecs-setup.ps1
aws-ecr-setup.ps1
