{"architectures": ["EmotionAVModel"], "auto_map": {"AutoConfig": "modeling_emotion_av.EmotionAVConfig", "AutoModel": "modeling_emotion_av.EmotionAVModel", "AutoModelForAudioClassification": "modeling_emotion_av.EmotionAVModel"}, "dropout_rate": 0.4, "emotion_labels": ["angry", "disgust", "fear", "happy", "neutral", "sad"], "emotion_to_av_mapping": {"angry": {"arousal": -1.0, "valence": -0.9269662921348314}, "disgust": {"arousal": 1.0, "valence": 0.22539062733339038}, "fear": {"arousal": -1.0, "valence": -0.0003170637456042718}, "happy": {"arousal": -0.5347432024169184, "valence": 1.0}, "neutral": {"arousal": 0.1546223286796688, "valence": -1.0}, "sad": {"arousal": 0.06459984477929674, "valence": -1.0}}, "final_size": 256, "hidden_size": 1024, "id2label": {"0": "angry", "1": "disgust", "2": "fear", "3": "happy", "4": "neutral", "5": "sad"}, "input_dim": 787, "intermediate_size": 512, "label2id": {"angry": 0, "disgust": 1, "fear": 2, "happy": 3, "neutral": 4, "sad": 5}, "model_type": "emotion_av", "num_emotion_classes": 6, "pipeline_tag": "audio-classification", "torch_dtype": "float32", "transformers_version": "4.52.3"}