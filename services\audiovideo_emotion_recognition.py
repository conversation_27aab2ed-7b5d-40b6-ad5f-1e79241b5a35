import os
import base64
import uuid
import torch
import librosa
import requests
import numpy as np
from datetime import datetime, timezone
from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel
from typing import List, Dict, Any
from dotenv import load_dotenv
from collections import defaultdict
from transformers import AutoFeatureExtractor, AutoModelForAudioClassification, pipeline
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
import logging
import cv2
import tensorflow as tf
from deepface import DeepFace
import tempfile
import shutil
import warnings

# Disable warnings
warnings.filterwarnings("ignore", category=FutureWarning, module="librosa")
warnings.filterwarnings("ignore", message="Some weights of.*were not initialized.*")
os.environ["HF_HUB_DISABLE_SYMLINKS_WARNING"] = "1"

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Load Environment Variables ---
load_dotenv()
HF_TOKEN = os.getenv("HF_API_Key2")
HF_API_URL = os.getenv("HF_URL_VISION") or "https://api-inference.huggingface.co/models/trpakov/vit-face-expression"
HF_IMAGE_DESCRIP_URL = os.getenv("Image_description_url")
NVIDIA_SCOUT_API = os.getenv("NVIDIA_LLAMA4_SCOUT_API_KEY")

# --- Import your SQLAlchemy models and session maker ---
from model.model_correct import AudioEmotion, ImageEmotion, SessionLocal

# --- Routers ---
audio_router = APIRouter()
image_router = APIRouter()

# --- DB Dependency ---
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# --- Request Models ---
class AudioInput(BaseModel):
    patient_id: str = "default_patient"
    conversation_id: str = "default_conversation"
    session_id: str = None
    audio_base64: str
    timestamp: datetime = datetime.now(timezone.utc)

class ImageInput(BaseModel):
    patient_id: str = "default_patient"
    conversation_id: str = "default_conversation"
    session_id: str = None
    image_base64: str
    timestamp: datetime = datetime.now(timezone.utc)

# --- Global Variables ---
_deepface_initialized = False
_audio_emotion_pipeline = None

# --- Emotion Mappings ---
emotion_mapping = {
    'angry': 'anger', 'disgust': 'disgust', 'fear': 'fear',
    'happy': 'joy', 'sad': 'sadness', 'surprise': 'surprise', 'neutral': 'neutral'
}

arousal_valence_map = {
    'anger': (0.8, 0.2), 'joy': (0.7, 0.8), 'sadness': (0.3, 0.2),
    'fear': (0.8, 0.3), 'surprise': (0.8, 0.6), 'disgust': (0.6, 0.2),
    'neutral': (0.5, 0.5)
}

# --- Initialize DeepFace ---
def initialize_deepface():
    global _deepface_initialized
    if not _deepface_initialized:
        try:
            logger.info("🔄 Initializing DeepFace...")
            # Warm up DeepFace with a dummy image
            dummy_img = np.zeros((224, 224, 3), dtype=np.uint8)
            DeepFace.analyze(dummy_img, actions=["emotion"], enforce_detection=False, silent=True)
            _deepface_initialized = True
            logger.info("✅ DeepFace initialized successfully")
        except Exception as e:
            logger.error(f"❌ DeepFace initialization failed: {str(e)}")
            raise e

# --- Image Emotion Detection (Fixed function name) ---
def detect_image_emotion(image_path: str) -> Dict[str, Any]:
    """Detect emotion from image using DeepFace."""
    try:
        if not _deepface_initialized:
            initialize_deepface()
        
        logger.info("🧠 Starting DeepFace emotion analysis...")
        
        result = DeepFace.analyze(
            img_path=image_path,
            actions=["emotion"],
            enforce_detection=False,
            detector_backend='opencv',
            silent=True
        )
        
        if isinstance(result, list):
            if len(result) == 0:
                logger.warning("⚠️ No faces detected in image")
                return {"emotion": "neutral", "confidence": 0.5, "details": {}}
            result = result[0]
        
        emotions = result.get('emotion', {})
        dominant_emotion = result.get('dominant_emotion', 'neutral').lower()
        confidence = emotions.get(dominant_emotion, 0.5) / 100.0
        
        mapped_emotion = emotion_mapping.get(dominant_emotion, dominant_emotion)
        
        logger.info(f"✅ Image emotion detected: {mapped_emotion} ({confidence:.2f})")
        
        return {
            "emotion": mapped_emotion,
            "confidence": confidence,
            "details": emotions
        }
        
    except Exception as e:
        logger.error(f"❌ Image emotion detection failed: {str(e)}")
        return {"emotion": "neutral", "confidence": 0.5, "details": {}}

# --- Audio Pipeline Initialization ---
def get_audio_emotion_pipeline():
    global _audio_emotion_pipeline
    
    if _audio_emotion_pipeline is None:
        try:
            logger.info("🔄 Loading audio emotion recognition pipeline...")
            
            _audio_emotion_pipeline = pipeline(
                "audio-classification",
                model="ehcalabres/wav2vec2-lg-xlsr-en-speech-emotion-recognition",
                device=0 if torch.cuda.is_available() else -1
            )
            
            logger.info("✅ Audio emotion pipeline loaded successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to load audio emotion pipeline: {str(e)}")
            try:
                _audio_emotion_pipeline = pipeline(
                    "audio-classification",
                    model="superb/wav2vec2-base-superb-er",
                    device=0 if torch.cuda.is_available() else -1
                )
                logger.info("✅ Fallback audio emotion pipeline loaded successfully")
            except Exception as fallback_error:
                logger.error(f"❌ Fallback model also failed: {str(fallback_error)}")
                raise fallback_error
    
    return _audio_emotion_pipeline

# --- Audio Emotion Prediction ---
def predict_audio_emotion(audio_path: str) -> Dict[str, Any]:
    """Predict emotion from audio using HuggingFace pipeline."""
    try:
        logger.info(f"🎵 === AUDIO EMOTION PREDICTION STARTED ===")
        
        pipeline_model = get_audio_emotion_pipeline()
        
        # Load and preprocess audio
        audio, sr = librosa.load(audio_path, sr=16000)
        
        # Get predictions
        results = pipeline_model(audio)
        
        if not results:
            raise ValueError("No predictions returned from model")
        
        # Get top prediction
        top_prediction = max(results, key=lambda x: x['score'])
        emotion_label = top_prediction['label'].lower()
        confidence = top_prediction['score']
        
        # Map emotion
        mapped_emotion = emotion_mapping.get(emotion_label, emotion_label)
        arousal, valence = arousal_valence_map.get(mapped_emotion, (0.5, 0.5))
        
        logger.info(f"🎯 Emotion: {mapped_emotion}, Confidence: {confidence:.4f}")
        
        return {
            "emotion": mapped_emotion,
            "confidence": confidence,
            "arousal": arousal,
            "valence": valence,
            "all_predictions": results
        }
        
    except Exception as e:
        logger.error(f"❌ Audio emotion prediction failed: {str(e)}")
        # Try HuggingFace API fallback
        return predict_audio_emotion_hf_api(audio_path)

# --- HuggingFace API Fallback ---
def predict_audio_emotion_hf_api(audio_path: str) -> Dict[str, Any]:
    """Fallback method using HuggingFace API for audio emotion detection."""
    logger.info("🌐 Using HuggingFace API for audio emotion detection...")
    
    try:
        with open(audio_path, "rb") as f:
            audio_bytes = f.read()
        
        api_url = "https://api-inference.huggingface.co/models/ehcalabres/wav2vec2-lg-xlsr-en-speech-emotion-recognition"
        
        headers = {
            "Authorization": f"Bearer {HF_TOKEN}",
            "Content-Type": "audio/wav"
        }
        
        response = requests.post(api_url, headers=headers, data=audio_bytes)
        response.raise_for_status()
        
        results = response.json()
        
        if not results:
            raise ValueError("No results from HuggingFace API")
        
        top_prediction = max(results, key=lambda x: x['score'])
        emotion_label = top_prediction['label'].lower()
        confidence = top_prediction['score']
        
        mapped_emotion = emotion_mapping.get(emotion_label, emotion_label)
        arousal, valence = arousal_valence_map.get(mapped_emotion, (0.5, 0.5))
        
        return {
            "emotion": mapped_emotion,
            "confidence": confidence,
            "arousal": arousal,
            "valence": valence,
            "all_predictions": results
        }
        
    except Exception as e:
        logger.error(f"❌ HuggingFace API prediction failed: {str(e)}")
        # Return neutral as final fallback
        return {
            "emotion": "neutral",
            "confidence": 0.5,
            "arousal": 0.5,
            "valence": 0.5,
            "all_predictions": []
        }

# --- Image Description Function ---
def describe_user_image(image_path: str, api_url: str, token: str) -> str:
    """Generate description of an image using NVIDIA API."""
    try:
        with open(image_path, "rb") as image_file:
            image_b64 = base64.b64encode(image_file.read()).decode()
        
        headers = {
            "Authorization": f"Bearer {NVIDIA_SCOUT_API}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "meta/llama-3.2-90b-vision-instruct",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "Describe this image in detail."},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_b64}"}}
                    ]
                }
            ],
            "max_tokens": 300,
            "temperature": 0.7
        }
        
        response = requests.post(api_url, headers=headers, json=payload)
        response.raise_for_status()
        
        return response.json()["choices"][0]["message"]["content"]
        
    except Exception as e:
        logger.error(f"❌ Image description failed: {str(e)}")
        return "Unable to generate image description."

# --- API Endpoints ---

@audio_router.post("/analyze-conversation-audio", status_code=status.HTTP_200_OK)
async def analyze_conversation_audio(input: AudioInput, db: Session = Depends(get_db)):
    """Analyze audio emotion during a conversation for real-time emotion tracking."""
    logger.info("🎤 === AUDIO EMOTION ANALYSIS STARTED ===")
    
    temp_audio_path = None
    
    try:
        # Decode base64 audio
        audio_data = base64.b64decode(input.audio_base64.split(",")[-1])
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
            tmp_file.write(audio_data)
            temp_audio_path = tmp_file.name
        
        # Analyze emotion
        result = predict_audio_emotion(temp_audio_path)
        
        # Generate session_id if not provided
        session_id = input.session_id or f"conv_{input.conversation_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Create database record
        sample_id = str(uuid.uuid4())
        record = AudioEmotion(
            sample_id=sample_id,
            patient_id=input.patient_id,
            conversation_id=input.conversation_id,
            session_id=session_id,
            timestamp=input.timestamp,
            source="audio",
            emotion=result["emotion"],
            confidence=result["confidence"],
            arousal=result["arousal"],
            valence=result["valence"]
        )
        
        db.add(record)
        db.commit()
        
        response_data = {
            "status": "success",
            "sample_id": record.sample_id,
            "patient_id": input.patient_id,
            "conversation_id": input.conversation_id,
            "emotion_data": {
                "emotion": result["emotion"],
                "confidence": result["confidence"],
                "arousal": result["arousal"],
                "valence": result["valence"]
            },
            "timestamp": record.timestamp.isoformat()
        }
        
        return response_data
        
    except Exception as e:
        logger.error(f"❌ Audio analysis failed: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Audio analysis failed: {str(e)}")
        
    finally:
        if temp_audio_path and os.path.exists(temp_audio_path):
            os.unlink(temp_audio_path)

@image_router.post("/analyze-conversation-image", status_code=status.HTTP_200_OK)
async def analyze_conversation_image(payload: ImageInput, db: Session = Depends(get_db)):
    """Analyze facial emotion from image during a conversation using DeepFace."""
    logger.info("📷 === IMAGE EMOTION ANALYSIS STARTED ===")
    
    temp_image_path = None
    
    try:
        # Decode base64 image
        image_data = base64.b64decode(payload.image_base64.split(",")[-1])
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_file:
            tmp_file.write(image_data)
            temp_image_path = tmp_file.name
        
        # Analyze emotion using DeepFace
        result = detect_image_emotion(temp_image_path)
        
        # Generate session_id if not provided
        session_id = payload.session_id or f"conv_{payload.conversation_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Create database record
        sample_id = str(uuid.uuid4())
        record = ImageEmotion(
            sample_id=sample_id,
            patient_id=payload.patient_id,
            conversation_id=payload.conversation_id,
            session_id=session_id,
            timestamp=payload.timestamp,
            source="image",
            emotion=result["emotion"],
            confidence=result["confidence"]
        )
        
        db.add(record)
        db.commit()
        
        response_data = {
            "status": "success",
            "sample_id": record.sample_id,
            "patient_id": payload.patient_id,
            "conversation_id": payload.conversation_id,
            "emotion_data": {
                "emotion": result["emotion"],
                "confidence": result["confidence"]
            },
            "timestamp": record.timestamp.isoformat()
        }
        
        return response_data
        
    except Exception as e:
        logger.error(f"❌ Image analysis failed: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Image analysis failed: {str(e)}")
        
    finally:
        if temp_image_path and os.path.exists(temp_image_path):
            os.unlink(temp_image_path)

@image_router.post("/image-description", status_code=status.HTTP_200_OK)
async def describe_image(payload: ImageInput):
    """Generate description of an image."""
    temp_image_path = None
    
    try:
        # Decode base64 image
        image_data = base64.b64decode(payload.image_base64.split(",")[-1])
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_file:
            tmp_file.write(image_data)
            temp_image_path = tmp_file.name
        
        # Generate description
        description = describe_user_image(temp_image_path, HF_IMAGE_DESCRIP_URL, HF_TOKEN)
        
        return {"description": description}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Image description failed: {str(e)}")
        
    finally:
        if temp_image_path and os.path.exists(temp_image_path):
            os.unlink(temp_image_path)

@audio_router.get("/conversation/{conversation_id}/emotions")
def get_conversation_emotions(conversation_id: str, db: Session = Depends(get_db)):
    """Get all emotions for a specific conversation."""
    try:
        # Get audio emotions
        audio_emotions = db.query(AudioEmotion).filter(
            AudioEmotion.conversation_id == conversation_id
        ).order_by(AudioEmotion.timestamp.desc()).all()
        
        # Get image emotions
        image_emotions = db.query(ImageEmotion).filter(
            ImageEmotion.conversation_id == conversation_id
        ).order_by(ImageEmotion.timestamp.desc()).all()
        
        # Format response
        audio_data = [
            {
                "sample_id": emotion.sample_id,
                "patient_id": emotion.patient_id,
                "timestamp": emotion.timestamp.isoformat(),
                "emotion": emotion.emotion,
                "confidence": emotion.confidence,
                "arousal": emotion.arousal,
                "valence": emotion.valence,
                "source": "audio"
            }
            for emotion in audio_emotions
        ]
        
        image_data = [
            {
                "sample_id": emotion.sample_id,
                "patient_id": emotion.patient_id,
                "timestamp": emotion.timestamp.isoformat(),
                "emotion": emotion.emotion,
                "confidence": emotion.confidence,
                "source": "image"
            }
            for emotion in image_emotions
        ]
        
        return {
            "conversation_id": conversation_id,
            "total_audio_samples": len(audio_data),
            "total_image_samples": len(image_data),
            "audio_emotions": audio_data,
            "image_emotions": image_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving conversation emotions: {str(e)}")

@audio_router.get("/health")
def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "enhanced_audiovideo_emotion_recognition",
        "features": ["deepface_emotion", "huggingface_audio_emotion", "conversation_tracking"],
        "models": {
            "image_emotion": "DeepFace with OpenCV backend",
            "audio_emotion": "HuggingFace Transformers Pipeline",
            "fallback_audio": "HuggingFace API"
        }
    }
