# Stop and remove any existing container
docker stop prasha-chat-bot 2>$null
docker rm prasha-chat-bot 2>$null

# Set environment variables
$envVars = @(
    "--env", "HF_API_URL=https://router.huggingface.co/hf-inference/models/SamLowe/roberta-base-go_emotions"
    "--env", "HF_API_KEY=*************************************"
    "--env","postgres_username=postgres"
    "--env","postgres_password=Prashaind2025"
    "--env","postgres_host=prashasync-db.eastus.cloudapp.azure.com"
    "--env","postgres_port=5432"
    "--env","postgres_database=postgres"
    "--env","azure_openai_endpoint=https://mentalhealth-bot.openai.azure.com/"
    "--env","azure_openai_api_key=7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL"
    "--env","azure_openai_audio_endpoint=https://rohan-mcj7eabj-swedencentral.cognitiveservices.azure.com/"
    "--env","azure_openai_audio_api_key=3UQVkRcpzKYj17XgXR4HCuCMiQDR0CMZNUR4RWqv6bg5rJRMC6p7JQQJ99BFACfhMk5XJ3w3AAAAACOG4Bp7"
    "--env","AZURE_OPENAI_STT_API_KEY=5qEkB5GSMSg63Hh9q0Mmtyn84DtHEYOrnCvOmSbfDxEgPwYGIwtmJQQJ99BFACHYHv6XJ3w3AAAAACOG4iPD"
    "--env","AZURE_OPENAI_STT_ENDPOINT=https://rohan-mcj671r5-eastus2.cognitiveservices.azure.com/"
    "--env","AZURE_OPENAI_EMBEDDING_API_KEY=5qEkB5GSMSg63Hh9q0Mmtyn84DtHEYOrnCvOmSbfDxEgPwYGIwtmJQQJ99BFACHYHv6XJ3w3AAAAACOG4iPD"
    "--env","USE_S3=False"
    "--env","HF_API_Key2=*************************************"
    "--env","HF_URL_VISION=https://router.huggingface.co/hf-inference/models/dima806/facial_emotions_image_detection"
    "--env","Image_description_url=https://router.huggingface.co/fireworks-ai/inference/v1/chat/completions"
    "--env","NVIDIA_LLAMA4_SCOUT_API_KEY=**********************************************************************"
    "--env","BLOB_SAS_URL=https://prashastorage.blob.core.windows.net/prasha-healthcare-data"
    "--env","PDF_BUCKET_NAME=prasha-healthcare-pdf"
    "--env","JWT_SECRET=e3ddffa7eb26539eb449c2f9fbd5bd0a566cf00bef73f37e015d826e0b602f0d"
    "--env","JWT_ALGORITHM=HS256"
    "--env","STATIC_JWT_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.3oZ2Ubh5rLBdHvQHd5Qr9GJczA5MXcxaVx5H5xLwvZ4"
        
)
# Run the container
# docker run -d `
#     --name prashaacrregistry `
#     -p 8000:8000 `
#     $envVars `
#     prashaacrregistry.azurecr.io/prasha-chatbot:latest
docker run -d `
    --name prasha-chat-bot `
    -p 8000:8000 `
    $envVars `
    prasannakumar012/prasha-chat-bot:latest
# Wait and show logs
Start-Sleep -Seconds 5
Write-Host "✅ Container started. Showing logs..."
# docker logs -f prashaacrregistry
docker logs -f prasha-chat-bot