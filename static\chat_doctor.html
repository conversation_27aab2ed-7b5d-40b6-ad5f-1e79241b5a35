<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Doctor AI Assistant</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #2ecc71;
            --light-color: #ecf0f1;
            --dark-color: #34495e;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --success-color: #27ae60;
            --info-color: #3498db;
            --border-radius: 10px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: var(--dark-color);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
            color: white;
            padding: 15px 0;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            box-shadow: var(--box-shadow);
        }

        .header h1 {
            font-size: 1.8rem;
            margin: 0;
        }

        .header p {
            margin: 5px 0 0;
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .login-container {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--box-shadow);
            max-width: 500px;
            margin: 40px auto;
            transition: var(--transition);
        }

        .login-container h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .chat-container {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 200px);
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }

        .chat-header {
            background-color: var(--primary-color);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-header h2 {
            font-size: 1.2rem;
            margin: 0;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .connected {
            background-color: var(--success-color);
        }

        .disconnected {
            background-color: var(--danger-color);
        }

        .connecting {
            background-color: var(--warning-color);
        }

        .chat-body {
            flex-grow: 1;
            display: flex;
            overflow: hidden;
        }

        .chat-messages {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .message {
            max-width: 80%;
            margin-bottom: 15px;
            padding: 12px 15px;
            border-radius: 18px;
            position: relative;
            line-height: 1.5;
        }

        .message.doctor {
            align-self: flex-end;
            background-color: var(--secondary-color);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.assistant {
            align-self: flex-start;
            background-color: #f1f3f5;
            color: var(--dark-color);
            border-bottom-left-radius: 5px;
        }

        .message-time {
            font-size: 0.7rem;
            opacity: 0.7;
            margin-top: 5px;
            text-align: right;
        }

        .chat-input {
            display: flex;
            padding: 15px;
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }

        .chat-input input {
            flex-grow: 1;
            padding: 12px 15px;
            border: 1px solid #ced4da;
            border-radius: 25px;
            margin-right: 10px;
            font-size: 0.95rem;
        }

        .chat-input input:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
        }

        .btn {
            border-radius: 25px;
            padding: 8px 20px;
            font-weight: 500;
            transition: var(--transition);
        }

        .btn-primary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }

        .btn-outline-secondary {
            color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--secondary-color);
            color: white;
        }

        .btn-icon {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .recording .btn-icon {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
            }
        }

        .patient-info {
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-radius: var(--border-radius);
            margin-bottom: 15px;
            border-left: 4px solid var(--info-color);
        }

        .patient-info h4 {
            color: var(--primary-color);
            font-size: 1rem;
            margin-bottom: 5px;
        }

        .patient-info p {
            margin: 0;
            font-size: 0.9rem;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 0.9rem;
            color: #6c757d;
        }

        .typing-indicator span {
            height: 8px;
            width: 8px;
            margin: 0 1px;
            background-color: #6c757d;
            border-radius: 50%;
            display: inline-block;
            animation: typing 1.4s infinite both;
        }

        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0% {
                opacity: 0.4;
                transform: translateY(0);
            }
            50% {
                opacity: 1;
                transform: translateY(-5px);
            }
            100% {
                opacity: 0.4;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header text-center">
            <h1><i class="fas fa-user-md me-2"></i> Doctor AI Assistant</h1>
            <p>Your intelligent medical assistant for patient care</p>
        </div>

        <div id="loginForm" class="login-container">
            <h2><i class="fas fa-sign-in-alt me-2"></i> Connect to Assistant</h2>
            <div class="mb-3">
                <label for="doctorId" class="form-label">Doctor ID</label>
                <input type="text" class="form-control" id="doctorId" value="b81fcef7-158a-49d2-b22c-ca9b5f7de696">
                <div class="form-text">Enter your doctor ID to connect to the AI assistant</div>
            </div>
            <div class="d-grid gap-2">
                <button id="connectBtn" class="btn btn-primary">
                    <i class="fas fa-plug me-2"></i> Connect
                </button>
            </div>
        </div>

        <div id="chatInterface" class="chat-container d-none">
            <div class="chat-header">
                <h2>
                    <i class="fas fa-user-md me-2"></i> Doctor Assistant
                    <span id="doctorName" class="ms-2 text-info"></span>
                </h2>
                <div class="status-indicator">
                    <div id="statusDot" class="status-dot disconnected"></div>
                    <span id="connectionStatus">Disconnected</span>
                </div>
            </div>
            <div class="chat-body">
                <div id="chatMessages" class="chat-messages">
                    <!-- Messages will be added here -->
                </div>
            </div>
            <div class="chat-input">
                <input type="text" id="messageInput" placeholder="Ask about a patient or medical information..." disabled>
                <button id="sendBtn" class="btn btn-primary ms-2" disabled>
                    <i class="fas fa-paper-plane"></i>
                </button>
                <button id="micBtn" class="btn btn-outline-secondary btn-icon ms-2" disabled>
                    <i class="fas fa-microphone"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Static JWT token from environment
        const STATIC_JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.3oZ2Ubh5rLBdHvQHd5Qr9GJczA5MXcxaVx5H5xLwvZ4';

        // DOM Elements
        const loginForm = document.getElementById('loginForm');
        const chatInterface = document.getElementById('chatInterface');
        const doctorIdInput = document.getElementById('doctorId');
        const connectBtn = document.getElementById('connectBtn');
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const micBtn = document.getElementById('micBtn');
        const connectionStatus = document.getElementById('connectionStatus');
        const statusDot = document.getElementById('statusDot');
        const doctorName = document.getElementById('doctorName');

        // WebSocket and recording variables
        let socket;
        let isRecording = false;
        let mediaRecorder;
        let audioChunks = [];

        // Connect to WebSocket
        connectBtn.addEventListener('click', function() {
            const doctorId = doctorIdInput.value.trim();

            if (!doctorId) {
                alert('Please enter a doctor ID');
                return;
            }

            // Update UI
            connectBtn.disabled = true;
            connectBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Connecting...';
            updateConnectionStatus('connecting', 'Connecting...');

            // Create WebSocket connection
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/smart-chat-doc/${doctorId}?token=${encodeURIComponent(STATIC_JWT_TOKEN)}`;

                // Close existing socket if any
                if (socket) {
                    socket.close();
                }

                socket = new WebSocket(wsUrl);

                // Connection opened
                socket.onopen = function(event) {
                    updateConnectionStatus('connected', 'Connected');

                    // Show chat interface, hide login form
                    loginForm.classList.add('d-none');
                    chatInterface.classList.remove('d-none');

                    // Enable chat controls
                    messageInput.disabled = false;
                    sendBtn.disabled = false;
                    micBtn.disabled = false;

                    // Focus input
                    messageInput.focus();

                    // Set doctor name
                    doctorName.textContent = `(ID: ${doctorId})`;

                    // Add welcome message
                    addMessage('Welcome to the Doctor AI Assistant. How can I help you today?', 'assistant');
                };

                // Listen for messages
                socket.onmessage = function(event) {
                    const data = JSON.parse(event.data);

                    if (data.error) {
                        addMessage(`Error: ${data.error}`, 'assistant');
                        updateConnectionStatus('error', 'Error');
                    } else if (data.transcription) {
                        // Replace the "Recording audio..." and "Processing audio..." messages with the actual transcription
                        const recordingMessages = Array.from(chatMessages.querySelectorAll('.message.doctor')).filter(
                            msg => msg.textContent.includes('Recording audio...') || msg.textContent.includes('Processing audio...')
                        );

                        if (recordingMessages.length > 0) {
                            // Remove the last two recording-related messages
                            if (recordingMessages.length >= 2) {
                                recordingMessages[recordingMessages.length - 1].remove();
                                recordingMessages[recordingMessages.length - 2].remove();
                            } else {
                                recordingMessages[recordingMessages.length - 1].remove();
                            }
                        }

                        // Add the transcribed text as a user message
                        addMessage(data.transcription, 'doctor');

                        // Show typing indicator while waiting for AI response
                        showTypingIndicator();
                    } else if (data.response) {
                        addMessage(data.response, 'assistant');
                    }
                };

                // Connection closed
                socket.onclose = function(event) {
                    updateConnectionStatus('disconnected', 'Disconnected');

                    // Disable chat controls
                    messageInput.disabled = true;
                    sendBtn.disabled = true;
                    micBtn.disabled = true;

                    // Reset connect button
                    connectBtn.disabled = false;
                    connectBtn.innerHTML = '<i class="fas fa-plug me-2"></i> Connect';

                    // Show reconnect message if it wasn't a normal closure
                    if (event.code !== 1000) {
                        addMessage('Connection lost. Please reconnect.', 'assistant');
                    }
                };

                // Connection error
                socket.onerror = function(error) {
                    console.error('WebSocket error:', error);
                    updateConnectionStatus('disconnected', 'Error');

                    // Reset connect button
                    connectBtn.disabled = false;
                    connectBtn.innerHTML = '<i class="fas fa-plug me-2"></i> Connect';

                    // Show error message
                    addMessage('Error connecting to server. Please check your connection and try again.', 'assistant');
                };
            } catch (error) {
                console.error('Error creating WebSocket:', error);
                updateConnectionStatus('disconnected', 'Error');

                // Reset connect button
                connectBtn.disabled = false;
                connectBtn.innerHTML = '<i class="fas fa-plug me-2"></i> Connect';

                // Show error message
                addMessage(`Error: ${error.message}`, 'assistant');
            }
        });

        // Send message
        function sendMessage() {
            const message = messageInput.value.trim();

            if (!message || !socket || socket.readyState !== WebSocket.OPEN) {
                return;
            }

            // Add message to chat
            addMessage(message, 'doctor');

            // Send message to server
            socket.send(JSON.stringify({
                text: message
            }));

            // Show typing indicator
            showTypingIndicator();

            // Clear input and focus
            messageInput.value = '';
            messageInput.focus();
        }

        // Send button click
        sendBtn.addEventListener('click', function() {
            sendMessage();
        });

        // Enter key to send
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Add message to chat
        function addMessage(text, sender) {
            const messageElement = document.createElement('div');
            messageElement.className = `message ${sender}`;

            // Format message text (handle markdown-like formatting)
            let formattedText = text
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/\n/g, '<br>');

            // Add patient info box for patient information
            if (sender === 'assistant' && text.includes('Patient Information')) {
                const patientInfoMatch = text.match(/## Patient Information([\s\S]*?)(?=##|$)/);
                if (patientInfoMatch) {
                    const patientInfo = patientInfoMatch[1].trim();
                    formattedText = formattedText.replace(/## Patient Information([\s\S]*?)(?=##|$)/,
                        '<div class="patient-info"><h4>Patient Information</h4><p>$1</p></div>');
                }
            }

            // Replace other markdown headers
            formattedText = formattedText.replace(/## (.*?)(?=\n|$)/g, '<strong>$1</strong>');

            messageElement.innerHTML = formattedText;

            // Add timestamp
            const timeElement = document.createElement('div');
            timeElement.className = 'message-time';
            timeElement.textContent = new Date().toLocaleTimeString();
            messageElement.appendChild(timeElement);

            // Remove typing indicator if exists
            removeTypingIndicator();

            // Add to chat
            chatMessages.appendChild(messageElement);

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Show typing indicator
        function showTypingIndicator() {
            removeTypingIndicator(); // Remove existing indicator if any

            const typingElement = document.createElement('div');
            typingElement.className = 'typing-indicator';
            typingElement.id = 'typingIndicator';
            typingElement.innerHTML = 'AI is thinking <span></span><span></span><span></span>';

            chatMessages.appendChild(typingElement);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Remove typing indicator
        function removeTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        // Update connection status
        function updateConnectionStatus(status, text) {
            connectionStatus.textContent = text;

            // Remove all status classes
            statusDot.classList.remove('connected', 'disconnected', 'connecting');

            // Add appropriate class
            statusDot.classList.add(status);
        }

        // Microphone button
        micBtn.addEventListener('click', function() {
            if (isRecording) {
                stopRecording();
            } else {
                startRecording();
            }
        });

        // Start recording
        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];

                mediaRecorder.addEventListener('dataavailable', event => {
                    audioChunks.push(event.data);
                });

                mediaRecorder.addEventListener('stop', () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                    sendAudioToServer(audioBlob);

                    // Stop all tracks
                    stream.getTracks().forEach(track => track.stop());
                });

                // Start recording
                mediaRecorder.start();
                isRecording = true;

                // Update UI
                micBtn.classList.add('recording');
                micBtn.innerHTML = '<i class="fas fa-stop"></i>';

                // Add recording message
                addMessage('Recording audio...', 'doctor');

            } catch (error) {
                console.error('Error accessing microphone:', error);
                alert('Error accessing microphone: ' + error.message);
            }
        }

        // Stop recording
        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;

                // Update UI
                micBtn.classList.remove('recording');
                micBtn.innerHTML = '<i class="fas fa-microphone"></i>';

                // Show processing message
                addMessage('Processing audio...', 'doctor');
            }
        }

        // Send audio to server
        function sendAudioToServer(audioBlob) {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                addMessage('Error: Not connected to server', 'assistant');
                return;
            }

            // Convert blob to base64
            const reader = new FileReader();
            reader.onloadend = () => {
                const base64Audio = reader.result.split(',')[1];

                // Send to server
                socket.send(JSON.stringify({
                    audio: base64Audio
                }));

                // Show typing indicator
                showTypingIndicator();
            };
            reader.readAsDataURL(audioBlob);
        }
    </script>
</body>
</html>
