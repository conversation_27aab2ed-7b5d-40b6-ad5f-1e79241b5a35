# PowerShell script to test Docker setup and environment variables

Write-Host "=== DOCKER ENVIRONMENT TEST ===" -ForegroundColor Cyan

# Check if Dock<PERSON> is running
Write-Host "`nChecking Docker status..." -ForegroundColor Yellow
$dockerVersion = docker --version 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Docker is running: $dockerVersion" -ForegroundColor Green
} else {
    Write-Host "❌ Docker is not running or not installed" -ForegroundColor Red
    exit 1
}

# Check if .env file exists
if (-Not (Test-Path ".env")) {
    Write-Host "❌ .env file not found in current directory" -ForegroundColor Red
    exit 1
}

Write-Host "✅ .env file found" -ForegroundColor Green

# Check Docker image
$imageName = "prasha-chatbot"
$imageExists = docker images -q $imageName 2>$null

if ($imageExists) {
    Write-Host "✅ Docker image '$imageName' found" -ForegroundColor Green

    # Show image details
    Write-Host "`nImage details:" -ForegroundColor Cyan
    docker images $imageName --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
} else {
    Write-Host "⚠️  Docker image '$imageName' not found" -ForegroundColor Yellow
    Write-Host "💡 To build the image, run: docker build -t $imageName ." -ForegroundColor Yellow
}

# Test environment variables
Write-Host "`nTesting environment variables..." -ForegroundColor Yellow

$envContent = Get-Content ".env"
$envVars = @{}

foreach ($line in $envContent) {
    if ($line -match '^([^#=]+)=(.*)$') {
        $key = $matches[1].Trim()
        $value = $matches[2].Trim()
        $envVars[$key] = $value
    }
}

Write-Host "Found $($envVars.Count) environment variables" -ForegroundColor Green

# Check critical variables
$criticalVars = @(
    "azure_openai_api_key",
    "postgres_host",
    "postgres_username",
    "postgres_password",
    "HF_API_Key"
)

Write-Host "`nCritical variables check:" -ForegroundColor Cyan
foreach ($var in $criticalVars) {
    if ($envVars.ContainsKey($var) -and $envVars[$var] -ne "") {
        $maskedValue = $envVars[$var].Substring(0, [Math]::Min(10, $envVars[$var].Length)) + "..."
        Write-Host "  ✅ $var = $maskedValue" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $var = MISSING OR EMPTY" -ForegroundColor Red
    }
}

# Test container run (dry run)
Write-Host "`nTesting container configuration..." -ForegroundColor Yellow

if ($imageExists) {
    Write-Host "Testing container startup (will stop after 10 seconds)..." -ForegroundColor Yellow

    $testContainer = "prasha-test-container"

    # Stop and remove test container if exists
    docker stop $testContainer 2>$null | Out-Null
    docker rm $testContainer 2>$null | Out-Null

    # Run test container
    docker run -d --name $testContainer --env-file .env -p 8001:8000 $imageName | Out-Null

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Container started successfully" -ForegroundColor Green

        # Wait a bit
        Start-Sleep -Seconds 10

        # Check logs
        Write-Host "`nContainer logs:" -ForegroundColor Cyan
        docker logs $testContainer

        # Check if container is still running
        $containerStatus = docker ps --filter "name=$testContainer" --format "{{.Status}}"
        if ($containerStatus) {
            Write-Host "`n✅ Container is running: $containerStatus" -ForegroundColor Green
        } else {
            Write-Host "`n❌ Container stopped unexpectedly" -ForegroundColor Red
        }

        # Clean up
        docker stop $testContainer 2>$null | Out-Null
        docker rm $testContainer 2>$null | Out-Null
        Write-Host "Test container cleaned up" -ForegroundColor Gray

    } else {
        Write-Host "❌ Failed to start test container" -ForegroundColor Red
    }
} else {
    Write-Host "⚠️  Skipping container test - image not found" -ForegroundColor Yellow
}

Write-Host "`n=== TEST SUMMARY ===" -ForegroundColor Cyan
Write-Host "✅ Docker: Running" -ForegroundColor Green
Write-Host "✅ .env file: Found" -ForegroundColor Green

if ($imageExists) {
    Write-Host "✅ Image: Available" -ForegroundColor Green
} else {
    Write-Host "⚠️  Image: Not built" -ForegroundColor Yellow
}

Write-Host "`nNext steps:" -ForegroundColor Yellow
if (-not $imageExists) {
    Write-Host "  1. Build the Docker image: docker build -t $imageName ." -ForegroundColor White
}
Write-Host "  2. Run the container: .\run_docker.ps1" -ForegroundColor White
Write-Host "  3. Test the application: http://localhost:8000" -ForegroundColor White

Write-Host "`nTest completed!" -ForegroundColor Green
