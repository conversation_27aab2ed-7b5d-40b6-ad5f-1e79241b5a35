import React, { useState, useEffect, useRef, useCallback } from 'react';
import './ChatFinal3.css'; // Import the CSS file

const ChatFinal3 = () => {
    // UI State
    const [patientId, setPatientId] = useState('f31a95c6-76ef-4bb2-936c-b258285682d9');
    const [persona, setPersona] = useState('psychologist');
    const [voice, setVoice] = useState('default');
    const [chatVoice, setChatVoice] = useState('default'); // Voice selected in chat interface
    const [isConnected, setIsConnected] = useState(false);
    const [connectionStatusText, setConnectionStatusText] = useState('Disconnected');
    const [connectionStatusClass, setConnectionStatusClass] = useState('disconnected');
    const [statusMessage, setStatusMessage] = useState('Please connect to start chatting');
    const [isStatusBarFading, setIsStatusBarFading] = useState(false);
    const [messages, setMessages] = useState([]); // Stores all chat messages
    const [messageInput, setMessageInput] = useState('');
    const [isRecording, setIsRecording] = useState(false);
    const [patientInfo, setPatientInfo] = useState(null);

    // WebSocket and Audio State
    const socketRef = useRef(null);
    const mediaRecorderRef = useRef(null);
    const audioChunksRef = useRef([]);
    const currentAudioRef = useRef(null); // For playing full message audio
    const heartbeatIntervalRef = useRef(null);
    const reconnectAttemptsRef = useRef(0);
    const MAX_RECONNECT_ATTEMPTS = 5;

    // Streaming State
    const [currentStreamingMessageText, setCurrentStreamingMessageText] = useState('');
    const [isStreamingActive, setIsStreamingActive] = useState(false);
    const streamingAudioQueueRef = useRef([]);
    const isPlayingStreamingAudioRef = useRef(false);
    const completeAudioRef = useRef(null); // Stores complete audio for full playback button

    // Utility function to convert base64 to blob
    const base64ToBlob = (base64, mimeType) => {
        const byteCharacters = atob(base64);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        return new Blob([byteArray], { type: mimeType });
    };

    // Function to show status messages
    const showStatusMessage = useCallback((message, autoHide = true) => {
        setStatusMessage(message);
        setIsStatusBarFading(false);
        if (autoHide) {
            setTimeout(() => {
                setIsStatusBarFading(true);
            }, 3000);
        }
    }, []);

    // Function to scroll chat to bottom
    const scrollToBottom = useCallback(() => {
        // Use setTimeout to ensure the scroll happens after the DOM is updated
        setTimeout(() => {
            const chatMessagesDiv = document.getElementById('chatMessages');
            if (chatMessagesDiv) {
                chatMessagesDiv.scrollTop = chatMessagesDiv.scrollHeight;
            }
        }, 100);
    }, []);

    // 🧹 Function to clean JSON artifacts from streaming text
    const cleanStreamingText = useCallback((text) => {
        // MINIMAL cleaning - preserve ALL content including spaces
        return text; // Return exactly as received - no cleaning at all
    }, []);

    // 🚫 Function to check if text chunk should be skipped
    const shouldSkipTextChunk = useCallback((text) => {
        const cleanText = text.toLowerCase().trim();

        // Skip JSON metadata fields
        const jsonFields = [
            'extracted_keywords',
            'suggested_specialist',
            'current_persona',
            'response_id'
        ];

        // Skip if contains JSON field names
        if (jsonFields.some(field => cleanText.includes(field))) {
            return true;
        }

        // Skip if it's just JSON syntax
        if (/^[\{\}\[\],:"'\s]*$/.test(cleanText)) {
            return true;
        }

        return false;
    }, []);

    // 🌊 Function to start a streaming message
    const startStreamingMessage = useCallback(() => {
        if (isStreamingActive) {
            // Finish previous streaming message if any
            setMessages(prevMessages => prevMessages.map(msg =>
                msg.isStreaming ? { ...msg, isStreaming: false } : msg
            ));
        }

        setIsStreamingActive(true);
        setCurrentStreamingMessageText('');
        streamingAudioQueueRef.current = [];
        completeAudioRef.current = null; // Reset complete audio for new message

        setMessages(prevMessages => [
            ...prevMessages,
            {
                id: Date.now(), // Unique ID for key
                text: '',
                sender: 'ai',
                timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                isStreaming: true,
                keywords: [],
                audio: null // Will be set to completeAudioRef.current later
            }
        ]);
        scrollToBottom();
        console.log('🌊 Started streaming message');
    }, [isStreamingActive, scrollToBottom]);

    // 🌊 Function to append text to streaming message
    const appendToStreamingMessage = useCallback((text) => {
        if (!isStreamingActive || !text) return;

        if (shouldSkipTextChunk(text)) {
            console.log('🚫 Skipping text chunk:', text);
            return;
        }

        const cleanedText = cleanStreamingText(text);
        setMessages(prevMessages => {
            const lastMessage = prevMessages[prevMessages.length - 1];
            if (lastMessage && lastMessage.isStreaming) {
                return prevMessages.map((msg, index) =>
                    index === prevMessages.length - 1
                        ? { ...msg, text: msg.text + cleanedText }
                        : msg
                );
            } else {
                // This shouldn't happen if streaming_start is always received first
                // But as a fallback, if text comes before start, create a new streaming message
                startStreamingMessage();
                return [...prevMessages, {
                    id: Date.now(),
                    text: cleanedText,
                    sender: 'ai',
                    timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                    isStreaming: true,
                    keywords: [],
                    audio: null
                }];
            }
        });
        scrollToBottom();
    }, [isStreamingActive, shouldSkipTextChunk, cleanStreamingText, scrollToBottom, startStreamingMessage]);

    // 🎵 Function to play streaming audio
    const playStreamingAudio = useCallback((audioBase64) => {
        if (!audioBase64) return;

        isPlayingStreamingAudioRef.current = true;
        const audioBlob = base64ToBlob(audioBase64, 'audio/mpeg');
        const audioUrl = URL.createObjectURL(audioBlob);
        const audio = new Audio(audioUrl);

        audio.onended = () => {
            isPlayingStreamingAudioRef.current = false;
            URL.revokeObjectURL(audioUrl);

            // Play next audio in queue
            if (streamingAudioQueueRef.current.length > 0) {
                const nextAudio = streamingAudioQueueRef.current.shift();
                playStreamingAudio(nextAudio);
            }
        };

        audio.onerror = (e) => {
            console.error('🎵 Error playing streaming audio:', e);
            isPlayingStreamingAudioRef.current = false;
            URL.revokeObjectURL(audioUrl);
        };

        audio.play().catch(e => {
            console.error('🎵 Failed to play streaming audio:', e);
            isPlayingStreamingAudioRef.current = false;
            URL.revokeObjectURL(audioUrl);
        });
    }, [base64ToBlob]);

    // 🌊 Function to finish streaming message
    const finishStreamingMessage = useCallback((keywords = [], audio = null) => {
        setIsStreamingActive(false);
        setMessages(prevMessages => prevMessages.map(msg =>
            msg.isStreaming
                ? { ...msg, isStreaming: false, keywords: keywords, audio: audio }
                : msg
        ));
        completeAudioRef.current = null; // Clear complete audio after message is finalized
        scrollToBottom();
        console.log('🌊 Finished streaming message');
    }, [scrollToBottom]);

    // 🧹 Function to clean up streaming state
    const cleanupStreamingState = useCallback(() => {
        setIsStreamingActive(false);
        setCurrentStreamingMessageText('');
        streamingAudioQueueRef.current = [];
        isPlayingStreamingAudioRef.current = false;
        completeAudioRef.current = null;
        console.log('🧹 Streaming state cleaned up');
    }, []);

    // 🎵 Function to play complete audio
    const playCompleteAudio = useCallback((audioBase64) => {
        if (!audioBase64) return;

        // Stop current audio if playing
        if (currentAudioRef.current) {
            currentAudioRef.current.pause();
            currentAudioRef.current = null;
        }

        const audioBlob = base64ToBlob(audioBase64, 'audio/mpeg');
        const audioUrl = URL.createObjectURL(audioBlob);
        currentAudioRef.current = new Audio(audioUrl);

        currentAudioRef.current.onended = () => {
            URL.revokeObjectURL(audioUrl);
            currentAudioRef.current = null;
        };

        currentAudioRef.current.onerror = (e) => {
            console.error('🎵 Error playing complete audio:', e);
            URL.revokeObjectURL(audioUrl);
            currentAudioRef.current = null;
        };

        currentAudioRef.current.play().catch(e => {
            console.error('🎵 Failed to play complete audio:', e);
            URL.revokeObjectURL(audioUrl);
            currentAudioRef.current = null;
        });
    }, [base64ToBlob]);

    // Function to add regular message (used for user messages and non-streaming AI messages)
    const addMessage = useCallback((text, sender, keywords = [], audio = null) => {
        setMessages(prevMessages => [
            ...prevMessages,
            {
                id: Date.now(),
                text,
                sender,
                timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                keywords,
                audio
            }
        ]);
        scrollToBottom();
    }, [scrollToBottom]);

    // Update patient info
    const updatePatientInfo = useCallback((patientData) => {
        setPatientInfo(patientData);
    }, []);

    // Heartbeat functionality
    const startHeartbeat = useCallback(() => {
        if (heartbeatIntervalRef.current) {
            clearInterval(heartbeatIntervalRef.current);
        }
        heartbeatIntervalRef.current = setInterval(() => {
            if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
                socketRef.current.send(JSON.stringify({ type: 'ping' }));
                console.log('💓 Heartbeat ping sent');
            }
        }, 30000); // Send heartbeat every 30 seconds
    }, []);

    const stopHeartbeat = useCallback(() => {
        if (heartbeatIntervalRef.current) {
            clearInterval(heartbeatIntervalRef.current);
            heartbeatIntervalRef.current = null;
        }
    }, []);

    // Reconnection functionality
    const reconnectWebSocket = useCallback(() => {
        if (reconnectAttemptsRef.current >= MAX_RECONNECT_ATTEMPTS) {
            console.warn("🛑 Max reconnect attempts reached.");
            showStatusMessage('Connection failed. Please refresh the page.', false);
            return;
        }

        reconnectAttemptsRef.current++;
        console.log(`🔁 Attempting reconnect (${reconnectAttemptsRef.current})...`);
        showStatusMessage(`Reconnecting... (${reconnectAttemptsRef.current}/${MAX_RECONNECT_ATTEMPTS})`, false);

        setTimeout(() => {
            connectWebSocket(); // Trigger the same connection logic
        }, 2000 * reconnectAttemptsRef.current); // exponential backoff
    }, [showStatusMessage]);

    // Connect to WebSocket
    const connectWebSocket = useCallback(() => {
        if (!patientId) {
            alert('Please enter a patient ID');
            return;
        }
        if (!persona) {
            alert('Please select a healthcare provider');
            return;
        }

        // Update UI
        setIsConnected(true); // Indicate connection attempt
        setConnectionStatusText('Connecting...');
        setConnectionStatusClass('connecting');
        showStatusMessage('Connecting to server...', false);

        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            // Use localhost for development, or window.location.host for deployment
            const wsUrl = `${protocol}//localhost:8000/chat-final3/${patientId}/${persona}`;
            // const wsUrl = `${protocol}//${window.location.host}/chat-final3/${patientId}/${persona}`;

            // Close existing socket if any
            if (socketRef.current) {
                socketRef.current.close(1000, "Initiating new connection"); // 1000 = normal closure
                socketRef.current = null;
            }

            const ws = new WebSocket(wsUrl);
            socketRef.current = ws;

            ws.onopen = (event) => {
                console.log('🔌 WebSocket connected');
                reconnectAttemptsRef.current = 0;
                startHeartbeat();

                let selectedVoiceToSend = voice === 'default'
                    ? (persona === 'psychologist' ? 'onyx' : 'shimmer')
                    : voice;

                ws.send(JSON.stringify({
                    token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.Ij0BnOgMwKFwB7VOzRdL-qdpEXNF7YvZZ8R_c-vKbcM", // This should ideally come from a secure source or auth flow
                    voice: selectedVoiceToSend
                }));

                setConnectionStatusText('Connected');
                setConnectionStatusClass('connected');
                showStatusMessage(`Connected to ${persona} specialist`);
                setIsConnected(true);
                setChatVoice(voice); // Sync chat voice select

                setTimeout(() => {
                    document.getElementById('messageInput')?.focus();
                }, 100);
            };

            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);

                    if (data.type === 'ping') {
                        console.log('💓 Received server ping, sending pong');
                        ws.send(JSON.stringify({ "type": "pong" }));
                        return;
                    }
                    if (data.type === 'pong') {
                        console.log('💓 Heartbeat pong received');
                        return;
                    }
                    if (data.error) {
                        showStatusMessage(`Error: ${data.error}`, true);
                        return;
                    }
                    if (data.type === 'auth_success') {
                        console.log('✅ Authentication successful');
                        if (data.patient_info) {
                            updatePatientInfo(data.patient_info);
                        }
                        showStatusMessage(`Connected to ${persona} specialist - Ready to chat!`);
                        return;
                    }

                    // Streaming handlers
                    if (data.type === 'streaming_start') {
                        const voice = data.voice || 'unknown';
                        const specialistPersona = data.persona || persona;
                        console.log(`🌊 Streaming started - Persona: ${specialistPersona}, Voice: ${voice}`);
                        startStreamingMessage();
                        return;
                    }
                    if (data.type === 'streaming_text') {
                        appendToStreamingMessage(data.text);
                        return;
                    }
                    if (data.type === 'streaming_audio') {
                        if (streamingAudioQueueRef.current.length > 100) {
                            streamingAudioQueueRef.current.shift();
                            console.warn('⚠️ Audio queue full, dropping oldest audio');
                        }
                        if (isPlayingStreamingAudioRef.current) {
                            streamingAudioQueueRef.current.push(data.audio);
                        } else {
                            playStreamingAudio(data.audio);
                        }
                        return;
                    }
                    if (data.type === 'complete_audio') {
                        completeAudioRef.current = data.audio;
                        console.log('🎵 Received complete audio for full playback', {
                            audioLength: data.audio ? data.audio.length : 0,
                            textLength: data.text ? data.text.length : 0
                        });
                        console.log('🎵 Complete audio stored for manual playback only - NOT auto-playing');
                        return;
                    }
                    if (data.type === 'streaming_complete') {
                        console.log('🌊 Streaming completed');
                        setTimeout(() => {
                            console.log('🎵 Finishing streaming with audio:', {
                                hasCompleteAudio: !!completeAudioRef.current,
                                completeAudioLength: completeAudioRef.current ? completeAudioRef.current.length : 0
                            });
                            finishStreamingMessage(
                                data.extracted_keywords || [],
                                completeAudioRef.current || null
                            );
                            cleanupStreamingState();
                        }, 50);
                        return;
                    }
                    if (data.type === 'streaming_error') {
                        finishStreamingMessage();
                        showStatusMessage(`Streaming error: ${data.error}`, true);
                        cleanupStreamingState();
                        return;
                    }

                    if (data.type === 'transcription') {
                        const transcription = data.text.trim();
                        if (transcription) {
                            addMessage(transcription, 'user');
                            setMessageInput('');
                        }
                        return;
                    }
                    if (data.type === 'error') {
                        showStatusMessage(`Error: ${data.message}`, true);
                        return;
                    }
                    if (data.processing_time) {
                        console.log(`⏱️ Request processed in: ${data.processing_time}`);
                    }
                } catch (error) {
                    console.error('❌ Error parsing message:', error);
                    console.error('Raw message:', event.data);
                    showStatusMessage(`Message processing error: ${error.message}`);
                    if (isStreamingActive) {
                        cleanupStreamingState();
                    }
                }
            };

            ws.onclose = (event) => {
                console.log('🔌 WebSocket disconnected:', event.code, event.reason);
                setConnectionStatusText('Disconnected');
                setConnectionStatusClass('disconnected');
                stopHeartbeat();
                setIsConnected(false); // Update UI state
                if (isStreamingActive) {
                    cleanupStreamingState();
                }

                if (event.code !== 1000 && event.code !== 1001) { // 1000 = normal closure, 1001 = going away (browser closing)
                    showStatusMessage('Connection lost. Attempting to reconnect...', false);
                    reconnectWebSocket();
                } else {
                    showStatusMessage('Disconnected from server');
                }
            };

            ws.onerror = (error) => {
                console.error('🔌 WebSocket error:', error);
                setConnectionStatusText('Connection Error');
                setConnectionStatusClass('disconnected');
                showStatusMessage('Connection error occurred', true);
            };

        } catch (error) {
            console.error('❌ Error creating WebSocket:', error);
            setConnectionStatusText('Connection Failed');
            setConnectionStatusClass('disconnected');
            showStatusMessage('Failed to create connection', true);
        }
    }, [patientId, persona, voice, showStatusMessage, startHeartbeat, updatePatientInfo, startStreamingMessage,
        appendToStreamingMessage, playStreamingAudio, finishStreamingMessage, cleanupStreamingState,
        addMessage, reconnectWebSocket, isStreamingActive]);

    // Send message function
    const sendMessage = useCallback(() => {
        const message = messageInput.trim();
        if (!message || !socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
            return;
        }

        addMessage(message, 'user');

        let selectedVoiceToSend = chatVoice === 'default'
            ? (persona === 'psychologist' ? 'onyx' : 'shimmer')
            : chatVoice;

        socketRef.current.send(JSON.stringify({
            text: message,
            voice: selectedVoiceToSend
        }));

        setMessageInput('');
    }, [messageInput, chatVoice, persona, addMessage]);

    // Audio recording functionality
    const startRecording = useCallback(async () => {
        if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
            showStatusMessage('Please connect to the server first');
            return;
        }

        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            const recorder = new MediaRecorder(stream);
            mediaRecorderRef.current = recorder;
            audioChunksRef.current = [];

            recorder.ondataavailable = (event) => {
                audioChunksRef.current.push(event.data);
            };

            recorder.onstop = () => {
                const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
                const reader = new FileReader();
                reader.onloadend = () => {
                    const base64Audio = reader.result.split(',')[1];

                    let selectedVoiceToSend = chatVoice === 'default'
                        ? (persona === 'psychologist' ? 'onyx' : 'shimmer')
                        : chatVoice;

                    socketRef.current.send(JSON.stringify({
                        audio: base64Audio,
                        voice: selectedVoiceToSend
                    }));
                };
                reader.readAsDataURL(audioBlob);
                stream.getTracks().forEach(track => track.stop());
            };

            recorder.start();
            setIsRecording(true);
            showStatusMessage('Recording... Click to stop');
        } catch (error) {
            console.error('Error starting recording:', error);
            showStatusMessage('Error accessing microphone', true);
        }
    }, [chatVoice, persona, showStatusMessage]);

    const stopRecording = useCallback(() => {
        if (mediaRecorderRef.current && isRecording) {
            mediaRecorderRef.current.stop();
            setIsRecording(false);
            showStatusMessage('Processing audio...');
        }
    }, [isRecording, showStatusMessage]);

    // Update voice helper text based on current persona
    const getVoiceHelperText = useCallback(() => {
        const defaultVoice = persona === 'psychologist' ? 'Onyx (Male, Deep)' : 'Shimmer (Female, Clear)';
        const specialistName = persona === 'psychologist' ? 'Psychologist' : 'Dietician';
        return `Default: ${specialistName} uses ${defaultVoice}`;
    }, [persona]);

    // Effect for handling component unmount (cleanup WebSocket)
    useEffect(() => {
        return () => {
            if (socketRef.current) {
                console.log('🔌 Closing WebSocket on component unmount.');
                socketRef.current.close(1000, "Component unmounted"); // 1000 = normal closure
            }
            stopHeartbeat();
            if (currentAudioRef.current) {
                currentAudioRef.current.pause();
                currentAudioRef.current = null;
            }
        };
    }, [stopHeartbeat]); // Dependency ensures cleanup runs when stopHeartbeat is stable

    return (
        <div className="container-fluid d-flex flex-column h-100 py-3">
            {!isConnected ? (
                <div id="loginForm" className="login-container">
                    <h2><i className="fas fa-hospital-user me-2"></i> Healthcare Chat - Specialized Care</h2>
                    <div className="mb-3">
                        <label htmlFor="patientId" className="form-label">Patient ID</label>
                        <input
                            type="text"
                            className="form-control"
                            id="patientId"
                            value={patientId}
                            onChange={(e) => setPatientId(e.target.value)}
                        />
                    </div>
                    <div className="mb-3">
                        <label htmlFor="personaSelect" className="form-label">Select Healthcare Provider</label>
                        <select
                            className="form-select"
                            id="personaSelect"
                            value={persona}
                            onChange={(e) => setPersona(e.target.value)}
                        >
                            <option value="psychologist">Psychologist (Dr. Ori)</option>
                            <option value="dietician">Dietician (Dr. Maya)</option>
                        </select>
                    </div>
                    <div className="mb-3">
                        <label htmlFor="voiceSelect" className="form-label">AI Voice</label>
                        <select
                            className="form-select"
                            id="voiceSelect"
                            value={voice}
                            onChange={(e) => setVoice(e.target.value)}
                        >
                            <option value="default">Default (Auto-select by specialist)</option>
                            <option value="nova">Nova (Female, Soft)</option>
                            <option value="alloy">Alloy (Neutral)</option>
                            <option value="echo">Echo (Male)</option>
                            <option value="fable">Fable (Female)</option>
                            <option value="onyx">Onyx (Male, Deep)</option>
                            <option value="shimmer">Shimmer (Female, Clear)</option>
                        </select>
                        <small className="form-text text-muted">
                            {getVoiceHelperText()}
                        </small>
                    </div>
                    <div className="alert alert-info mb-3">
                        <i className="fas fa-info-circle me-2"></i>
                        <strong>Specialized Healthcare Chat:</strong> Connect directly with your chosen healthcare specialist for personalized care.
                    </div>
                    <button id="connectBtn" className="btn btn-primary w-100" onClick={connectWebSocket}>
                        {isConnected ? (
                            <>
                                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Connecting...
                            </>
                        ) : 'Connect'}
                    </button>
                </div>
            ) : (
                <div id="chatInterface" className="chat-container">
                    <div className="chat-header">
                        <h2>
                            <i className="fas fa-hospital-user me-2"></i> Healthcare Chat
                            <span id="personaIndicator" className={`persona-indicator persona-${persona}`}>
                                {persona === 'psychologist' ? 'Psychologist (Dr. Ori)' : 'Dietician (Dr. Maya)'}
                            </span>
                        </h2>
                        <div className="d-flex align-items-center">
                            <span id="connectionStatus" className={connectionStatusClass}>{connectionStatusText}</span>
                        </div>
                    </div>
                    <div className={`status-bar ${isStatusBarFading ? 'fade-out' : ''}`}>
                        <div id="statusMessage">{statusMessage}</div>
                    </div>
                    <div className="chat-body">
                        <div className="sidebar">
                            <div className="patient-info">
                                <h3>Patient Information</h3>
                                <div id="patientInfoContent">
                                    {!patientInfo ? (
                                        <div className="d-flex justify-content-center align-items-center h-100">
                                            <div className="spinner-border text-primary" role="status">
                                                <span className="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    ) : (
                                        <>
                                            {patientInfo.patient && (
                                                <div className="info-section">
                                                    <h4>Basic Info</h4>
                                                    <div className="info-item">
                                                        <strong>Name:</strong> {patientInfo.patient.name || 'N/A'}
                                                    </div>
                                                    <div className="info-item">
                                                        <strong>Age:</strong> {patientInfo.patient.age || 'N/A'}
                                                    </div>
                                                    <div className="info-item">
                                                        <strong>Gender:</strong> {patientInfo.patient.gender || 'N/A'}
                                                    </div>
                                                </div>
                                            )}
                                            {patientInfo.medical_history && patientInfo.medical_history.length > 0 && (
                                                <div className="info-section">
                                                    <h4>Medical History</h4>
                                                    {patientInfo.medical_history.slice(0, 3).map((mh, index) => (
                                                        <div key={index} className="info-item">
                                                            <strong>{mh.condition || 'Condition'}:</strong> {mh.notes || 'N/A'}
                                                            {mh.diagnosis_date && <div className="text-muted small">{mh.diagnosis_date}</div>}
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                            {patientInfo.onboarding_questions && patientInfo.onboarding_questions.length > 0 && (
                                                <div className="info-section">
                                                    <h4>Health Information</h4>
                                                    {patientInfo.onboarding_questions.slice(0, 3).map((q, index) => (
                                                        <div key={index} className="info-item">
                                                            <strong>Q:</strong> {q.question || 'N/A'}
                                                            <div><strong>A:</strong> {q.answer || 'N/A'}</div>
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                            {patientInfo.emotion_analysis && patientInfo.emotion_analysis.length > 0 && (
                                                <div className="info-section">
                                                    <h4>Recent Mood</h4>
                                                    {patientInfo.emotion_analysis.slice(0, 2).map((e, index) => (
                                                        <div key={index} className="info-item">
                                                            <strong>Emotion:</strong> {e.dominant_emotion || 'N/A'}
                                                            <div>Confidence: {e.confidence ? (e.confidence * 100).toFixed(1) + '%' : 'N/A'}</div>
                                                            {e.created_at && <div className="text-muted small">{new Date(e.created_at).toLocaleDateString()}</div>}
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                            {(!patientInfo.patient && !patientInfo.medical_history?.length && !patientInfo.onboarding_questions?.length && !patientInfo.emotion_analysis?.length) && (
                                                <div className="text-center text-muted">No patient information available</div>
                                            )}
                                        </>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="chat-messages" id="chatMessages">
                            {messages.map((msg) => (
                                <div key={msg.id} className={`message ${msg.sender === 'user' ? 'user-message' : 'ai-message'} ${msg.isStreaming ? 'streaming-message' : ''}`}>
                                    <div className="message-text">
                                        {msg.text}
                                    </div>
                                    <div className="message-time">{msg.timestamp}</div>
                                    {msg.isStreaming && (
                                        <div className="streaming-indicator">
                                            <i className="fas fa-circle-notch fa-spin"></i> Generating response...
                                        </div>
                                    )}
                                    {msg.keywords && msg.keywords.length > 0 && (
                                        <div className="message-keywords">
                                            {msg.keywords.map((keyword, index) => (
                                                <span key={index} className="keyword-tag">{keyword}</span>
                                            ))}
                                        </div>
                                    )}
                                    {msg.audio && (
                                        <div className="audio-controls">
                                            <button className="audio-btn" onClick={() => playCompleteAudio(msg.audio)}>
                                                <i className="fas fa-play"></i>
                                            </button>
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                    <div className="chat-input">
                        <input
                            type="text"
                            id="messageInput"
                            placeholder="Type your message here..."
                            autoComplete="off"
                            value={messageInput}
                            onChange={(e) => setMessageInput(e.target.value)}
                            onKeyPress={(e) => { if (e.key === 'Enter') sendMessage(); }}
                        />
                        <select
                            id="chatVoiceSelect"
                            className="voice-select"
                            title="Select AI voice"
                            value={chatVoice}
                            onChange={(e) => setChatVoice(e.target.value)}
                        >
                            <option value="default">Default</option>
                            <option value="nova">Nova</option>
                            <option value="alloy">Alloy</option>
                            <option value="echo">Echo</option>
                            <option value="fable">Fable</option>
                            <option value="onyx">Onyx</option>
                            <option value="shimmer">Shimmer</option>
                        </select>
                        <button id="micBtn" className={`btn-mic ${isRecording ? 'recording' : ''}`} title="Record audio" onClick={isRecording ? stopRecording : startRecording}>
                            <i className={`fas ${isRecording ? 'fa-stop' : 'fa-microphone'}`}></i>
                        </button>
                        <button id="sendBtn" className="btn-send" title="Send message" onClick={sendMessage}>
                            <i className="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ChatFinal3;