import React from 'react';
import ChatFinal3 from './ChatFinal3';

// Example usage of the ChatFinal3 component
const ChatFinal3Example = () => {
  return (
    <div style={{ height: '100vh', backgroundColor: '#f8f9fa' }}>
      <ChatFinal3
        patientId="f31a95c6-76ef-4bb2-936c-b258285682d9"
        persona="psychologist" // or "dietician"
        token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.Ij0BnOgMwKFwB7VOzRdL-qdpEXNF7YvZZ8R_c-vKbcM"
        conversationId={null} // Optional: pass existing conversation ID
      />
    </div>
  );
};

export default ChatFinal3Example;

// Alternative usage examples:

// 1. With existing conversation
const ExistingConversationExample = () => {
  return (
    <ChatFinal3
      patientId="f31a95c6-76ef-4bb2-936c-b258285682d9"
      persona="dietician"
      token="your-jwt-token"
      conversationId="550e8400-e29b-41d4-a716-446655440000"
    />
  );
};

// 2. With dynamic props from parent component
const DynamicChatExample = ({ patientData, selectedPersona, authToken }) => {
  return (
    <ChatFinal3
      patientId={patientData.id}
      persona={selectedPersona}
      token={authToken}
      conversationId={patientData.lastConversationId}
    />
  );
};

// 3. Full-screen chat interface
const FullScreenChatExample = () => {
  return (
    <div style={{ 
      position: 'fixed', 
      top: 0, 
      left: 0, 
      right: 0, 
      bottom: 0, 
      backgroundColor: '#f8f9fa' 
    }}>
      <ChatFinal3
        patientId="f31a95c6-76ef-4bb2-936c-b258285682d9"
        persona="psychologist"
        token="your-jwt-token"
      />
    </div>
  );
};
