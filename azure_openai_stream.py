import os
from openai import AzureOpenAI

endpoint = "https://mentalhealth-bot.openai.azure.com/"
model_name = "gpt-4o"
deployment = "gpt-4o"

subscription_key = "7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL"
api_version = "2024-12-01-preview"

client = AzureOpenAI(
    api_version=api_version,
    azure_endpoint=endpoint,
    api_key=subscription_key,
)

response = client.chat.completions.create(
    stream=True,
    messages=[
        {
            "role": "system",
            "content": "You are a helpful assistant.",
        },
        {
            "role": "user",
            "content": "I am going to Paris, what should I see?",
        }
    ],
    max_tokens=4096,
    temperature=1.0,
    top_p=1.0,
    model=deployment,
)

print("Streaming response:", response)

for update in response:
    if update.choices:
        print(update.choices[0].delta.content or "", end="")

client.close()
# Request JSON-formatted response
# response = client.chat.completions.create(
#     model=deployment,
#     stream=True,
#     response_format={"type": "json_object"},  # ✅ fix here
#     messages=[
#         {"role": "system", "content": "You are a JSON API that returns travel suggestions."},
#         {"role": "user", "content": "Give me a JSON list of top 3 places to visit in Paris."},
#     ],
#     temperature=0.7,
#     max_tokens=1000
# )

# # Capture streamed response — only one chunk should contain the full JSON
# output = ""
# for chunk in response:
#     if chunk.choices and chunk.choices[0].delta.content:
#         output += chunk.choices[0].delta.content

# print("\n✅ JSON Output:\n", output)

# client.close()