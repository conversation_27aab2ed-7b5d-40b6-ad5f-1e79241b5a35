<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatFinal3 React Component Test</title>
    
    <!-- React and Babel for JSX -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Our custom CSS -->
    <link rel="stylesheet" href="/static/ChatFinal3.css">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .test-header {
            background-color: #343a40;
            color: white;
            padding: 10px 20px;
            text-align: center;
        }
        
        .test-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading-message {
            text-align: center;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="test-container">
            <div class="test-header">
                <h3><i class="fas fa-flask me-2"></i>ChatFinal3 React Component Test</h3>
                <small>Testing the converted React JSX component with chat_final3.py service</small>
            </div>
            <div class="test-content">
                <div class="loading-message">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div>Loading React Component...</div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;

        // ChatFinal3 Component (Embedded for testing)
        const ChatFinal3 = ({ 
          patientId = "f31a95c6-76ef-4bb2-936c-b258285682d9", 
          persona = "psychologist",
          token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.Ij0BnOgMwKFwB7VOzRdL-qdpEXNF7YvZZ8R_c-vKbcM",
          conversationId = null
        }) => {
          // State variables
          const [socket, setSocket] = useState(null);
          const [isConnected, setIsConnected] = useState(false);
          const [connectionStatus, setConnectionStatus] = useState('Disconnected');
          const [messages, setMessages] = useState([]);
          const [messageInput, setMessageInput] = useState('');
          const [isRecording, setIsRecording] = useState(false);
          const [patientInfo, setPatientInfo] = useState(null);
          const [statusMessage, setStatusMessage] = useState('Ready to connect');
          const [selectedVoice, setSelectedVoice] = useState('nova');
          const [currentPersona, setCurrentPersona] = useState(persona);
          
          // Streaming state
          const [isStreamingActive, setIsStreamingActive] = useState(false);
          const [currentStreamingMessage, setCurrentStreamingMessage] = useState(null);
          const [streamingAudioQueue, setStreamingAudioQueue] = useState([]);
          const [isPlayingStreamingAudio, setIsPlayingStreamingAudio] = useState(false);
          const [completeAudio, setCompleteAudio] = useState(null);

          // Refs
          const chatMessagesRef = useRef(null);
          const mediaRecorderRef = useRef(null);
          const audioChunksRef = useRef([]);
          const currentAudioRef = useRef(null);
          const heartbeatIntervalRef = useRef(null);
          const reconnectAttemptsRef = useRef(0);

          // Constants
          const MAX_RECONNECTS = 5;
          const VOICE_OPTIONS = [
            { value: 'nova', label: 'Nova (Female, Soft)' },
            { value: 'alloy', label: 'Alloy (Neutral)' },
            { value: 'echo', label: 'Echo (Male)' },
            { value: 'fable', label: 'Fable (Female)' },
            { value: 'onyx', label: 'Onyx (Male, Deep)' },
            { value: 'shimmer', label: 'Shimmer (Female, Clear)' }
          ];

          // Auto-connect on component mount
          useEffect(() => {
            connectToWebSocket();
            return () => {
              if (socket) {
                socket.close();
              }
              stopHeartbeat();
            };
          }, [patientId, persona]);

          // Scroll to bottom when messages change
          useEffect(() => {
            scrollToBottom();
          }, [messages]);

          // Utility functions
          const scrollToBottom = () => {
            setTimeout(() => {
              if (chatMessagesRef.current) {
                chatMessagesRef.current.scrollTop = chatMessagesRef.current.scrollHeight;
              }
            }, 100);
          };

          const showStatusMessage = (message, autoHide = true) => {
            setStatusMessage(message);
            if (autoHide) {
              setTimeout(() => {
                setStatusMessage('');
              }, 3000);
            }
          };

          const base64ToBlob = (base64, mimeType) => {
            const byteCharacters = atob(base64);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            return new Blob([byteArray], { type: mimeType });
          };

          // Heartbeat functionality
          const startHeartbeat = () => {
            if (heartbeatIntervalRef.current) {
              clearInterval(heartbeatIntervalRef.current);
            }
            heartbeatIntervalRef.current = setInterval(() => {
              if (socket && socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({ type: 'ping' }));
                console.log('💓 Heartbeat ping sent');
              }
            }, 30000);
          };

          const stopHeartbeat = () => {
            if (heartbeatIntervalRef.current) {
              clearInterval(heartbeatIntervalRef.current);
              heartbeatIntervalRef.current = null;
            }
          };

          // WebSocket connection
          const connectToWebSocket = () => {
            if (socket) {
              socket.close();
            }

            setConnectionStatus('Connecting...');
            showStatusMessage('Connecting to server...', false);

            try {
              const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
              const wsUrl = `${protocol}//${window.location.host}/chat-final3/${patientId}/${currentPersona}`;
              
              const newSocket = new WebSocket(wsUrl);

              newSocket.onopen = (event) => {
                console.log('🔌 WebSocket connected');
                setSocket(newSocket);
                setIsConnected(true);
                setConnectionStatus('Connected');
                reconnectAttemptsRef.current = 0;
                startHeartbeat();

                // Send authentication
                const authData = {
                  token: token,
                  voice: selectedVoice
                };
                
                if (conversationId) {
                  authData.conversation_id = conversationId;
                }

                newSocket.send(JSON.stringify(authData));
                showStatusMessage(`Connected to ${currentPersona} specialist`);
              };

              newSocket.onmessage = (event) => {
                try {
                  const data = JSON.parse(event.data);
                  handleWebSocketMessage(data);
                } catch (error) {
                  console.error('❌ Error parsing message:', error);
                  showStatusMessage(`Message processing error: ${error.message}`);
                }
              };

              newSocket.onclose = (event) => {
                console.log('🔌 WebSocket disconnected:', event.code, event.reason);
                setSocket(null);
                setIsConnected(false);
                setConnectionStatus('Disconnected');
                stopHeartbeat();

                if (event.code !== 1000 && reconnectAttemptsRef.current < MAX_RECONNECTS) {
                  reconnectAttemptsRef.current++;
                  showStatusMessage(`Reconnecting... (${reconnectAttemptsRef.current}/${MAX_RECONNECTS})`, false);
                  setTimeout(() => {
                    connectToWebSocket();
                  }, 2000 * reconnectAttemptsRef.current);
                } else if (event.code !== 1000) {
                  showStatusMessage('Connection failed. Please refresh the page.', false);
                } else {
                  showStatusMessage('Disconnected from server');
                }
              };

              newSocket.onerror = (error) => {
                console.error('🔌 WebSocket error:', error);
                setConnectionStatus('Connection Error');
                showStatusMessage('Connection error occurred', true);
              };

            } catch (error) {
              console.error('❌ Error creating WebSocket:', error);
              setConnectionStatus('Connection Failed');
              showStatusMessage('Failed to create connection', true);
            }
          };

          // Handle WebSocket messages (simplified for testing)
          const handleWebSocketMessage = (data) => {
            console.log('📨 Received message:', data);

            if (data.error) {
              console.error('❌ Server error:', data.error);
              showStatusMessage(`Server error: ${data.error}`, true);
              return;
            }

            if (data.type === 'auth_success') {
              console.log('✅ Authentication successful');
              if (data.patient_info) {
                setPatientInfo(data.patient_info);
              }
              showStatusMessage(`Connected to ${currentPersona} specialist - Ready to chat!`);
              return;
            }

            if (data.type === 'conversation_history') {
              console.log('📜 Loading conversation history');
              if (data.messages && data.messages.length > 0) {
                const historyMessages = data.messages.map((msg, index) => ({
                  id: `history-${index}`,
                  text: msg.message,
                  sender: msg.sender === 'user' ? 'user' : 'ai',
                  timestamp: new Date(msg.timestamp),
                  isStreaming: false,
                  keywords: [],
                  audio: null
                }));
                setMessages(historyMessages);
              }
              return;
            }

            if (data.type === 'streaming_start') {
              console.log('🌊 Streaming started');
              const newMessage = {
                id: Date.now(),
                text: '',
                sender: 'ai',
                timestamp: new Date(),
                isStreaming: true,
                keywords: [],
                audio: null
              };
              setMessages(prev => [...prev, newMessage]);
              return;
            }

            if (data.type === 'streaming_text') {
              console.log('📝 Streaming text:', data.text);
              setMessages(prev => prev.map(msg =>
                msg.isStreaming ? { ...msg, text: msg.text + data.text } : msg
              ));
              return;
            }

            if (data.type === 'streaming_complete') {
              console.log('✅ Streaming complete');
              setMessages(prev => prev.map(msg =>
                msg.isStreaming ? { ...msg, isStreaming: false } : msg
              ));
              return;
            }

            if (data.type === 'transcription') {
              console.log('🎤 Transcription received:', data.text);
              const transcription = data.text.trim();
              if (transcription) {
                const newMessage = {
                  id: Date.now(),
                  text: transcription,
                  sender: 'user',
                  timestamp: new Date(),
                  isStreaming: false,
                  keywords: [],
                  audio: null
                };
                setMessages(prev => [...prev, newMessage]);
                setMessageInput('');
              }
              return;
            }

            // Log unhandled message types
            console.log('🔍 Unhandled message type:', data.type);
          };

          // Send message function
          const sendMessage = () => {
            const message = messageInput.trim();
            if (!message || !socket || socket.readyState !== WebSocket.OPEN) {
              return;
            }

            // Add user message to chat
            const newMessage = {
              id: Date.now(),
              text: message,
              sender: 'user',
              timestamp: new Date(),
              isStreaming: false,
              keywords: [],
              audio: null
            };

            setMessages(prev => [...prev, newMessage]);

            // Send to server
            socket.send(JSON.stringify({
              text: message,
              voice: selectedVoice
            }));

            // Clear input
            setMessageInput('');
          };

          // Handle key press
          const handleKeyPress = (e) => {
            if (e.key === 'Enter') {
              sendMessage();
            }
          };

          // Render message
          const renderMessage = (message) => {
            const isUser = message.sender === 'user';

            return (
              <div
                key={message.id}
                className={`message ${isUser ? 'user-message' : 'ai-message'}`}
              >
                <div>{message.text}</div>
                <div className="message-time">
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </div>
              </div>
            );
          };

          return (
            <div className="chat-container">
              {/* Header */}
              <div className="chat-header">
                <h2>
                  <i className="fas fa-hospital-user me-2"></i> Healthcare Chat Test
                  <span className={`persona-indicator persona-${currentPersona}`}>
                    {currentPersona === 'psychologist' ? 'Psychologist (Dr. Ori)' : 'Dietician (Dr. Maya)'}
                  </span>
                </h2>
                <div className="d-flex align-items-center">
                  <span className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
                    {connectionStatus}
                  </span>
                </div>
              </div>

              {/* Status Bar */}
              {statusMessage && (
                <div className="status-bar">
                  <div>{statusMessage}</div>
                </div>
              )}

              {/* Chat Body */}
              <div className="chat-body">
                {/* Sidebar */}
                <div className="sidebar">
                  <div className="patient-info">
                    <h3>Patient Information</h3>
                    <div className="patient-info-content">
                      {!patientInfo ? (
                        <div className="d-flex justify-content-center align-items-center h-100">
                          <div className="spinner-border text-primary" role="status">
                            <span className="visually-hidden">Loading...</span>
                          </div>
                        </div>
                      ) : (
                        <div className="info-section">
                          <h4>Test Patient</h4>
                          <div className="info-item">
                            <strong>ID:</strong> {patientId}
                          </div>
                          <div className="info-item">
                            <strong>Status:</strong> Connected
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Messages */}
                <div className="chat-messages" ref={chatMessagesRef}>
                  {messages.length === 0 ? (
                    <div style={{ textAlign: 'center', color: '#6c757d', marginTop: '50px' }}>
                      <i className="fas fa-comments fa-3x mb-3"></i>
                      <div>Start a conversation with your healthcare specialist</div>
                    </div>
                  ) : (
                    messages.map(message => renderMessage(message))
                  )}
                </div>
              </div>

              {/* Input */}
              <div className="chat-input">
                <input
                  type="text"
                  value={messageInput}
                  onChange={(e) => setMessageInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message here..."
                  disabled={!isConnected}
                />
                
                <select
                  value={selectedVoice}
                  onChange={(e) => setSelectedVoice(e.target.value)}
                  className="voice-select"
                  title="Select AI voice"
                >
                  {VOICE_OPTIONS.map(voice => (
                    <option key={voice.value} value={voice.value}>
                      {voice.value}
                    </option>
                  ))}
                </select>

                <button
                  className="btn-send"
                  onClick={sendMessage}
                  disabled={!isConnected || !messageInput.trim()}
                  title="Send message"
                >
                  <i className="fas fa-paper-plane"></i>
                </button>
              </div>
            </div>
          );
        };

        // Test App Component
        const TestApp = () => {
          return (
            <div style={{ height: '100vh' }}>
              <ChatFinal3
                patientId="f31a95c6-76ef-4bb2-936c-b258285682d9"
                persona="psychologist"
                token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.Ij0BnOgMwKFwB7VOzRdL-qdpEXNF7YvZZ8R_c-vKbcM"
                conversationId={null}
              />
            </div>
          );
        };

        // Render the app
        ReactDOM.render(<TestApp />, document.getElementById('root'));
    </script>
</body>
</html>
