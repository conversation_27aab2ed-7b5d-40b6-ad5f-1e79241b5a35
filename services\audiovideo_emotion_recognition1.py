import os
import base64
import uuid
import torch
import librosa
import requests
import numpy as np
import warnings
from datetime import datetime, timezone
from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel
from typing import List, Optional
from dotenv import load_dotenv
from collections import defaultdict
from transformers import AutoFeatureExtractor, AutoModelForAudioClassification
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Disable HuggingFace symlinks warning on Windows
os.environ["HF_HUB_DISABLE_SYMLINKS_WARNING"] = "1"

# Suppress specific warnings
warnings.filterwarnings("ignore", category=FutureWarning, module="librosa")
warnings.filterwarnings("ignore", message="Some weights of.*were not initialized.*")


# --- Load Environment Variables ---
load_dotenv()
HF_TOKEN = os.getenv("HF_API_Key2")
HF_API_URL = os.getenv("HF_URL_VISION") or "https://api-inference.huggingface.co/models/trpakov/vit-face-expression"
# HF_AUDIO_URL = os.getenv("Audio_HF_URL")
HF_IMAGE_DESCRIP_URL = os.getenv("Image_description_url")
NVIDIA_SCOUT_API = os.getenv("NVIDIA_LLAMA4_SCOUT_API_KEY")

# === GLOBAL MODEL CACHE ===
# Load models once at startup and reuse them
_cached_audio_model = None
_cached_audio_feature_extractor = None
_model_loading_lock = False

def initialize_audio_emotion_model():
    """Initialize audio emotion model at application startup"""
    global _cached_audio_model, _cached_audio_feature_extractor, _model_loading_lock

    if _cached_audio_model is not None and _cached_audio_feature_extractor is not None:
        logger.info("✅ Audio emotion model already initialized")
        return True

    try:
        _model_loading_lock = True

        # Use the specific downloaded model
        model_id = "ehcalabres/wav2vec2-lg-xlsr-en-speech-emotion-recognition"
        logger.info(f"🤖 Loading audio emotion model: {model_id}")

        # Load feature extractor from cache
        logger.info("📥 Loading feature extractor from cache...")
        feature_extractor = AutoFeatureExtractor.from_pretrained(
            model_id,
            cache_dir=os.path.join(os.getcwd(), "model_cache"),
            local_files_only=True,  # Only use cached files
            trust_remote_code=True
        )
        logger.info("✅ Feature extractor loaded successfully")
        logger.info(f"📊 Sampling rate: {feature_extractor.sampling_rate}Hz")

        # Load model from cache
        logger.info("📥 Loading emotion classification model from cache...")
        model = AutoModelForAudioClassification.from_pretrained(
            model_id,
            cache_dir=os.path.join(os.getcwd(), "model_cache"),
            local_files_only=True,  # Only use cached files
            trust_remote_code=True
        )
        logger.info("✅ Emotion classification model loaded successfully")

        # Verify model has the expected emotion labels
        if hasattr(model.config, 'id2label') and model.config.id2label:
            emotions = list(model.config.id2label.values())
            logger.info(f"📊 Available emotions: {emotions}")
            logger.info(f"📊 Number of emotion classes: {len(emotions)}")

            # Expected emotions: ['angry', 'calm', 'disgust', 'fearful', 'happy', 'neutral', 'sad', 'surprised']
            expected_emotions = ['angry', 'calm', 'disgust', 'fearful', 'happy', 'neutral', 'sad', 'surprised']
            if set(emotions) == set(expected_emotions):
                logger.info("✅ Model has expected emotion labels")
            else:
                logger.warning(f"⚠️ Model emotions differ from expected: {emotions}")
        else:
            logger.error("❌ Model has no emotion labels!")
            return False

        # Test the model with a dummy input
        logger.info("🧪 Testing model with dummy input...")
        dummy_audio = np.random.randn(feature_extractor.sampling_rate)  # 1 second of random audio
        test_inputs = feature_extractor(
            dummy_audio,
            sampling_rate=feature_extractor.sampling_rate,
            return_tensors="pt"
        )

        model.eval()
        with torch.no_grad():
            test_outputs = model(**test_inputs)

        # Check outputs
        if hasattr(test_outputs, 'logits'):
            logits = test_outputs.logits
            logger.info(f"📊 Model output shape: {logits.shape}")

            # Get probabilities
            probs = torch.softmax(logits, dim=-1)
            predicted_id = torch.argmax(probs, dim=-1).item()
            predicted_emotion = model.config.id2label[predicted_id]
            confidence = probs[0][predicted_id].item()

            logger.info(f"🧪 Test prediction: {predicted_emotion} (confidence: {confidence:.3f})")
            logger.info("✅ Model test passed!")
        else:
            logger.error("❌ Model output has no logits!")
            return False

        # Cache the working models globally
        _cached_audio_feature_extractor = feature_extractor
        _cached_audio_model = model

        logger.info(f"🎯 Audio emotion model initialization complete!")
        return True

    except Exception as e:
        logger.error(f"❌ Failed to initialize audio emotion model: {str(e)}")
        logger.error(f"❌ Make sure you ran 'python download_models.py' first")
        return False
    finally:
        _model_loading_lock = False

def get_audio_emotion_model():
    """Get the cached audio emotion model"""
    global _cached_audio_model, _cached_audio_feature_extractor

    if _cached_audio_model is None or _cached_audio_feature_extractor is None:
        logger.error("❌ Audio emotion model not initialized! Call initialize_audio_emotion_model() first")
        raise RuntimeError("Audio emotion model not initialized")

    return _cached_audio_feature_extractor, _cached_audio_model

def test_audio_emotion_model():
    """Test the loaded audio emotion model with a simple check"""
    logger.info("🧪 === TESTING AUDIO EMOTION MODEL ===")

    try:
        feature_extractor, model = get_audio_emotion_model()

        # Just test that the model can process audio without errors
        # Note: This model is trained on speech, so synthetic signals may not give diverse results
        logger.info("🔄 Testing basic model functionality...")

        # Create a simple test audio (1 second of low noise)
        test_audio = np.random.randn(feature_extractor.sampling_rate) * 0.1

        # Process audio
        inputs = feature_extractor(
            test_audio,
            sampling_rate=feature_extractor.sampling_rate,
            return_tensors="pt"
        )

        # Get prediction
        model.eval()
        with torch.no_grad():
            outputs = model(**inputs)

        # Extract results
        if hasattr(outputs, 'logits'):
            logits = outputs.logits
            probs = torch.softmax(logits, dim=-1)
            predicted_id = torch.argmax(probs, dim=-1).item()

            if hasattr(model.config, 'id2label') and model.config.id2label:
                emotion = model.config.id2label[predicted_id]
                confidence = probs[0][predicted_id].item()

                logger.info(f"📊 Test prediction: {emotion} (confidence: {confidence:.3f})")
                logger.info("✅ Model is functioning correctly")

                # Log all available emotions
                emotions = list(model.config.id2label.values())
                logger.info(f"📊 Model can predict: {emotions}")

                # Note about speech requirement
                logger.info("💡 Note: This model is trained on human speech")
                logger.info("💡 For best results, use actual speech audio files")

                return True
            else:
                logger.error("❌ Model has no emotion labels")
                return False
        else:
            logger.error("❌ Model output has no logits")
            return False

    except Exception as e:
        logger.error(f"❌ Model testing failed: {str(e)}")
        return False

# ================= COMMENTED OUT: OLD MODEL CACHING =================
# # --- Model Caching ---
# # Global variables to cache loaded models and avoid re-downloading
# _cached_audio_model = None
# _cached_audio_feature_extractor = None
# _model_loading_lock = False

# def get_cached_audio_model():
#     """Get cached audio emotion model or load it if not cached"""
#     global _cached_audio_model, _cached_audio_feature_extractor, _model_loading_lock

#     if _cached_audio_model is not None and _cached_audio_feature_extractor is not None:
#         logger.info("✅ Using cached audio emotion model")
#         return _cached_audio_feature_extractor, _cached_audio_model

#     if _model_loading_lock:
#         logger.info("⏳ Model is already being loaded by another request, waiting...")
#         import time
#         while _model_loading_lock:
#             time.sleep(0.5)
#         return _cached_audio_feature_extractor, _cached_audio_model

#     try:
#         _model_loading_lock = True
#         model_id = "vishrutjha/pph-emotion-classification-model"
#         logger.info(f"🤖 Loading audio emotion model: {model_id}")

#         # Load with caching enabled
#         feature_extractor = AutoFeatureExtractor.from_pretrained(
#             model_id,
#             trust_remote_code=True,
#             cache_dir=os.path.join(os.getcwd(), "model_cache"),
#             local_files_only=False
#         )
#         logger.info("✅ Feature extractor loaded successfully")

#         model = AutoModelForAudioClassification.from_pretrained(
#             model_id,
#             trust_remote_code=True,
#             cache_dir=os.path.join(os.getcwd(), "model_cache"),
#             local_files_only=False
#         )
#         logger.info("✅ Audio emotion model loaded successfully")

#         # Cache the models
#         _cached_audio_feature_extractor = feature_extractor
#         _cached_audio_model = model

#         return feature_extractor, model

#     except Exception as e:
#         logger.error(f"❌ Failed to load audio emotion model: {str(e)}")
#         raise
#     finally:
#         _model_loading_lock = False

# --- Import your SQLAlchemy models and session maker ---
from model.model_correct import AudioEmotion, ImageEmotion, SessionLocal

# --- Routers ---
audio_router = APIRouter()
image_router = APIRouter()

# --- DB Dependency ---
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# --- Request Models ---
class AudioInput(BaseModel):
    patient_id: str
    conversation_id: str
    session_id: str = None  # Optional, will auto-generate if not provided
    audio_base64: str
    timestamp: datetime = datetime.now(timezone.utc)

class ImageInput(BaseModel):
    patient_id: str
    conversation_id: str
    session_id: str = None  # Optional, will auto-generate if not provided
    image_base64: str
    timestamp: datetime = datetime.now(timezone.utc)

# ------------------- Video/Image-Based Emotion Detection -------------------

headers_image = {
    "Authorization": f"Bearer {HF_TOKEN}",
    "Content-Type": "image/jpeg"
}

def detect_videoemotion(image_path):
    logger.info(f"📸 === IMAGE EMOTION PREDICTION STARTED ===")
    logger.info(f"📁 Image file path: {image_path}")

    if not os.path.isfile(image_path):
        logger.error(f"❌ File not found: {image_path}")
        return "unknown"

    try:
        logger.info("📖 Reading image file...")
        with open(image_path, "rb") as f:
            data = f.read()
        logger.info(f"✅ Image file read successfully - Size: {len(data)} bytes")

        logger.info(f"🌐 Sending request to Hugging Face API: {HF_API_URL}")
        logger.info(f"🔑 Using API token: {HF_TOKEN[:10]}..." if HF_TOKEN else "❌ No API token")

        response = requests.post(HF_API_URL, headers=headers_image, data=data)
        logger.info(f"📡 API response status: {response.status_code}")

        response.raise_for_status()
        predictions = response.json()
        logger.info(f"📊 API response received - Predictions count: {len(predictions) if predictions else 0}")

        if predictions:
            logger.info("🔍 Processing predictions...")
            for i, pred in enumerate(predictions):
                logger.info(f"  Prediction {i+1}: {pred.get('label', 'unknown')} - Score: {pred.get('score', 0):.4f}")

            top = max(predictions, key=lambda x: x["score"])
            emotion = top["label"].lower()
            confidence = top["score"]

            logger.info(f"🎯 Top prediction - Emotion: {emotion}")
            logger.info(f"🎯 Top prediction - Confidence: {confidence:.4f}")
            logger.info(f"📸 === IMAGE EMOTION PREDICTION COMPLETED ===")

            return emotion
        else:
            logger.warning("⚠️ No predictions received from API, returning neutral")
            logger.info(f"📸 === IMAGE EMOTION PREDICTION COMPLETED (NEUTRAL) ===")
            return "neutral"

    except Exception as e:
        logger.error(f"❌ Image emotion detection failed: {str(e)}")
        logger.error(f"❌ Exception type: {type(e).__name__}")
        logger.error(f"📸 === IMAGE EMOTION PREDICTION FAILED ===")
        return "unknown"

# ------------------- Audio-Based Emotion Detection -------------------

# === AUDIO LOADING WITH FALLBACKS ===
def load_audio_with_fallback(audio_path: str, target_sr: int) -> tuple:
    """Load audio with multiple fallback methods"""
    try:
        # Primary method: librosa with soundfile backend
        audio, sr = librosa.load(audio_path, sr=target_sr)
        logger.info(f"✅ Audio loaded with librosa - Duration: {len(audio)/sr:.2f}s, Sample rate: {sr}Hz, Samples: {len(audio)}")
        return audio, sr
    except Exception as e:
        logger.warning(f"⚠️ Librosa failed: {str(e)}, trying fallback methods...")

        try:
            # Fallback 1: scipy.io.wavfile
            import scipy.io.wavfile as wavfile
            sr_scipy, audio_scipy = wavfile.read(audio_path)

            # Convert to float and normalize
            if audio_scipy.dtype == np.int16:
                audio_scipy = audio_scipy.astype(np.float32) / 32768.0
            elif audio_scipy.dtype == np.int32:
                audio_scipy = audio_scipy.astype(np.float32) / 2147483648.0

            # Resample if needed
            if sr_scipy != target_sr:
                import scipy.signal
                audio_scipy = scipy.signal.resample(audio_scipy, int(len(audio_scipy) * target_sr / sr_scipy))
                sr_scipy = target_sr

            logger.info(f"✅ Audio loaded with scipy - Duration: {len(audio_scipy)/sr_scipy:.2f}s, Sample rate: {sr_scipy}Hz")
            return audio_scipy, sr_scipy

        except Exception as e2:
            logger.error(f"❌ All audio loading methods failed: {str(e2)}")
            raise Exception(f"Could not load audio file: {audio_path}")

def predict_audio_emotion(audio_path: str) -> dict:
    logger.info(f"🎵 === AUDIO EMOTION PREDICTION STARTED ===")
    logger.info(f"📁 Audio file path: {audio_path}")

    try:
        # Get the cached model
        feature_extractor, model = get_audio_emotion_model()
        logger.info("✅ Using cached audio emotion model")

        # Load audio with fallback methods
        logger.info(f"🎵 Loading audio file with target sampling rate: {feature_extractor.sampling_rate}")
        audio, sr = load_audio_with_fallback(audio_path, feature_extractor.sampling_rate)

        # Preprocess audio for better emotion recognition
        logger.info("🔄 Preprocessing audio...")

        # Normalize audio to prevent clipping
        if np.max(np.abs(audio)) > 0:
            audio = audio / np.max(np.abs(audio)) * 0.9

        # Ensure minimum length (pad if too short)
        min_length = feature_extractor.sampling_rate  # 1 second minimum
        if len(audio) < min_length:
            audio = np.pad(audio, (0, min_length - len(audio)), mode='constant')
            logger.info(f"📊 Padded audio to minimum length: {len(audio)} samples")

        # Limit maximum length (truncate if too long)
        max_length = feature_extractor.sampling_rate * 10  # 10 seconds maximum
        if len(audio) > max_length:
            audio = audio[:max_length]
            logger.info(f"📊 Truncated audio to maximum length: {len(audio)} samples")

        logger.info(f"📊 Final audio length: {len(audio)} samples ({len(audio)/feature_extractor.sampling_rate:.2f} seconds)")

        # Extract features
        logger.info("🔄 Extracting features...")
        inputs = feature_extractor(
            audio,
            sampling_rate=feature_extractor.sampling_rate,
            return_tensors="pt",
            padding=True,
            truncation=True
        )
        logger.info(f"✅ Features extracted")
        logger.info(f"📊 Available input keys: {list(inputs.keys())}")

        # Log shapes for debugging
        for key, value in inputs.items():
            if hasattr(value, 'shape'):
                logger.info(f"📊 {key} shape: {value.shape}")

        # Run model inference
        logger.info("🧠 Running model inference...")
        model.eval()
        with torch.no_grad():
            outputs = model(**inputs)
            logger.info("✅ Model inference completed")

        # Inspect model outputs
        logger.info(f"📊 Model output type: {type(outputs)}")
        if hasattr(outputs, 'keys'):
            logger.info(f"📊 Model output keys: {list(outputs.keys())}")

        # Extract emotion logits
        if hasattr(outputs, 'emotion_logits'):
            logits = outputs.emotion_logits
            logger.info(f"📊 Found emotion_logits with shape: {logits.shape}")
        elif hasattr(outputs, 'logits'):
            logits = outputs.logits
            logger.info(f"📊 Found logits with shape: {logits.shape}")
        else:
            logger.error(f"❌ No emotion_logits or logits found in outputs")
            logger.error(f"❌ Available attributes: {[attr for attr in dir(outputs) if not attr.startswith('_')]}")
            raise KeyError("No emotion logits found in model output")

        # Get predictions
        probs = torch.softmax(logits, dim=-1)
        predicted_id = torch.argmax(probs, dim=-1).item()
        logger.info(f"📊 Prediction - ID: {predicted_id}, Logits shape: {logits.shape}")

        # Get emotion label and confidence
        emotion = model.config.id2label[predicted_id]
        confidence = probs[0][predicted_id].item()

        logger.info(f"📊 Available emotions: {list(model.config.id2label.values())}")

        # Log all emotion probabilities for debugging
        logger.info("📊 All emotion probabilities:")
        for idx, prob in enumerate(probs[0]):
            label = model.config.id2label.get(idx, f"emotion_{idx}")
            logger.info(f"   {label}: {prob.item():.4f}")

        # Map emotions to arousal/valence (this model doesn't provide arousal/valence)
        # Expected emotions: ['angry', 'calm', 'disgust', 'fearful', 'happy', 'neutral', 'sad', 'surprised']
        emotion_mapping = {
            'angry': {'arousal': 0.9, 'valence': 0.1},
            'calm': {'arousal': 0.2, 'valence': 0.7},
            'disgust': {'arousal': 0.6, 'valence': 0.2},
            'fearful': {'arousal': 0.8, 'valence': 0.2},
            'happy': {'arousal': 0.8, 'valence': 0.9},
            'neutral': {'arousal': 0.5, 'valence': 0.5},
            'sad': {'arousal': 0.3, 'valence': 0.1},
            'surprised': {'arousal': 0.7, 'valence': 0.6}
        }

        if emotion.lower() in emotion_mapping:
            arousal = emotion_mapping[emotion.lower()]['arousal']
            valence = emotion_mapping[emotion.lower()]['valence']
        else:
            # Fallback for any unexpected emotions
            arousal, valence = 0.5, 0.5
            logger.warning(f"⚠️ Unknown emotion '{emotion}', using default arousal/valence")

        logger.info(f"📊 Mapped arousal/valence: {arousal:.4f}, {valence:.4f}")

        # Log final results
        logger.info(f"🎯 Final prediction - Emotion: {emotion}")
        logger.info(f"🎯 Final prediction - Confidence: {confidence:.4f}")
        logger.info(f"🎯 Final prediction - Arousal: {arousal:.4f}")
        logger.info(f"🎯 Final prediction - Valence: {valence:.4f}")
        logger.info(f"🎵 === AUDIO EMOTION PREDICTION COMPLETED ===")

        return {
            "emotion": emotion,
            "confidence": confidence,
            "arousal": arousal,
            "valence": valence
        }

    except Exception as e:
        logger.error(f"❌ Audio emotion prediction failed: {str(e)}")
        logger.error(f"❌ Exception type: {type(e).__name__}")
        logger.error(f"🎵 === AUDIO EMOTION PREDICTION FAILED ===")

        # Return neutral emotion as fallback
        return {
            'emotion': 'neutral',
            'confidence': 0.5,
            'arousal': 0.5,
            'valence': 0.5
        }

# ================= COMMENTED OUT: LOCAL MODEL APPROACH =================
# def predict_audio_emotion_local(audio_path: str) -> dict:
#     """Local model approach - commented out for now"""
#     logger.info(f"🎵 === AUDIO EMOTION PREDICTION STARTED (LOCAL) ===")
#     logger.info(f"📁 Audio file path: {audio_path}")

#     try:
#         # Use cached model
#         feature_extractor, model = get_cached_audio_model()

#         logger.info(f"🎵 Loading audio file with target sampling rate: {feature_extractor.sampling_rate}")

#         # Load audio with improved error handling
#         audio, sr = load_audio_with_fallback(audio_path, feature_extractor.sampling_rate)

#         logger.info("🔄 Extracting features...")
#         inputs = feature_extractor(audio, sampling_rate=feature_extractor.sampling_rate, return_tensors="pt")
#         logger.info(f"✅ Features extracted")
#         logger.info(f"📊 Available input keys: {list(inputs.keys())}")

#         # Log shapes for all available keys
#         for key, value in inputs.items():
#             if hasattr(value, 'shape'):
#                 logger.info(f"📊 {key} shape: {value.shape}")
#             else:
#                 logger.info(f"📊 {key}: {value}")

#         logger.info("🧠 Running model inference...")
#         model.eval()
#         with torch.no_grad():
#             outputs = model(**inputs)
#             logger.info("✅ Model inference completed")

#         # Inspect model outputs
#         logger.info(f"📊 Model output type: {type(outputs)}")
#         if hasattr(outputs, 'keys'):
#             logger.info(f"📊 Model output keys: {list(outputs.keys())}")

#         # Check for emotion logits
#         if hasattr(outputs, 'emotion_logits'):
#             logits = outputs.emotion_logits
#             logger.info(f"📊 Found emotion_logits with shape: {logits.shape}")
#         elif hasattr(outputs, 'logits'):
#             logits = outputs.logits
#             logger.info(f"📊 Found logits with shape: {logits.shape}")
#         else:
#             logger.error(f"❌ No emotion_logits or logits found in outputs")
#             logger.error(f"❌ Available attributes: {[attr for attr in dir(outputs) if not attr.startswith('_')]}")
#             raise KeyError("No emotion logits found in model output")

#         probs = torch.softmax(logits, dim=-1)
#         predicted_id = torch.argmax(probs, dim=-1).item()
#         logger.info(f"📊 Prediction - ID: {predicted_id}, Logits shape: {logits.shape}")

#         emotion = model.config.id2label[predicted_id]
#         confidence = probs[0][predicted_id].item()

#         # Check for arousal/valence
#         if hasattr(outputs, 'arousal_valence'):
#             arousal, valence = outputs.arousal_valence[0].tolist()
#             logger.info(f"📊 Found arousal_valence: {arousal:.4f}, {valence:.4f}")
#         else:
#             logger.warning("⚠️ No arousal_valence found, using defaults")
#             arousal, valence = 0.5, 0.5

#         logger.info(f"🎯 Final prediction - Emotion: {emotion}")
#         logger.info(f"🎯 Final prediction - Confidence: {confidence:.4f}")
#         logger.info(f"🎯 Final prediction - Arousal: {arousal:.4f}")
#         logger.info(f"🎯 Final prediction - Valence: {valence:.4f}")
#         logger.info(f"🎵 === AUDIO EMOTION PREDICTION COMPLETED ===")

#         return {
#             "emotion": emotion,
#             "confidence": confidence,
#             "arousal": arousal,
#             "valence": valence
#         }

#     except Exception as e:
#         logger.error(f"❌ Audio emotion prediction failed: {str(e)}")
#         logger.error(f"❌ Exception type: {type(e).__name__}")
#         logger.error(f"🎵 === AUDIO EMOTION PREDICTION FAILED ===")
#         raise e

# ------------------- Image Description -------------------

def describe_user_image(image_path, hf_api_url, hf_token):
    if not os.path.isfile(image_path):
        raise FileNotFoundError(f"Image not found: {image_path}")

    with open(image_path, "rb") as f:
        image_bytes = f.read()
    image_b64 = base64.b64encode(image_bytes).decode("utf-8")

    headers = {
        "Authorization": f"Bearer {hf_token}",
        "Content-Type": "application/json"
    }

    payload = {
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "Describe this image in one sentence."},
                    {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_b64}"}}
                ]
            }
        ],
        "model": "accounts/fireworks/models/llama4-scout-instruct-basic"
    }

    response = requests.post(hf_api_url, headers=headers, json=payload)
    if response.status_code != 200:
        raise Exception(f"Error {response.status_code}: {response.text}")

    return response.json()["choices"][0]["message"]["content"]

# ------------------- API Endpoints -------------------

# --------- Audio Emotion Analysis ---------

@audio_router.post("/analyze-conversation-audio", status_code=status.HTTP_200_OK)
async def analyze_conversation_audio(input: AudioInput, db: Session = Depends(get_db)):
    """
    Analyze audio emotion during a conversation for real-time emotion tracking.
    """
    logger.info("🎤 === AUDIO EMOTION ANALYSIS STARTED ===")
    logger.info(f"📥 Input received - Patient ID: {input.patient_id}")
    logger.info(f"📥 Input received - Conversation ID: {input.conversation_id}")
    logger.info(f"📥 Input received - Session ID: {input.session_id}")
    logger.info(f"📥 Input received - Timestamp: {input.timestamp}")
    logger.info(f"📥 Input received - Audio base64 length: {len(input.audio_base64)} characters")

    try:
        # Decode base64 audio
        logger.info("🔄 Decoding base64 audio data...")
        audio_data = base64.b64decode(input.audio_base64.split(",")[-1])
        logger.info(f"✅ Audio decoded successfully - Size: {len(audio_data)} bytes")

        audio_path = f"temp_audio_{uuid.uuid4()}.wav"
        logger.info(f"💾 Saving audio to temporary file: {audio_path}")

        with open(audio_path, "wb") as f:
            f.write(audio_data)
        logger.info(f"✅ Audio file saved successfully")

    except Exception as e:
        logger.error(f"❌ Error decoding/saving audio: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Invalid base64 audio: {str(e)}")

    try:
        # Analyze emotion
        logger.info("🧠 Starting emotion analysis...")
        result = predict_audio_emotion(audio_path)
        logger.info(f"✅ Emotion analysis completed!")
        logger.info(f"📊 Analysis result - Emotion: {result['emotion']}")
        logger.info(f"📊 Analysis result - Confidence: {result['confidence']}")
        logger.info(f"📊 Analysis result - Arousal: {result['arousal']}")
        logger.info(f"📊 Analysis result - Valence: {result['valence']}")

        # Generate session_id if not provided
        session_id = input.session_id or f"conv_{input.conversation_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        logger.info(f"🔑 Using session ID: {session_id}")

        # Create database record
        sample_id = str(uuid.uuid4())
        logger.info(f"💾 Creating database record with sample ID: {sample_id}")

        record = AudioEmotion(
            sample_id=sample_id,
            patient_id=input.patient_id,
            conversation_id=input.conversation_id,
            session_id=session_id,
            timestamp=input.timestamp,
            source="audio",
            emotion=result["emotion"],
            confidence=result["confidence"],
            arousal=result["arousal"],
            valence=result["valence"]
        )

        # Save to database
        logger.info("💾 Saving to database...")
        db.add(record)
        db.commit()
        logger.info("✅ Database save successful!")

        # Prepare response
        response_data = {
            "status": "success",
            "sample_id": record.sample_id,
            "patient_id": input.patient_id,
            "conversation_id": input.conversation_id,
            "emotion_data": {
                "emotion": result["emotion"],
                "confidence": result["confidence"],
                "arousal": result["arousal"],
                "valence": result["valence"]
            },
            "timestamp": record.timestamp.isoformat()
        }

        logger.info("📤 Preparing response...")
        logger.info(f"📤 Response - Status: {response_data['status']}")
        logger.info(f"📤 Response - Sample ID: {response_data['sample_id']}")
        logger.info(f"📤 Response - Emotion: {response_data['emotion_data']['emotion']}")
        logger.info(f"📤 Response - Confidence: {response_data['emotion_data']['confidence']}")
        logger.info("🎤 === AUDIO EMOTION ANALYSIS COMPLETED SUCCESSFULLY ===")

        return response_data

    except Exception as e:
        logger.error(f"❌ Audio analysis failed: {str(e)}")
        logger.error(f"❌ Exception type: {type(e).__name__}")
        logger.error(f"❌ Exception details: {str(e)}")
        db.rollback()
        logger.info("🔄 Database rollback completed")
        logger.info("🎤 === AUDIO EMOTION ANALYSIS FAILED ===")
        raise HTTPException(status_code=500, detail=f"Audio analysis failed: {str(e)}")
    finally:
        # Clean up temporary file
        logger.info("🧹 Cleaning up temporary files...")
        if os.path.exists(audio_path):
            os.remove(audio_path)
            logger.info(f"🗑️ Removed temporary file: {audio_path}")
        else:
            logger.warning(f"⚠️ Temporary file not found for cleanup: {audio_path}")


# --------- Image Emotion Analysis ---------

@image_router.post("/analyze-conversation-image", status_code=status.HTTP_200_OK)
async def analyze_conversation_image(payload: ImageInput, db: Session = Depends(get_db)):
    """
    Analyze facial emotion from image during a conversation for real-time emotion tracking.
    """
    logger.info("📷 === IMAGE EMOTION ANALYSIS STARTED ===")
    logger.info(f"📥 Input received - Patient ID: {payload.patient_id}")
    logger.info(f"📥 Input received - Conversation ID: {payload.conversation_id}")
    logger.info(f"📥 Input received - Session ID: {payload.session_id}")
    logger.info(f"📥 Input received - Timestamp: {payload.timestamp}")
    logger.info(f"📥 Input received - Image base64 length: {len(payload.image_base64)} characters")

    try:
        # Decode base64 image
        logger.info("🔄 Decoding base64 image data...")
        image_data = base64.b64decode(payload.image_base64.split(",")[-1])
        logger.info(f"✅ Image decoded successfully - Size: {len(image_data)} bytes")

        image_path = f"temp_img_{uuid.uuid4()}.jpg"
        logger.info(f"💾 Saving image to temporary file: {image_path}")

        with open(image_path, "wb") as f:
            f.write(image_data)
        logger.info(f"✅ Image file saved successfully")

    except Exception as e:
        logger.error(f"❌ Error decoding/saving image: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Invalid base64 image: {str(e)}")

    try:
        # Analyze emotion
        logger.info("🧠 Starting facial emotion analysis...")
        emotion = detect_videoemotion(image_path)
        logger.info(f"✅ Facial emotion analysis completed!")
        logger.info(f"📊 Analysis result - Emotion: {emotion}")
        logger.info(f"📊 Analysis result - Confidence: 0.95 (default)")

        # Generate session_id if not provided
        session_id = payload.session_id or f"conv_{payload.conversation_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        logger.info(f"🔑 Using session ID: {session_id}")

        # Create database record
        sample_id = str(uuid.uuid4())
        logger.info(f"💾 Creating database record with sample ID: {sample_id}")

        record = ImageEmotion(
            sample_id=sample_id,
            patient_id=payload.patient_id,
            conversation_id=payload.conversation_id,
            session_id=session_id,
            timestamp=payload.timestamp,
            source="image",
            emotion=emotion,
            confidence=0.95  # Default confidence for image emotion
        )

        # Save to database
        logger.info("💾 Saving to database...")
        db.add(record)
        db.commit()
        logger.info("✅ Database save successful!")

        # Prepare response
        response_data = {
            "status": "success",
            "sample_id": record.sample_id,
            "patient_id": payload.patient_id,
            "conversation_id": payload.conversation_id,
            "emotion_data": {
                "emotion": emotion,
                "confidence": 0.95
            },
            "timestamp": record.timestamp.isoformat()
        }

        logger.info("📤 Preparing response...")
        logger.info(f"📤 Response - Status: {response_data['status']}")
        logger.info(f"📤 Response - Sample ID: {response_data['sample_id']}")
        logger.info(f"📤 Response - Emotion: {response_data['emotion_data']['emotion']}")
        logger.info(f"📤 Response - Confidence: {response_data['emotion_data']['confidence']}")
        logger.info("📷 === IMAGE EMOTION ANALYSIS COMPLETED SUCCESSFULLY ===")

        return response_data

    except Exception as e:
        logger.error(f"❌ Image analysis failed: {str(e)}")
        logger.error(f"❌ Exception type: {type(e).__name__}")
        logger.error(f"❌ Exception details: {str(e)}")
        db.rollback()
        logger.info("🔄 Database rollback completed")
        logger.info("📷 === IMAGE EMOTION ANALYSIS FAILED ===")
        raise HTTPException(status_code=500, detail=f"Image analysis failed: {str(e)}")
    finally:
        # Clean up temporary file
        logger.info("🧹 Cleaning up temporary files...")
        if os.path.exists(image_path):
            os.remove(image_path)
            logger.info(f"🗑️ Removed temporary file: {image_path}")
        else:
            logger.warning(f"⚠️ Temporary file not found for cleanup: {image_path}")

# --------- Image Description ---------

@image_router.post("/image-description", status_code=status.HTTP_200_OK)
async def describe_image(payload: ImageInput):
    try:
        image_data = base64.b64decode(payload.image_base64.split(",")[-1])
        image_path = f"temp_desc_{uuid.uuid4()}.png"
        with open(image_path, "wb") as f:
            f.write(image_data)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid base64 image. {e}")

    try:
        description = describe_user_image(image_path, HF_IMAGE_DESCRIP_URL, HF_TOKEN)
    except Exception as e:
        os.remove(image_path)
        raise HTTPException(status_code=500, detail=f"Image description failed: {e}")

    os.remove(image_path)
    return {"description": description}

# --------- Conversation Emotion History ---------

@audio_router.get("/conversation/{conversation_id}/emotions", status_code=status.HTTP_200_OK)
async def get_conversation_emotions(conversation_id: str, db: Session = Depends(get_db)):
    """
    Get all emotion data (audio and image) for a specific conversation.
    """
    logger.info(f"📊 === CONVERSATION EMOTIONS RETRIEVAL STARTED ===")
    logger.info(f"🔍 Conversation ID: {conversation_id}")

    try:
        # Get audio emotions
        logger.info("🎤 Querying audio emotions...")
        audio_emotions = db.query(AudioEmotion).filter(
            AudioEmotion.conversation_id == conversation_id
        ).order_by(AudioEmotion.timestamp.desc()).all()
        logger.info(f"✅ Found {len(audio_emotions)} audio emotion records")

        # Get image emotions
        logger.info("📷 Querying image emotions...")
        image_emotions = db.query(ImageEmotion).filter(
            ImageEmotion.conversation_id == conversation_id
        ).order_by(ImageEmotion.timestamp.desc()).all()
        logger.info(f"✅ Found {len(image_emotions)} image emotion records")

        # Format response
        audio_data = [
            {
                "sample_id": emotion.sample_id,
                "patient_id": emotion.patient_id,
                "timestamp": emotion.timestamp.isoformat(),
                "emotion": emotion.emotion,
                "confidence": emotion.confidence,
                "arousal": emotion.arousal,
                "valence": emotion.valence,
                "source": "audio"
            }
            for emotion in audio_emotions
        ]

        image_data = [
            {
                "sample_id": emotion.sample_id,
                "patient_id": emotion.patient_id,
                "timestamp": emotion.timestamp.isoformat(),
                "emotion": emotion.emotion,
                "confidence": emotion.confidence,
                "source": "image"
            }
            for emotion in image_emotions
        ]

        response_data = {
            "conversation_id": conversation_id,
            "total_audio_samples": len(audio_data),
            "total_image_samples": len(image_data),
            "audio_emotions": audio_data,
            "image_emotions": image_data
        }

        logger.info(f"📤 Preparing response...")
        logger.info(f"📤 Total audio samples: {len(audio_data)}")
        logger.info(f"📤 Total image samples: {len(image_data)}")
        logger.info(f"📊 === CONVERSATION EMOTIONS RETRIEVAL COMPLETED ===")

        return response_data

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving conversation emotions: {str(e)}")

@audio_router.get("/patient/{patient_id}/emotions", status_code=status.HTTP_200_OK)
async def get_patient_emotions(patient_id: str, limit: int = 50, db: Session = Depends(get_db)):
    """
    Get recent emotion data for a specific patient across all conversations.
    """
    try:
        # Get recent audio emotions
        audio_emotions = db.query(AudioEmotion).filter(
            AudioEmotion.patient_id == patient_id
        ).order_by(AudioEmotion.timestamp.desc()).limit(limit).all()

        # Get recent image emotions
        image_emotions = db.query(ImageEmotion).filter(
            ImageEmotion.patient_id == patient_id
        ).order_by(ImageEmotion.timestamp.desc()).limit(limit).all()

        # Format response
        audio_data = [
            {
                "sample_id": emotion.sample_id,
                "conversation_id": emotion.conversation_id,
                "timestamp": emotion.timestamp.isoformat(),
                "emotion": emotion.emotion,
                "confidence": emotion.confidence,
                "arousal": emotion.arousal,
                "valence": emotion.valence,
                "source": "audio"
            }
            for emotion in audio_emotions
        ]

        image_data = [
            {
                "sample_id": emotion.sample_id,
                "conversation_id": emotion.conversation_id,
                "timestamp": emotion.timestamp.isoformat(),
                "emotion": emotion.emotion,
                "confidence": emotion.confidence,
                "source": "image"
            }
            for emotion in image_emotions
        ]

        return {
            "patient_id": patient_id,
            "total_audio_samples": len(audio_data),
            "total_image_samples": len(image_data),
            "audio_emotions": audio_data,
            "image_emotions": image_data
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving patient emotions: {str(e)}")

# --------- Health Check ---------

@audio_router.get("/health")
def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "audiovideo_emotion_recognition",
        "features": ["audio_emotion", "image_emotion", "conversation_tracking"]
    }

@audio_router.get("/test-audio-backend")
def test_audio_backend():
    """Test if audio processing backend is working."""
    try:
        import librosa
        import soundfile as sf
        import numpy as np

        # Create a simple test audio (1 second of sine wave)
        sample_rate = 16000
        duration = 1.0
        frequency = 440  # A4 note
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = np.sin(2 * np.pi * frequency * t)

        # Save test audio
        test_file = "test_audio_backend.wav"
        sf.write(test_file, audio_data, sample_rate)

        # Try to load it back
        loaded_audio, loaded_sr = librosa.load(test_file, sr=sample_rate)

        # Clean up
        os.remove(test_file)

        return {
            "status": "success",
            "message": "Audio backend is working correctly",
            "librosa_version": librosa.__version__,
            "soundfile_available": True,
            "test_audio_duration": len(loaded_audio) / loaded_sr,
            "sample_rate": loaded_sr
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Audio backend test failed: {str(e)}",
            "error_type": type(e).__name__,
            "librosa_available": "librosa" in locals(),
            "soundfile_available": "sf" in locals()
        }
