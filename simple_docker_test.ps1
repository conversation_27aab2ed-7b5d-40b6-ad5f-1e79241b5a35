# Simple PowerShell script to test Docker setup

Write-Host "=== DOCKER SETUP TEST ===" -ForegroundColor Cyan

# Check Docker
Write-Host "`nChecking Docker..." -ForegroundColor Yellow
$dockerCheck = docker --version 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Docker is running: $dockerCheck" -ForegroundColor Green
} else {
    Write-Host "❌ Docker is not running" -ForegroundColor Red
    exit 1
}

# Check .env file
Write-Host "`nChecking .env file..." -ForegroundColor Yellow
if (Test-Path ".env") {
    Write-Host "✅ .env file found" -ForegroundColor Green
} else {
    Write-Host "❌ .env file not found" -ForegroundColor Red
    exit 1
}

# Check Docker image
Write-Host "`nChecking Docker image..." -ForegroundColor Yellow
$imageName = "prasha-chatbot"
$imageCheck = docker images -q $imageName 2>$null
if ($imageCheck) {
    Write-Host "✅ Docker image '$imageName' found" -ForegroundColor Green
} else {
    Write-Host "⚠️  Docker image '$imageName' not found" -ForegroundColor Yellow
    Write-Host "💡 Build it with: docker build -t $imageName ." -ForegroundColor Yellow
}

# Check critical environment variables
Write-Host "`nChecking environment variables..." -ForegroundColor Yellow
$envContent = Get-Content ".env" -ErrorAction SilentlyContinue

$criticalVars = @("azure_openai_api_key", "postgres_host", "postgres_username", "postgres_password")
$allGood = $true

foreach ($var in $criticalVars) {
    $found = $false
    foreach ($line in $envContent) {
        if ($line -like "$var=*" -and -not $line.StartsWith("#")) {
            $found = $true
            break
        }
    }
    
    if ($found) {
        Write-Host "  ✅ $var" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $var (missing or commented)" -ForegroundColor Red
        $allGood = $false
    }
}

# Summary
Write-Host "`n=== SUMMARY ===" -ForegroundColor Cyan
Write-Host "Docker: ✅" -ForegroundColor Green
Write-Host ".env file: ✅" -ForegroundColor Green

if ($imageCheck) {
    Write-Host "Image: ✅" -ForegroundColor Green
} else {
    Write-Host "Image: ⚠️  (needs building)" -ForegroundColor Yellow
}

if ($allGood) {
    Write-Host "Environment: ✅" -ForegroundColor Green
    Write-Host "`n🚀 Ready to run: .\run_docker.ps1" -ForegroundColor Cyan
} else {
    Write-Host "Environment: ❌ (check .env file)" -ForegroundColor Red
}

Write-Host "`nTest completed!" -ForegroundColor Green
