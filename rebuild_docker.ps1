# PowerShell script to rebuild Docker image with audio dependencies

$imageName = "prasha-chatbot"
$containerName = "prasha-chatbot-container"

Write-Host "=== REBUILDING DOCKER IMAGE WITH AUDIO SUPPORT ===" -ForegroundColor Cyan

# Stop and remove existing container
Write-Host "`nStopping existing container..." -ForegroundColor Yellow
docker stop $containerName 2>$null | Out-Null
docker rm $containerName 2>$null | Out-Null

# Remove existing image to force rebuild
Write-Host "Removing existing image..." -ForegroundColor Yellow
docker rmi $imageName 2>$null | Out-Null

# Build new image
Write-Host "`nBuilding new Docker image with audio dependencies..." -ForegroundColor Yellow
Write-Host "This may take several minutes..." -ForegroundColor Gray

$buildResult = docker build -t $imageName .

if ($LASTEXITCODE -eq 0) {
    Write-Host "`n✅ Docker image built successfully!" -ForegroundColor Green
    
    # Show image details
    Write-Host "`nImage details:" -ForegroundColor Cyan
    docker images $imageName --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    Write-Host "`n🚀 Ready to run with: .\run_docker.ps1" -ForegroundColor Green
    
} else {
    Write-Host "`n❌ Docker build failed!" -ForegroundColor Red
    Write-Host "Check the build output above for errors." -ForegroundColor Yellow
    exit 1
}

Write-Host "`nRebuild completed!" -ForegroundColor Green
