import os
import base64
import uuid
import torch
import librosa
import requests
from datetime import datetime, timezone
from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel
from typing import List
from dotenv import load_dotenv
from collections import defaultdict
from transformers import AutoFeatureExtractor, AutoModelForAudioClassification
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

# --- Load Environment Variables ---
load_dotenv()
HF_TOKEN = os.getenv("HF_API_Key")
HF_API_URL = os.getenv("HF_URL_VISION")
HF_AUDIO_URL = os.getenv("Audio_HF_URL")
HF_IMAGE_DESCRIP_URL = os.getenv("Image_description_url")
NVIDIA_SCOUT_API = os.getenv("NVIDIA_LLAMA4_SCOUT_API_KEY")

# --- Import your SQLAlchemy models and session maker ---
from model.model_correct import AudioEmotion, ImageEmotion, SessionLocal

# --- Routers ---
audio_router = APIRouter()
image_router = APIRouter()

# --- DB Dependency ---
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# --- Request Models ---
class AudioInput(BaseModel):
    session_id: str
    audio_base64: str
    timestamp: datetime = datetime.now(timezone.utc)

class ImageInput(BaseModel):
    image_base64: str

# ------------------- Video/Image-Based Emotion Detection -------------------

headers_image = {
    "Authorization": f"Bearer {HF_TOKEN}",
    "Content-Type": "image/jpeg"
}

def detect_videoemotion(image_path):
    if not os.path.isfile(image_path):
        print(f"[❌] File not found: {image_path}")
        return "unknown"
    try:
        with open(image_path, "rb") as f:
            data = f.read()
        response = requests.post(HF_API_URL, headers=headers_image, data=data)
        response.raise_for_status()
        predictions = response.json()
        if predictions:
            top = max(predictions, key=lambda x: x["score"])
            return top["label"].lower()
        else:
            return "neutral"
    except Exception as e:
        print(f"[EXCEPTION] Emotion detection failed: {e}")
        return "unknown"

# ------------------- Audio-Based Emotion Detection -------------------

def predict_audio_emotion(audio_path: str) -> dict:
    model_id = "vishrutjha/pph-emotion-classification-model"
    feature_extractor = AutoFeatureExtractor.from_pretrained(model_id, trust_remote_code=True)
    model = AutoModelForAudioClassification.from_pretrained(model_id, trust_remote_code=True)

    audio, sr = librosa.load(audio_path, sr=feature_extractor.sampling_rate)
    inputs = feature_extractor(audio, sampling_rate=feature_extractor.sampling_rate, return_tensors="pt")

    model.eval()
    with torch.no_grad():
        outputs = model(**inputs)

    logits = outputs.emotion_logits
    probs = torch.softmax(logits, dim=-1)
    predicted_id = torch.argmax(probs, dim=-1).item()

    emotion = model.config.id2label[predicted_id]
    confidence = probs[0][predicted_id].item()
    arousal, valence = outputs.arousal_valence[0].tolist()

    return {
        "emotion": emotion,
        "confidence": confidence,
        "arousal": arousal,
        "valence": valence
    }

# ------------------- Image Description -------------------

def describe_user_image(image_path, hf_api_url, hf_token):
    if not os.path.isfile(image_path):
        raise FileNotFoundError(f"Image not found: {image_path}")

    with open(image_path, "rb") as f:
        image_bytes = f.read()
    image_b64 = base64.b64encode(image_bytes).decode("utf-8")

    headers = {
        "Authorization": f"Bearer {hf_token}",
        "Content-Type": "application/json"
    }

    payload = {
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "Describe this image in one sentence."},
                    {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_b64}"}}
                ]
            }
        ],
        "model": "accounts/fireworks/models/llama4-scout-instruct-basic"
    }

    response = requests.post(hf_api_url, headers=headers, json=payload)
    if response.status_code != 200:
        raise Exception(f"Error {response.status_code}: {response.text}")

    return response.json()["choices"][0]["message"]["content"]

# ------------------- API Endpoints -------------------

# --------- Audio Emotion Analysis ---------

@audio_router.post("/run-analysis", status_code=status.HTTP_202_ACCEPTED)
async def analyze_audio(input: AudioInput, db: Session = Depends(get_db)):
    try:
        audio_data = base64.b64decode(input.audio_base64.split(",")[-1])
        audio_path = f"temp_audio_{uuid.uuid4()}.wav"
        with open(audio_path, "wb") as f:
            f.write(audio_data)
    except Exception:
        raise HTTPException(status_code=400, detail="Invalid base64 audio.")

    result = predict_audio_emotion(audio_path)
    os.remove(audio_path)

    record = AudioEmotion(
        sample_id=str(uuid.uuid4()),
        session_id=input.session_id,
        timestamp=input.timestamp,
        source="audio",
        emotion=result["emotion"],
        confidence=result["confidence"],
        arousal=result["arousal"],
        valence=result["valence"]
    )

    try:
        db.add(record)
        db.commit()
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

    return {"message": "Audio analyzed", "data": result}


# --------- Image Emotion Analysis ---------

@image_router.post("/image-emotion", status_code=status.HTTP_200_OK)
async def analyze_image_emotion(payload: ImageInput, db: Session = Depends(get_db)):
    try:
        image_data = base64.b64decode(payload.image_base64.split(",")[-1])
        image_path = f"temp_img_{uuid.uuid4()}.jpg"
        with open(image_path, "wb") as f:
            f.write(image_data)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid base64 image. {e}")

    try:
        emotion = detect_videoemotion(image_path)
    finally:
        os.remove(image_path)

    record = ImageEmotion(
        sample_id=str(uuid.uuid4()),
        session_id="image-session",  
        source="image",
        emotion=emotion,
        confidence=0.95  
    )

    try:
        db.add(record)
        db.commit()
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"DB error: {e}")

    return {"emotion": emotion}

# --------- Image Description ---------

@image_router.post("/image-description", status_code=status.HTTP_200_OK)
async def describe_image(payload: ImageInput):
    try:
        image_data = base64.b64decode(payload.image_base64.split(",")[-1])
        image_path = f"temp_desc_{uuid.uuid4()}.png"
        with open(image_path, "wb") as f:
            f.write(image_data)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid base64 image. {e}")

    try:
        description = describe_user_image(image_path, HF_IMAGE_DESCRIP_URL, HF_TOKEN)
    except Exception as e:
        os.remove(image_path)
        raise HTTPException(status_code=500, detail=f"Image description failed: {e}")

    os.remove(image_path)
    return {"description": description}
