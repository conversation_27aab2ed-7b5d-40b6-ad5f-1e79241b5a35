# Backup the existing credentials file
Copy-Item -Path $env:USERPROFILE\.aws\credentials -Destination $env:USERPROFILE\.aws\credentials.bak

# Create a new credentials file with a profile for the new account
@"
[default]
aws_access_key_id = YOUR_NEW_ACCESS_KEY
aws_secret_access_key = YOUR_NEW_SECRET_KEY

[old-account]
aws_access_key_id = ********************
aws_secret_access_key = sFX4457Hbenhf0vNc0j+x0BLcRQyxhpINpgu7IRz
"@ | Set-Content -Path $env:USERPROFILE\.aws\credentials

Write-Host "AWS credentials file updated. Your old credentials are backed up and also available under the 'old-account' profile."
