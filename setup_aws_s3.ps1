# PowerShell script to set up AWS S3 bucket and IAM permissions for PDF processing service
# This script creates an S3 bucket and adds the necessary IAM permissions without disturbing existing IAM setup

# Load environment variables from .env file
function Load-EnvFile {
    param (
        [string]$envFile = ".env"
    )

    if (Test-Path $envFile) {
        $envVars = @{}
        Get-Content $envFile | ForEach-Object {
            # Skip empty lines and comments
            if ($_ -match '^\s*$' -or $_ -match '^\s*#') {
                return
            }

            # Match key=value pattern
            if ($_ -match '^\s*([^=]+?)=(.*)$') {
                $key = $matches[1].Trim()
                $value = $matches[2].Trim()

                # Remove quotes if present
                if ($value -match '^[''"](.*)[''"]\s*$') {
                    $value = $matches[1]
                }

                # Set environment variable
                [Environment]::SetEnvironmentVariable($key, $value, "Process")
                Write-Host "Loaded environment variable: $key"

                # Store in hashtable for debugging
                $envVars[$key] = $value
            }
        }

        # Debug output for important variables
        Write-Host "PDF_BUCKET_NAME: $([Environment]::GetEnvironmentVariable('PDF_BUCKET_NAME'))" -ForegroundColor Yellow
        Write-Host "AWS_REGION: $([Environment]::GetEnvironmentVariable('AWS_REGION'))" -ForegroundColor Yellow
    } else {
        Write-Host "Environment file not found: $envFile" -ForegroundColor Red
        exit 1
    }
}

# Check if AWS CLI is installed
function Check-AwsCli {
    try {
        $awsVersion = aws --version
        Write-Host "AWS CLI is installed: $awsVersion" -ForegroundColor Green
    } catch {
        Write-Host "AWS CLI is not installed. Please install it first: https://aws.amazon.com/cli/" -ForegroundColor Red
        exit 1
    }
}

# Configure AWS CLI with credentials from .env
function Configure-AwsCli {
    $accessKey = [Environment]::GetEnvironmentVariable("AWS_ACCESS_KEY_ID")
    $secretKey = [Environment]::GetEnvironmentVariable("AWS_SECRET_ACCESS_KEY")
    $region = [Environment]::GetEnvironmentVariable("AWS_REGION")

    if (-not $accessKey -or -not $secretKey -or -not $region) {
        Write-Host "AWS credentials or region not found in .env file" -ForegroundColor Red
        exit 1
    }

    # Create AWS credentials file
    $awsFolder = "$env:USERPROFILE\.aws"
    if (-not (Test-Path $awsFolder)) {
        New-Item -ItemType Directory -Path $awsFolder | Out-Null
    }

    $credentialsContent = @"
[default]
aws_access_key_id = $accessKey
aws_secret_access_key = $secretKey
region = $region
"@

    $credentialsContent | Out-File -FilePath "$awsFolder\credentials" -Force
    Write-Host "AWS CLI configured with credentials from .env file" -ForegroundColor Green
}

# Create S3 bucket if it doesn't exist
function Create-S3Bucket {
    $bucketName = [Environment]::GetEnvironmentVariable("PDF_BUCKET_NAME")
    $region = [Environment]::GetEnvironmentVariable("AWS_REGION")

    if (-not $bucketName) {
        Write-Host "S3 bucket name not found in .env file" -ForegroundColor Red
        exit 1
    }

    # Check if bucket exists
    $bucketExists = $false
    try {
        $bucketList = aws s3api list-buckets --query "Buckets[].Name" --output json | ConvertFrom-Json
        $bucketExists = $bucketList -contains $bucketName
    } catch {
        Write-Host "Error checking if bucket exists: $_" -ForegroundColor Red
    }

    if ($bucketExists) {
        Write-Host "S3 bucket '$bucketName' already exists" -ForegroundColor Yellow
    } else {
        try {
            if ($region -eq "us-east-1") {
                # us-east-1 requires special handling (no LocationConstraint)
                aws s3api create-bucket --bucket $bucketName --output json
            } else {
                aws s3api create-bucket --bucket $bucketName --create-bucket-configuration LocationConstraint=$region --output json
            }
            Write-Host "S3 bucket '$bucketName' created successfully" -ForegroundColor Green

            # Enable server-side encryption
            $encryptionConfig = @"
{
    "Rules": [
        {
            "ApplyServerSideEncryptionByDefault": {
                "SSEAlgorithm": "AES256"
            },
            "BucketKeyEnabled": true
        }
    ]
}
"@
            $encryptionConfig | Out-File -FilePath "encryption-config.json" -Force
            aws s3api put-bucket-encryption --bucket $bucketName --server-side-encryption-configuration file://encryption-config.json
            Remove-Item -Path "encryption-config.json" -Force
            Write-Host "Server-side encryption enabled for bucket '$bucketName'" -ForegroundColor Green

            # Create folder structure
            aws s3api put-object --bucket $bucketName --key "pdfs/"
            aws s3api put-object --bucket $bucketName --key "faiss_index/"
            Write-Host "Created folder structure in bucket '$bucketName'" -ForegroundColor Green
        } catch {
            Write-Host "Error creating S3 bucket: $_" -ForegroundColor Red
            exit 1
        }
    }
}

# Create IAM policy for S3 access
function Create-IamPolicy {
    $bucketName = [Environment]::GetEnvironmentVariable("PDF_BUCKET_NAME")
    $policyName = "PrashaHealthPdfProcessingPolicy"

    # Check if policy already exists
    $policyExists = $false
    try {
        $policies = aws iam list-policies --scope Local --query "Policies[?PolicyName=='$policyName'].Arn" --output json | ConvertFrom-Json
        $policyExists = $policies.Length -gt 0
        if ($policyExists) {
            $policyArn = $policies[0]
        }
    } catch {
        Write-Host "Error checking if policy exists: $_" -ForegroundColor Red
    }

    if ($policyExists) {
        Write-Host "IAM policy '$policyName' already exists with ARN: $policyArn" -ForegroundColor Yellow
    } else {
        # Create policy document
        $policyDocument = @"
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:GetObject",
                "s3:DeleteObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::$bucketName",
                "arn:aws:s3:::$bucketName/*"
            ]
        }
    ]
}
"@
        $policyDocument | Out-File -FilePath "policy-document.json" -Force

        try {
            $policyResult = aws iam create-policy --policy-name $policyName --policy-document file://policy-document.json --output json | ConvertFrom-Json
            $policyArn = $policyResult.Policy.Arn
            Write-Host "IAM policy '$policyName' created successfully with ARN: $policyArn" -ForegroundColor Green
        } catch {
            Write-Host "Error creating IAM policy: $_" -ForegroundColor Red
            exit 1
        } finally {
            Remove-Item -Path "policy-document.json" -Force
        }
    }

    return $policyArn
}

# Attach policy to current IAM user
function Attach-PolicyToCurrentUser {
    param (
        [string]$policyArn
    )

    # Get current user
    try {
        $currentUser = aws iam get-user --query "User.UserName" --output text

        # Check if policy is already attached
        $attachedPolicies = aws iam list-attached-user-policies --user-name $currentUser --query "AttachedPolicies[].PolicyArn" --output json | ConvertFrom-Json
        $isPolicyAttached = $attachedPolicies -contains $policyArn

        if ($isPolicyAttached) {
            Write-Host "Policy is already attached to user '$currentUser'" -ForegroundColor Yellow
        } else {
            # Attach policy to user
            aws iam attach-user-policy --user-name $currentUser --policy-arn $policyArn
            Write-Host "Policy attached to user '$currentUser' successfully" -ForegroundColor Green
        }
    } catch {
        Write-Host "Error attaching policy to current user: $_" -ForegroundColor Red
        Write-Host "You may need to manually attach the policy with ARN: $policyArn" -ForegroundColor Yellow
    }
}

# Main execution
Write-Host "Setting up AWS S3 bucket and IAM permissions for PDF processing service..." -ForegroundColor Cyan

# Load environment variables
Load-EnvFile

# Check AWS CLI
Check-AwsCli

# Configure AWS CLI
Configure-AwsCli

# Create S3 bucket
Create-S3Bucket

# Create IAM policy
$policyArn = Create-IamPolicy

# Attach policy to current user
Attach-PolicyToCurrentUser -policyArn $policyArn

Write-Host "Setup complete!" -ForegroundColor Green
Write-Host "S3 Bucket: $([Environment]::GetEnvironmentVariable('PDF_BUCKET_NAME'))" -ForegroundColor Cyan
Write-Host "IAM Policy ARN: $policyArn" -ForegroundColor Cyan
Write-Host "To enable S3 integration in your application, make sure USE_S3=True is set in your .env file" -ForegroundColor Cyan
