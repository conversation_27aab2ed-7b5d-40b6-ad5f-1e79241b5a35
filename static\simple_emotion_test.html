<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Emotion API Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            border: none;
            margin-bottom: 20px;
        }
        
        .emotion-result {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
        }
        
        .emotion-badge {
            font-size: 1.5rem;
            padding: 10px 20px;
            border-radius: 25px;
            margin: 10px;
            display: inline-block;
            color: white;
            font-weight: bold;
        }
        
        .emotion-happy { background: linear-gradient(45deg, #FFD700, #FFA500); }
        .emotion-sad { background: linear-gradient(45deg, #4682B4, #1E90FF); }
        .emotion-angry { background: linear-gradient(45deg, #FF6347, #DC143C); }
        .emotion-excited { background: linear-gradient(45deg, #FF69B4, #FF1493); }
        .emotion-calm { background: linear-gradient(45deg, #98FB98, #90EE90); }
        .emotion-neutral { background: linear-gradient(45deg, #D3D3D3, #A9A9A9); }
        .emotion-fear { background: linear-gradient(45deg, #800080, #9932CC); }
        .emotion-surprise { background: linear-gradient(45deg, #FF8C00, #FF7F50); }
        
        .btn-record {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            font-size: 2rem;
            border: none;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-record.recording {
            background: linear-gradient(45deg, #ff4757, #ff3838);
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .status-message {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
        }
        
        .status-success { background-color: #d4edda; color: #155724; }
        .status-error { background-color: #f8d7da; color: #721c24; }
        .status-info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white text-center">
                        <h3>🎭 Simple Emotion API Test</h3>
                        <p class="mb-0">Test single audio/image emotion analysis</p>
                    </div>
                    <div class="card-body">
                        <!-- Configuration -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="patientId" class="form-label">Patient ID</label>
                                <input type="text" class="form-control" id="patientId" 
                                       value="f31a95c6-76ef-4bb2-936c-b258285682d9">
                            </div>
                            <div class="col-md-6">
                                <label for="conversationId" class="form-label">Conversation ID</label>
                                <input type="text" class="form-control" id="conversationId" 
                                       value="conv-12345">
                            </div>
                        </div>
                        
                        <!-- Audio Test -->
                        <div class="text-center mb-4">
                            <h5>🎤 Audio Emotion Test</h5>
                            <button id="recordBtn" class="btn-record">
                                <i class="fas fa-microphone"></i>
                            </button>
                            <div id="audioStatus" class="mt-2">
                                <small class="text-muted">Click to record audio</small>
                            </div>
                        </div>
                        
                        <!-- Image Test -->
                        <div class="text-center mb-4">
                            <h5>📷 Image Emotion Test</h5>
                            <input type="file" class="form-control mb-2" id="imageFile" accept="image/*">
                            <button id="analyzeImageBtn" class="btn btn-primary" disabled>
                                Analyze Image Emotion
                            </button>
                        </div>
                        
                        <!-- Status Messages -->
                        <div id="statusContainer"></div>
                        
                        <!-- Results -->
                        <div id="resultsContainer"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        // JWT Token - will be fetched from API
        let JWT_TOKEN = null;

        // Global variables
        let mediaRecorder;
        let audioChunks = [];
        let isRecording = false;
        
        // DOM Elements
        const patientIdInput = document.getElementById('patientId');
        const conversationIdInput = document.getElementById('conversationId');
        const recordBtn = document.getElementById('recordBtn');
        const audioStatus = document.getElementById('audioStatus');
        const imageFile = document.getElementById('imageFile');
        const analyzeImageBtn = document.getElementById('analyzeImageBtn');
        const statusContainer = document.getElementById('statusContainer');
        const resultsContainer = document.getElementById('resultsContainer');
        
        // Utility Functions
        function showStatus(message, type = 'info') {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status-message status-${type}`;
            statusDiv.textContent = message;
            statusContainer.innerHTML = '';
            statusContainer.appendChild(statusDiv);
            
            setTimeout(() => {
                statusDiv.remove();
            }, 5000);
        }
        
        function displayResult(type, result) {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'emotion-result';
            
            if (type === 'audio') {
                resultDiv.innerHTML = `
                    <h6>🎤 Audio Emotion Result</h6>
                    <span class="emotion-badge emotion-${result.emotion_data.emotion}">
                        ${result.emotion_data.emotion.toUpperCase()}
                    </span>
                    <div class="mt-3">
                        <strong>Confidence:</strong> ${Math.round(result.emotion_data.confidence * 100)}%<br>
                        <strong>Arousal:</strong> ${Math.round(result.emotion_data.arousal * 100)}%<br>
                        <strong>Valence:</strong> ${Math.round(result.emotion_data.valence * 100)}%<br>
                        <strong>Sample ID:</strong> ${result.sample_id}<br>
                        <strong>Timestamp:</strong> ${new Date(result.timestamp).toLocaleString()}
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <h6>📷 Image Emotion Result</h6>
                    <span class="emotion-badge emotion-${result.emotion_data.emotion}">
                        ${result.emotion_data.emotion.toUpperCase()}
                    </span>
                    <div class="mt-3">
                        <strong>Confidence:</strong> ${Math.round(result.emotion_data.confidence * 100)}%<br>
                        <strong>Sample ID:</strong> ${result.sample_id}<br>
                        <strong>Timestamp:</strong> ${new Date(result.timestamp).toLocaleString()}
                    </div>
                `;
            }
            
            resultsContainer.innerHTML = '';
            resultsContainer.appendChild(resultDiv);
        }
        
        // Audio Recording
        recordBtn.addEventListener('click', async () => {
            // Check if we have a valid token
            if (!JWT_TOKEN) {
                const tokenFetched = await fetchJwtToken();
                if (!tokenFetched) {
                    showStatus('Cannot proceed without authentication token', 'error');
                    return;
                }
            }

            if (!isRecording) {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    mediaRecorder = new MediaRecorder(stream);
                    audioChunks = [];
                    
                    mediaRecorder.ondataavailable = (event) => {
                        audioChunks.push(event.data);
                    };
                    
                    mediaRecorder.onstop = async () => {
                        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                        await analyzeAudio(audioBlob);
                        
                        // Stop all tracks
                        stream.getTracks().forEach(track => track.stop());
                    };
                    
                    mediaRecorder.start();
                    isRecording = true;
                    recordBtn.classList.add('recording');
                    recordBtn.innerHTML = '<i class="fas fa-stop"></i>';
                    audioStatus.innerHTML = '<small class="text-danger">Recording... Click to stop</small>';
                    showStatus('Recording audio...', 'info');
                    
                } catch (error) {
                    console.error('Error accessing microphone:', error);
                    showStatus('Error accessing microphone: ' + error.message, 'error');
                }
            } else {
                mediaRecorder.stop();
                isRecording = false;
                recordBtn.classList.remove('recording');
                recordBtn.innerHTML = '<i class="fas fa-microphone"></i>';
                audioStatus.innerHTML = '<small class="text-muted">Processing audio...</small>';
                showStatus('Processing audio...', 'info');
            }
        });
        
        // Audio Analysis Function
        async function analyzeAudio(audioBlob) {
            try {
                // Convert blob to base64
                const reader = new FileReader();
                reader.onloadend = async () => {
                    const audioBase64 = reader.result;
                    
                    console.log('🎤 Sending SINGLE audio clip for analysis...');
                    
                    const response = await fetch('/conversation-audio/analyze-conversation-audio', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${JWT_TOKEN}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            patient_id: patientIdInput.value,
                            conversation_id: conversationIdInput.value,
                            audio_base64: audioBase64,
                            timestamp: new Date().toISOString()
                        })
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        console.log('✅ Received SINGLE emotion result:', result);
                        displayResult('audio', result);
                        showStatus('Audio emotion analyzed successfully!', 'success');
                    } else {
                        const error = await response.json();
                        showStatus('Error: ' + error.detail, 'error');
                    }
                    
                    audioStatus.innerHTML = '<small class="text-muted">Click to record audio</small>';
                };
                reader.readAsDataURL(audioBlob);
                
            } catch (error) {
                console.error('Error analyzing audio:', error);
                showStatus('Error analyzing audio: ' + error.message, 'error');
                audioStatus.innerHTML = '<small class="text-muted">Click to record audio</small>';
            }
        }
        
        // Image File Selection
        imageFile.addEventListener('change', (event) => {
            const file = event.target.files[0];
            analyzeImageBtn.disabled = !file;
        });
        
        // Image Analysis
        analyzeImageBtn.addEventListener('click', async () => {
            // Check if we have a valid token
            if (!JWT_TOKEN) {
                const tokenFetched = await fetchJwtToken();
                if (!tokenFetched) {
                    showStatus('Cannot proceed without authentication token', 'error');
                    return;
                }
            }

            const file = imageFile.files[0];
            if (!file) return;
            
            try {
                // Convert image to base64
                const reader = new FileReader();
                reader.onloadend = async () => {
                    const imageBase64 = reader.result;
                    
                    console.log('📷 Sending SINGLE image for analysis...');
                    showStatus('Analyzing facial emotion...', 'info');
                    
                    const response = await fetch('/conversation-image/analyze-conversation-image', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${JWT_TOKEN}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            patient_id: patientIdInput.value,
                            conversation_id: conversationIdInput.value,
                            image_base64: imageBase64,
                            timestamp: new Date().toISOString()
                        })
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        console.log('✅ Received SINGLE emotion result:', result);
                        displayResult('image', result);
                        showStatus('Image emotion analyzed successfully!', 'success');
                    } else {
                        const error = await response.json();
                        showStatus('Error: ' + error.detail, 'error');
                    }
                };
                reader.readAsDataURL(file);
                
            } catch (error) {
                console.error('Error analyzing image:', error);
                showStatus('Error analyzing image: ' + error.message, 'error');
            }
        });
        
        // Fetch JWT token from API
        async function fetchJwtToken() {
            try {
                showStatus('Fetching authentication token...', 'info');
                const response = await fetch('/auth/token');
                if (response.ok) {
                    const data = await response.json();
                    JWT_TOKEN = data.access_token;
                    showStatus('Authentication token fetched successfully', 'success');
                    return true;
                } else {
                    const error = await response.json();
                    showStatus('Error fetching token: ' + error.detail, 'error');
                    return false;
                }
            } catch (error) {
                console.error('Error fetching token:', error);
                showStatus('Error fetching token: ' + error.message, 'error');
                return false;
            }
        }

        // Demo: How frontend would call during chat
        function demonstrateChatIntegration() {
            console.log(`
🎯 CHAT INTEGRATION EXAMPLE:

// During a conversation, when user speaks:
const audioClip = recordUserSpeech(); // Get single audio clip
const audioResult = await analyzeAudio(audioClip); // Get ONLY this emotion
console.log('Current audio emotion:', audioResult.emotion_data.emotion);

// During a conversation, capture user's face:
const faceImage = captureUserFace(); // Get single image
const imageResult = await analyzeImage(faceImage); // Get ONLY this emotion  
console.log('Current facial emotion:', imageResult.emotion_data.emotion);

// Use emotions to inform AI response:
const aiResponse = generateResponse({
    userMessage: "I'm feeling stressed",
    currentAudioEmotion: audioResult.emotion_data.emotion,
    currentFacialEmotion: imageResult.emotion_data.emotion
});
            `);
        }
        
        // Initialize on page load
        window.addEventListener('load', async () => {
            // Fetch token on page load
            await fetchJwtToken();
            // Show demo in console
            demonstrateChatIntegration();
        });
    </script>
</body>
</html>
