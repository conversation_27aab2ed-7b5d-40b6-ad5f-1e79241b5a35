<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Emotion Recognition</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            padding-top: 50px;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            border: none;
        }
        
        .card-header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            text-align: center;
            padding: 20px;
        }
        
        .record-btn {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: none;
            font-size: 2rem;
            transition: all 0.3s ease;
            margin: 20px auto;
            display: block;
        }
        
        .record-btn.recording {
            background: linear-gradient(45deg, #ff4757, #ff3838);
            color: white;
            animation: pulse 1.5s infinite;
        }
        
        .record-btn:not(.recording) {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .emotion-result {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .emotion-badge {
            font-size: 1.5rem;
            padding: 10px 20px;
            border-radius: 25px;
            margin: 10px;
            display: inline-block;
        }
        
        .emotion-happy { background: linear-gradient(45deg, #FFD700, #FFA500); }
        .emotion-sad { background: linear-gradient(45deg, #4682B4, #1E90FF); }
        .emotion-angry { background: linear-gradient(45deg, #FF6347, #DC143C); }
        .emotion-excited { background: linear-gradient(45deg, #FF69B4, #FF1493); }
        .emotion-calm { background: linear-gradient(45deg, #98FB98, #90EE90); }
        .emotion-neutral { background: linear-gradient(45deg, #D3D3D3, #A9A9A9); }
        
        .progress-container {
            margin: 20px 0;
        }
        
        .session-history {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .emotion-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .status-message {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
        }
        
        .status-success { background-color: #d4edda; color: #155724; }
        .status-error { background-color: #f8d7da; color: #721c24; }
        .status-info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-microphone me-2"></i>Audio Emotion Recognition</h2>
                        <p class="mb-0">Record your voice to analyze emotional state</p>
                    </div>
                    <div class="card-body">
                        <!-- Session ID Input -->
                        <div class="mb-3">
                            <label for="sessionId" class="form-label">Session ID</label>
                            <input type="text" class="form-control" id="sessionId" placeholder="Enter session ID or leave blank for auto-generation">
                        </div>
                        
                        <!-- Recording Controls -->
                        <div class="text-center">
                            <button id="recordBtn" class="record-btn">
                                <i class="fas fa-microphone"></i>
                            </button>
                            <div id="recordingStatus" class="mt-2">
                                <span class="text-muted">Click to start recording</span>
                            </div>
                        </div>
                        
                        <!-- File Upload Alternative -->
                        <div class="mt-4">
                            <label for="audioFile" class="form-label">Or upload an audio file:</label>
                            <input type="file" class="form-control" id="audioFile" accept="audio/*">
                            <button id="uploadBtn" class="btn btn-primary mt-2" disabled>
                                <i class="fas fa-upload me-2"></i>Analyze Uploaded Audio
                            </button>
                        </div>
                        
                        <!-- Status Messages -->
                        <div id="statusContainer"></div>
                        
                        <!-- Emotion Results -->
                        <div id="emotionResults" style="display: none;">
                            <h4 class="text-center mt-4">Emotion Analysis Results</h4>
                            <div class="emotion-result">
                                <div id="emotionDisplay"></div>
                                <div class="row mt-3">
                                    <div class="col-md-4">
                                        <strong>Confidence:</strong>
                                        <div class="progress mt-2">
                                            <div id="confidenceBar" class="progress-bar bg-success" role="progressbar"></div>
                                        </div>
                                        <span id="confidenceText"></span>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Arousal:</strong>
                                        <div class="progress mt-2">
                                            <div id="arousalBar" class="progress-bar bg-warning" role="progressbar"></div>
                                        </div>
                                        <span id="arousalText"></span>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Valence:</strong>
                                        <div class="progress mt-2">
                                            <div id="valenceBar" class="progress-bar bg-info" role="progressbar"></div>
                                        </div>
                                        <span id="valenceText"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Session History -->
                        <div class="mt-4">
                            <h4>Session History</h4>
                            <button id="loadHistoryBtn" class="btn btn-outline-primary mb-3">
                                <i class="fas fa-history me-2"></i>Load Session History
                            </button>
                            <div id="sessionHistory" class="session-history"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let mediaRecorder;
        let audioChunks = [];
        let isRecording = false;
        
        // JWT Token (you should get this from your authentication system)
        const JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.Ij0BnOgMwKFwB7VOzRdL-qdpEXNF7YvZZ8R_c-vKbcM";
        
        // DOM Elements
        const recordBtn = document.getElementById('recordBtn');
        const recordingStatus = document.getElementById('recordingStatus');
        const audioFileInput = document.getElementById('audioFile');
        const uploadBtn = document.getElementById('uploadBtn');
        const sessionIdInput = document.getElementById('sessionId');
        const statusContainer = document.getElementById('statusContainer');
        const emotionResults = document.getElementById('emotionResults');
        const loadHistoryBtn = document.getElementById('loadHistoryBtn');
        const sessionHistory = document.getElementById('sessionHistory');
        
        // Generate session ID if not provided
        function getSessionId() {
            let sessionId = sessionIdInput.value.trim();
            if (!sessionId) {
                sessionId = 'session_' + Date.now();
                sessionIdInput.value = sessionId;
            }
            return sessionId;
        }
        
        // Show status message
        function showStatus(message, type = 'info') {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status-message status-${type}`;
            statusDiv.textContent = message;
            statusContainer.innerHTML = '';
            statusContainer.appendChild(statusDiv);
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                statusDiv.remove();
            }, 5000);
        }
        
        // Start/Stop Recording
        recordBtn.addEventListener('click', async () => {
            if (!isRecording) {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    mediaRecorder = new MediaRecorder(stream);
                    audioChunks = [];
                    
                    mediaRecorder.ondataavailable = (event) => {
                        audioChunks.push(event.data);
                    };
                    
                    mediaRecorder.onstop = () => {
                        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                        analyzeAudioBlob(audioBlob);
                        
                        // Stop all tracks
                        stream.getTracks().forEach(track => track.stop());
                    };
                    
                    mediaRecorder.start();
                    isRecording = true;
                    recordBtn.classList.add('recording');
                    recordBtn.innerHTML = '<i class="fas fa-stop"></i>';
                    recordingStatus.innerHTML = '<span class="text-danger">Recording... Click to stop</span>';
                    showStatus('Recording started', 'info');
                    
                } catch (error) {
                    console.error('Error accessing microphone:', error);
                    showStatus('Error accessing microphone: ' + error.message, 'error');
                }
            } else {
                mediaRecorder.stop();
                isRecording = false;
                recordBtn.classList.remove('recording');
                recordBtn.innerHTML = '<i class="fas fa-microphone"></i>';
                recordingStatus.innerHTML = '<span class="text-muted">Processing audio...</span>';
                showStatus('Processing audio...', 'info');
            }
        });
        
        // File upload handling
        audioFileInput.addEventListener('change', (event) => {
            const file = event.target.files[0];
            uploadBtn.disabled = !file;
        });
        
        uploadBtn.addEventListener('click', () => {
            const file = audioFileInput.files[0];
            if (file) {
                analyzeAudioFile(file);
            }
        });
        
        // Analyze audio blob from recording
        async function analyzeAudioBlob(audioBlob) {
            try {
                const sessionId = getSessionId();
                const formData = new FormData();
                formData.append('audio_file', audioBlob, 'recording.wav');
                formData.append('session_id', sessionId);
                
                const response = await fetch('/audio-emotion/analyze-audio', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${JWT_TOKEN}`
                    },
                    body: formData
                });
                
                if (response.ok) {
                    const result = await response.json();
                    displayEmotionResult(result);
                    showStatus('Audio analysis completed successfully!', 'success');
                } else {
                    const error = await response.json();
                    showStatus('Error: ' + error.detail, 'error');
                }
                
                recordingStatus.innerHTML = '<span class="text-muted">Click to start recording</span>';
                
            } catch (error) {
                console.error('Error analyzing audio:', error);
                showStatus('Error analyzing audio: ' + error.message, 'error');
                recordingStatus.innerHTML = '<span class="text-muted">Click to start recording</span>';
            }
        }
        
        // Analyze uploaded audio file
        async function analyzeAudioFile(file) {
            try {
                const sessionId = getSessionId();
                const formData = new FormData();
                formData.append('audio_file', file);
                formData.append('session_id', sessionId);
                
                showStatus('Analyzing uploaded audio...', 'info');
                
                const response = await fetch('/audio-emotion/analyze-audio', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${JWT_TOKEN}`
                    },
                    body: formData
                });
                
                if (response.ok) {
                    const result = await response.json();
                    displayEmotionResult(result);
                    showStatus('Audio analysis completed successfully!', 'success');
                } else {
                    const error = await response.json();
                    showStatus('Error: ' + error.detail, 'error');
                }
                
            } catch (error) {
                console.error('Error analyzing audio file:', error);
                showStatus('Error analyzing audio: ' + error.message, 'error');
            }
        }
        
        // Display emotion analysis results
        function displayEmotionResult(result) {
            const emotionDisplay = document.getElementById('emotionDisplay');
            const confidenceBar = document.getElementById('confidenceBar');
            const arousalBar = document.getElementById('arousalBar');
            const valenceBar = document.getElementById('valenceBar');
            const confidenceText = document.getElementById('confidenceText');
            const arousalText = document.getElementById('arousalText');
            const valenceText = document.getElementById('valenceText');
            
            // Display emotion with appropriate styling
            emotionDisplay.innerHTML = `
                <span class="emotion-badge emotion-${result.emotion}">
                    ${result.emotion.toUpperCase()}
                </span>
            `;
            
            // Update progress bars
            const confidencePercent = Math.round(result.confidence * 100);
            const arousalPercent = Math.round(result.arousal * 100);
            const valencePercent = Math.round(result.valence * 100);
            
            confidenceBar.style.width = confidencePercent + '%';
            confidenceBar.textContent = confidencePercent + '%';
            confidenceText.textContent = confidencePercent + '%';
            
            arousalBar.style.width = arousalPercent + '%';
            arousalBar.textContent = arousalPercent + '%';
            arousalText.textContent = arousalPercent + '%';
            
            valenceBar.style.width = valencePercent + '%';
            valenceBar.textContent = valencePercent + '%';
            valenceText.textContent = valencePercent + '%';
            
            emotionResults.style.display = 'block';
        }
        
        // Load session history
        loadHistoryBtn.addEventListener('click', async () => {
            try {
                const sessionId = getSessionId();
                showStatus('Loading session history...', 'info');
                
                const response = await fetch(`/audio-emotion/session/${sessionId}`, {
                    headers: {
                        'Authorization': `Bearer ${JWT_TOKEN}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    displaySessionHistory(data);
                    showStatus(`Loaded ${data.total_samples} emotion samples`, 'success');
                } else {
                    const error = await response.json();
                    showStatus('Error loading history: ' + error.detail, 'error');
                }
                
            } catch (error) {
                console.error('Error loading session history:', error);
                showStatus('Error loading history: ' + error.message, 'error');
            }
        });
        
        // Display session history
        function displaySessionHistory(data) {
            sessionHistory.innerHTML = '';
            
            if (data.emotions.length === 0) {
                sessionHistory.innerHTML = '<p class="text-muted text-center">No emotion data found for this session.</p>';
                return;
            }
            
            data.emotions.forEach(emotion => {
                const emotionItem = document.createElement('div');
                emotionItem.className = 'emotion-item';
                emotionItem.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="emotion-badge emotion-${emotion.emotion}" style="font-size: 1rem; padding: 5px 10px;">
                                ${emotion.emotion}
                            </span>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">${new Date(emotion.timestamp).toLocaleString()}</small><br>
                            <small>Confidence: ${Math.round(emotion.confidence * 100)}%</small>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small>Arousal: ${Math.round(emotion.arousal * 100)}% | Valence: ${Math.round(emotion.valence * 100)}%</small>
                    </div>
                `;
                sessionHistory.appendChild(emotionItem);
            });
        }
        
        // Auto-generate session ID on page load
        window.addEventListener('load', () => {
            if (!sessionIdInput.value) {
                sessionIdInput.value = 'session_' + Date.now();
            }
        });
    </script>
</body>
</html>
