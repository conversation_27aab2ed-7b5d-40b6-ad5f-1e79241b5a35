<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Doctor Patient Insights</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 15px;
        }
        .insights-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .insight-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            transition: transform 0.3s ease;
        }
        .insight-card:hover {
            transform: translateY(-5px);
        }
        .insight-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .insight-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #eaf2f8;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: #2c3e50;
        }
        .insight-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }
        .insight-description {
            color: #555;
            margin-bottom: 15px;
        }
        .chart-container {
            height: 200px;
            margin-top: 15px;
        }
        .progress-container {
            margin-top: 15px;
        }
        .progress {
            height: 10px;
            border-radius: 5px;
        }
        .analysis-list {
            list-style-type: none;
            padding: 0;
        }
        .analysis-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .analysis-list li:last-child {
            border-bottom: none;
        }
        .analysis-category {
            font-weight: 600;
            color: #2c3e50;
            margin-right: 10px;
        }
        .emotion-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .emotion-item {
            background-color: #f0f2f5;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9rem;
        }
        .login-container {
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .error-message {
            color: #dc3545;
            margin-top: 10px;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #2c3e50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .patient-info {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .patient-info h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .patient-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .patient-info-item {
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .patient-info-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .badge-high {
            background-color: #dc3545;
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.8rem;
            margin-left: 5px;
        }
        .badge-medium {
            background-color: #ffc107;
            color: #212529;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.8rem;
            margin-left: 5px;
        }
        .badge-low {
            background-color: #28a745;
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.8rem;
            margin-left: 5px;
        }
        .insight-category {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 2px;
        }
        .insight-evidence-action {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-size: 0.9rem;
        }
        .insight-evidence, .insight-action {
            margin-bottom: 5px;
        }
        .insight-action {
            color: #0d6efd;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-user-md me-2"></i> Doctor Patient Insights</h1>
        <p>Clinical insights to help you provide better care for your patients</p>
    </div>

    <div class="container">
        <div id="loginForm" class="login-container">
            <h2 class="mb-4">View Patient Insights</h2>
            <div class="mb-3">
                <label for="patientId" class="form-label">Patient ID</label>
                <input type="text" class="form-control" id="patientId" placeholder="f31a95c6-76ef-4bb2-936c-b258285682d9">
            </div>
            <button id="loginBtn" class="btn btn-primary w-100">View Patient Insights</button>
            <div id="loginError" class="error-message"></div>
        </div>

        <div id="insightsView" style="display: none;">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Patient Clinical Insights</h2>
                <button id="refreshBtn" class="btn btn-outline-primary">
                    <i class="fas fa-sync-alt me-2"></i> Refresh Insights
                </button>
            </div>

            <div id="patientInfoContainer" class="patient-info">
                <!-- Patient info will be inserted here -->
            </div>

            <div id="loadingIndicator" class="loading">
                <div class="spinner"></div>
            </div>

            <div id="insightsContainer" class="insights-container"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const insightsView = document.getElementById('insightsView');
            const patientIdInput = document.getElementById('patientId');
            const loginBtn = document.getElementById('loginBtn');
            const loginError = document.getElementById('loginError');
            const refreshBtn = document.getElementById('refreshBtn');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const insightsContainer = document.getElementById('insightsContainer');
            const patientInfoContainer = document.getElementById('patientInfoContainer');

            // Hardcoded JWT token - no need for user to enter it
            const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.3oZ2Ubh5rLBdHvQHd5Qr9GJczA5MXcxaVx5H5xLwvZ4';

            // Check if we have stored patient ID
            const storedPatientId = localStorage.getItem('patientId');

            if (storedPatientId) {
                patientIdInput.value = storedPatientId;
                // Auto-load insights if we have a stored patient ID
                fetchInsights(storedPatientId);
            }

            loginBtn.addEventListener('click', function() {
                const patientId = patientIdInput.value.trim();

                if (!patientId) {
                    loginError.textContent = 'Please enter a patient ID';
                    return;
                }

                // Store patient ID for next time
                localStorage.setItem('patientId', patientId);

                fetchInsights(patientId);
            });

            refreshBtn.addEventListener('click', function() {
                const patientId = localStorage.getItem('patientId');

                if (patientId) {
                    fetchInsights(patientId);
                }
            });

            function fetchInsights(patientId) {
                // Show loading indicator
                loginForm.style.display = 'none';
                insightsView.style.display = 'block';
                loadingIndicator.style.display = 'flex';
                insightsContainer.innerHTML = '';
                patientInfoContainer.innerHTML = '';

                // Fetch insights directly using the hardcoded JWT token
                fetch(`/doctor-insights/doctor/patient/${patientId}`, {
                    headers: {
                        'Authorization': `Bearer ${JWT_TOKEN}`
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loading indicator
                    loadingIndicator.style.display = 'none';

                    // Create basic patient info from the first insight if available
                    const patientInfo = {
                        name: `Patient ${patientId.substring(0, 8)}...`,
                        age: "Not available",
                        gender: "Not available"
                    };

                    // Try to extract more patient info from insights if possible
                    if (data.insights && data.insights.length > 0) {
                        // Look for patient info in the insights data
                        for (const insight of data.insights) {
                            if (insight.data && insight.data.patient_info) {
                                Object.assign(patientInfo, insight.data.patient_info);
                                break;
                            }
                        }
                    }

                    // Display patient info
                    displayPatientInfo(patientInfo);

                    // Display insights
                    displayInsights(data.insights);
                })
                .catch(error => {
                    console.error('Error fetching insights:', error);
                    loadingIndicator.style.display = 'none';
                    insightsContainer.innerHTML = `
                        <div class="alert alert-danger w-100">
                            <h4>Error Loading Insights</h4>
                            <p>${error.message}</p>
                            <button class="btn btn-outline-danger mt-2" onclick="location.reload()">Try Again</button>
                        </div>
                    `;

                    // Show login form again if authentication failed
                    if (error.message.includes('401') || error.message.includes('403')) {
                        loginForm.style.display = 'block';
                        insightsView.style.display = 'none';
                        loginError.textContent = 'Authentication failed. Please check your credentials.';

                        // Clear stored credentials
                        localStorage.removeItem('doctorId');
                        localStorage.removeItem('patientId');
                        localStorage.removeItem('token');
                    }
                });
            }

            function displayPatientInfo(patient) {
                // If we don't have patient data, show a placeholder
                if (!patient || Object.keys(patient).length === 0) {
                    patientInfoContainer.innerHTML = `
                        <h3>Patient Information</h3>
                        <p>Patient information not available</p>
                    `;
                    return;
                }

                // Create patient info HTML
                let patientInfoHTML = `
                    <h3>Patient Information</h3>
                    <div class="patient-info-grid">
                        <div class="patient-info-item">
                            <div class="patient-info-label">Name</div>
                            <div>${patient.name || 'Not available'}</div>
                        </div>
                        <div class="patient-info-item">
                            <div class="patient-info-label">Age</div>
                            <div>${patient.age || 'Not available'}</div>
                        </div>
                        <div class="patient-info-item">
                            <div class="patient-info-label">Gender</div>
                            <div>${patient.gender || 'Not available'}</div>
                        </div>
                `;

                // Add health score if available
                if (patient.health_score !== undefined) {
                    let healthScoreBadge = '';
                    if (patient.health_score < 50) {
                        healthScoreBadge = '<span class="badge-high">Low</span>';
                    } else if (patient.health_score < 75) {
                        healthScoreBadge = '<span class="badge-medium">Medium</span>';
                    } else {
                        healthScoreBadge = '<span class="badge-low">High</span>';
                    }

                    patientInfoHTML += `
                        <div class="patient-info-item">
                            <div class="patient-info-label">Health Score</div>
                            <div>${patient.health_score}${healthScoreBadge}</div>
                        </div>
                    `;
                }

                // Add medication status if available
                if (patient.under_medications !== undefined) {
                    patientInfoHTML += `
                        <div class="patient-info-item">
                            <div class="patient-info-label">Under Medication</div>
                            <div>${patient.under_medications ? 'Yes' : 'No'}</div>
                        </div>
                    `;
                }

                patientInfoHTML += '</div>';
                patientInfoContainer.innerHTML = patientInfoHTML;
            }

            function displayInsights(insights) {
                if (!insights || insights.length === 0) {
                    insightsContainer.innerHTML = `
                        <div class="alert alert-info w-100">
                            <h4>No Insights Available</h4>
                            <p>We don't have any insights for this patient at the moment.</p>
                        </div>
                    `;
                    return;
                }

                // Clear container
                insightsContainer.innerHTML = '';

                // Add each insight
                insights.forEach(insight => {
                    const insightCard = document.createElement('div');
                    insightCard.className = 'insight-card';

                    // Determine icon based on insight type
                    let iconClass = 'fas fa-info-circle';
                    switch (insight.type) {
                        case 'chart':
                            iconClass = 'fas fa-chart-line';
                            break;
                        case 'progress':
                            iconClass = 'fas fa-tasks';
                            break;
                        case 'emotion':
                            iconClass = 'fas fa-smile';
                            break;
                        case 'analysis':
                            iconClass = 'fas fa-clipboard-check';
                            break;
                    }

                    // Determine priority badge
                    let priorityBadge = '';
                    if (insight.priority >= 5) {
                        priorityBadge = '<span class="badge-high">High Priority</span>';
                    } else if (insight.priority >= 3) {
                        priorityBadge = '<span class="badge-medium">Medium Priority</span>';
                    } else {
                        priorityBadge = '<span class="badge-low">Low Priority</span>';
                    }

                    // Determine confidence badge
                    let confidenceBadge = '';
                    if (insight.confidence === 'high') {
                        confidenceBadge = '<span class="badge-low">High Confidence</span>';
                    } else if (insight.confidence === 'medium') {
                        confidenceBadge = '<span class="badge-medium">Medium Confidence</span>';
                    } else {
                        confidenceBadge = '<span class="badge-high">Low Confidence</span>';
                    }

                    // Create header
                    const header = `
                        <div class="insight-header">
                            <div class="insight-icon">
                                <i class="${iconClass}"></i>
                            </div>
                            <div>
                                <h3 class="insight-title">${insight.title} ${priorityBadge} ${confidenceBadge}</h3>
                                <div class="insight-category">${insight.category || 'Clinical Insight'}</div>
                            </div>
                        </div>
                        <p class="insight-description">${insight.description}</p>
                    `;

                    // Add evidence and suggested action if available
                    let evidenceAndAction = '';
                    if (insight.evidence || insight.suggested_action) {
                        evidenceAndAction = `
                            <div class="insight-evidence-action">
                                ${insight.evidence ? `<div class="insight-evidence"><strong>Evidence:</strong> ${insight.evidence}</div>` : ''}
                                ${insight.suggested_action ? `<div class="insight-action"><strong>Suggested Action:</strong> ${insight.suggested_action}</div>` : ''}
                            </div>
                        `;
                    }

                    // Create content based on insight type
                    let content = evidenceAndAction;

                    if (insight.type === 'chart' && insight.data && insight.data.dates && insight.data.scores) {
                        const chartId = `chart-${Math.random().toString(36).substr(2, 9)}`;
                        content = `<div class="chart-container"><canvas id="${chartId}"></canvas></div>`;

                        // Add chart initialization after DOM is updated
                        setTimeout(() => {
                            const ctx = document.getElementById(chartId).getContext('2d');
                            new Chart(ctx, {
                                type: 'line',
                                data: {
                                    labels: insight.data.dates,
                                    datasets: [{
                                        label: 'Health Score',
                                        data: insight.data.scores,
                                        backgroundColor: 'rgba(44, 62, 80, 0.2)',
                                        borderColor: 'rgba(44, 62, 80, 1)',
                                        borderWidth: 2,
                                        tension: 0.3,
                                        pointBackgroundColor: 'rgba(44, 62, 80, 1)'
                                    }]
                                },
                                options: {
                                    scales: {
                                        y: {
                                            beginAtZero: false,
                                            min: Math.max(0, Math.min(...insight.data.scores) - 10),
                                            max: Math.min(100, Math.max(...insight.data.scores) + 10)
                                        }
                                    },
                                    responsive: true,
                                    maintainAspectRatio: false
                                }
                            });
                        }, 0);
                    } else if (insight.type === 'progress' && insight.data && insight.data.adherence_rate !== null) {
                        const adherenceRate = insight.data.adherence_rate;
                        const progressColor = adherenceRate >= 90 ? 'success' :
                                            adherenceRate >= 70 ? 'info' :
                                            adherenceRate >= 50 ? 'warning' : 'danger';

                        content = `
                            <div class="progress-container">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Medication Adherence</span>
                                    <span>${adherenceRate}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-${progressColor}" role="progressbar"
                                        style="width: ${adherenceRate}%" aria-valuenow="${adherenceRate}"
                                        aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="mt-3">
                                    <small>Taken: ${insight.data.total_doses - insight.data.missed_doses} of ${insight.data.total_doses} doses</small>
                                </div>
                            </div>
                        `;

                        if (insight.data.medications && insight.data.medications.length > 0) {
                            content += '<div class="mt-3"><strong>Medication Details:</strong></div>';
                            content += '<ul class="analysis-list mt-2">';
                            insight.data.medications.forEach(med => {
                                content += `
                                    <li>
                                        <div>${med.name}</div>
                                        <small>Adherence: ${med.adherence_rate}% (${med.prescribed_doses - med.missed_doses}/${med.prescribed_doses} doses)</small>
                                    </li>
                                `;
                            });
                            content += '</ul>';
                        }
                    } else if (insight.type === 'analysis' && insight.data) {
                        content = '<ul class="analysis-list">';

                        if (insight.data.improvement_areas && insight.data.improvement_areas.length > 0) {
                            content += `
                                <li>
                                    <span class="analysis-category">Areas of Improvement:</span>
                                    ${insight.data.improvement_areas.join(', ')}
                                </li>
                            `;
                        }

                        if (insight.data.concerns && insight.data.concerns.length > 0) {
                            content += `
                                <li>
                                    <span class="analysis-category">Clinical Concerns:</span>
                                    ${insight.data.concerns.join(', ')}
                                </li>
                            `;
                        }

                        if (insight.data.recommendations && insight.data.recommendations.length > 0) {
                            content += `
                                <li>
                                    <span class="analysis-category">Recommendations:</span>
                                    <ul>
                                        ${insight.data.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                                    </ul>
                                </li>
                            `;
                        }

                        content += '</ul>';
                    } else if (insight.type === 'emotion' && insight.data) {
                        content = `
                            <div>
                                <div class="mb-2">
                                    <strong>Dominant Emotion:</strong> ${insight.data.dominant_emotion}
                                </div>
                            `;

                        if (insight.data.emotion_distribution && Object.keys(insight.data.emotion_distribution).length > 0) {
                            content += '<div class="emotion-container">';
                            for (const [emotion, value] of Object.entries(insight.data.emotion_distribution)) {
                                content += `
                                    <div class="emotion-item">
                                        ${emotion}: ${typeof value === 'number' ? Math.round(value * 100) + '%' : value}
                                    </div>
                                `;
                            }
                            content += '</div>';
                        }

                        if (insight.data.recent_entries && insight.data.recent_entries.length > 0) {
                            content += '<div class="mt-3"><strong>Recent Diary Entries:</strong></div>';
                            content += '<ul class="analysis-list mt-2">';
                            insight.data.recent_entries.slice(0, 3).forEach(entry => {
                                content += `
                                    <li>
                                        <div>${entry.date}</div>
                                        <small>${entry.content}</small>
                                    </li>
                                `;
                            });
                            content += '</ul>';
                        }

                        content += '</div>';
                    }

                    insightCard.innerHTML = header + content;
                    insightsContainer.appendChild(insightCard);
                });
            }
        });
    </script>
</body>
</html>
