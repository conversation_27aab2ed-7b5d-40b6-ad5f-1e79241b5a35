<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI <PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
        }

        .login-form, .chat-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .login-form {
            text-align: center;
        }

        .login-form h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2rem;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e1e1;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .persona-selection {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 10px;
        }

        .persona-option {
            padding: 15px;
            border: 2px solid #e1e1e1;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .persona-option:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .persona-option.selected {
            border-color: #667eea;
            background-color: #667eea;
            color: white;
        }

        .persona-option h3 {
            margin-bottom: 5px;
        }

        .persona-option p {
            font-size: 14px;
            opacity: 0.8;
        }

        .login-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            color: #e74c3c;
            margin-top: 15px;
            padding: 10px;
            background-color: #fdf2f2;
            border-radius: 5px;
            border-left: 4px solid #e74c3c;
        }

        .chat-container {
            display: none;
            max-width: 800px;
            height: 600px;
            flex-direction: column;
        }

        .chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 20px;
        }

        .chat-header h2 {
            color: #333;
        }

        .persona-indicator {
            padding: 8px 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }

        .logout-btn {
            padding: 8px 16px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            margin-bottom: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            background: #fafafa;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background: #667eea;
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }

        .message.assistant {
            background: white;
            color: #333;
            border: 2px solid #e1e1e1;
            margin-right: auto;
            border-bottom-left-radius: 5px;
        }

        .message.streaming {
            border-left: 4px solid #667eea;
            animation: pulse-border 1.5s infinite;
        }

        @keyframes pulse-border {
            0% { border-left-color: #667eea; }
            50% { border-left-color: #764ba2; }
            100% { border-left-color: #667eea; }
        }

        .typing-indicator {
            display: none;
            background: white;
            color: #666;
            margin-right: auto;
            font-style: italic;
            border: 2px solid #e1e1e1;
        }

        .chat-input {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e1e1e1;
            border-radius: 25px;
            font-size: 16px;
        }

        .chat-input input:focus {
            outline: none;
            border-color: #667eea;
        }

        .send-btn, .voice-btn {
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s ease;
        }

        .send-btn:hover, .voice-btn:hover {
            transform: translateY(-2px);
        }

        .voice-btn.recording {
            background: #e74c3c;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 20px;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .connection-status.connected {
            background: #27ae60;
        }

        .connection-status.disconnected {
            background: #e74c3c;
        }

        .connection-status.connecting {
            background: #f39c12;
        }

        .transcription-display {
            margin-top: 10px;
            padding: 10px;
            background: #e8f4f8;
            border-radius: 10px;
            font-style: italic;
            color: #2c3e50;
        }

        .audio-controls {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .audio-player {
            flex: 1;
        }

        .hidden {
            display: none !important;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .chat-container {
                height: 500px;
                padding: 20px;
            }
            
            .persona-selection {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Login Form -->
        <div class="login-form" id="loginForm">
            <h1>🏥 AI Doctor Chat</h1>
            <form id="authForm">
                <div class="form-group">
                    <label for="patientId">Patient ID</label>
                    <input type="text" id="patientId" name="patientId" required placeholder="Enter your patient ID">
                </div>
                
                <div class="form-group">
                    <label for="token">Authentication Token</label>
                    <input type="password" id="token" name="token" required placeholder="Enter your authentication token">
                </div>
                
                <div class="form-group">
                    <label>Select Doctor Persona</label>
                    <div class="persona-selection">
                        <div class="persona-option selected" data-persona="psychologist">
                            <h3>🧠 Dr. Ori</h3>
                            <p>Psychologist</p>
                        </div>
                        <div class="persona-option" data-persona="dietician">
                            <h3>🥗 Dr. Maya</h3>
                            <p>Dietician</p>
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="login-btn" id="loginBtn">Connect to Doctor</button>
            </form>
            
            <div class="error-message hidden" id="errorMessage"></div>
        </div>

        <!-- Chat Interface -->
        <div class="chat-container" id="chatContainer">
            <div class="chat-header">
                <h2 id="doctorName">Dr. Ori - Psychologist</h2>
                <div class="persona-indicator" id="personaIndicator">Psychologist</div>
                <button class="logout-btn" id="logoutBtn">Logout</button>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <!-- Messages will be added here dynamically -->
            </div>
            
            <div class="typing-indicator message" id="typingIndicator">
                Doctor is typing...
            </div>
            
            <div class="transcription-display hidden" id="transcriptionDisplay">
                <strong>Transcription:</strong> <span id="transcriptionText"></span>
            </div>
            
            <div class="chat-input">
                <input type="text" id="messageInput" placeholder="Type your message..." maxlength="1000">
                <button class="voice-btn" id="voiceBtn">🎤 Voice</button>
                <button class="send-btn" id="sendBtn">Send</button>
            </div>
            
            <div class="audio-controls hidden" id="audioControls">
                <audio class="audio-player" id="audioPlayer" controls></audio>
            </div>
        </div>
    </div>

    <!-- Connection Status -->
    <div class="connection-status disconnected" id="connectionStatus">Disconnected</div>

    <script>
        class ChatApp {
            constructor() {
                this.ws = null;
                this.currentPersona = 'psychologist';
                this.patientId = '';
                this.isRecording = false;
                this.mediaRecorder = null;
                this.audioChunks = [];
                this.heartbeatInterval = null;
                this.currentStreamingMessage = null; // Track current streaming message
                this.audioQueue = []; // Queue for streaming audio
                
                this.initializeElements();
                this.attachEventListeners();
            }

            initializeElements() {
                // Login form elements
                this.loginForm = document.getElementById('loginForm');
                this.authForm = document.getElementById('authForm');
                this.patientIdInput = document.getElementById('patientId');
                this.tokenInput = document.getElementById('token');
                this.loginBtn = document.getElementById('loginBtn');
                this.errorMessage = document.getElementById('errorMessage');
                
                // Chat elements
                this.chatContainer = document.getElementById('chatContainer');
                this.chatMessages = document.getElementById('chatMessages');
                this.messageInput = document.getElementById('messageInput');
                this.sendBtn = document.getElementById('sendBtn');
                this.voiceBtn = document.getElementById('voiceBtn');
                this.logoutBtn = document.getElementById('logoutBtn');
                this.typingIndicator = document.getElementById('typingIndicator');
                this.connectionStatus = document.getElementById('connectionStatus');
                this.doctorName = document.getElementById('doctorName');
                this.personaIndicator = document.getElementById('personaIndicator');
                this.transcriptionDisplay = document.getElementById('transcriptionDisplay');
                this.transcriptionText = document.getElementById('transcriptionText');
                this.audioControls = document.getElementById('audioControls');
                this.audioPlayer = document.getElementById('audioPlayer');
            }

            attachEventListeners() {
                // Persona selection
                document.querySelectorAll('.persona-option').forEach(option => {
                    option.addEventListener('click', (e) => {
                        document.querySelectorAll('.persona-option').forEach(opt => opt.classList.remove('selected'));
                        e.currentTarget.classList.add('selected');
                        this.currentPersona = e.currentTarget.dataset.persona;
                    });
                });

                // Form submission
                this.authForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleLogin();
                });

                // Chat input
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.sendMessage();
                    }
                });

                this.sendBtn.addEventListener('click', () => this.sendMessage());
                this.voiceBtn.addEventListener('click', () => this.toggleVoiceRecording());
                this.logoutBtn.addEventListener('click', () => this.logout());
                
                // Auto-focus on message input when chat is visible
                this.messageInput.addEventListener('focus', () => {
                    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                });
            }

            showError(message) {
                this.errorMessage.textContent = message;
                this.errorMessage.classList.remove('hidden');
                setTimeout(() => {
                    this.errorMessage.classList.add('hidden');
                }, 5000);
            }

            updateConnectionStatus(status) {
                this.connectionStatus.className = `connection-status ${status}`;
                const statusText = {
                    'connected': 'Connected',
                    'connecting': 'Connecting...',
                    'disconnected': 'Disconnected'
                };
                this.connectionStatus.textContent = statusText[status] || 'Unknown';
            }

            handleLogin() {
                const patientId = this.patientIdInput.value.trim();
                const token = this.tokenInput.value.trim();
                
                if (!patientId || !token) {
                    this.showError('Please fill in all fields');
                    return;
                }

                this.patientId = patientId;
                this.loginBtn.disabled = true;
                this.loginBtn.textContent = 'Connecting...';
                
                this.connectWebSocket(patientId, token);
            }

            connectWebSocket(patientId, token) {
                this.updateConnectionStatus('connecting');
                
                // Use the appropriate WebSocket URL - adjust this based on your server setup
                const wsUrl = `ws://localhost:8000/chat-final2/${patientId}`;
                
                try {
                    this.ws = new WebSocket(wsUrl);
                    
                    this.ws.onopen = () => {
                        console.log('WebSocket connection opened');
                        this.updateConnectionStatus('connected');
                        
                        // Send authentication message
                        const authData = {
                            token: token,
                            persona: this.currentPersona
                        };
                        
                        this.ws.send(JSON.stringify(authData));
                        
                        // Start heartbeat
                        this.startHeartbeat();
                    };
                    
                    this.ws.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            this.handleWebSocketMessage(data);
                        } catch (error) {
                            console.error('Error parsing WebSocket message:', error);
                        }
                    };
                    
                    this.ws.onclose = (event) => {
                        console.log('WebSocket connection closed:', event.code, event.reason);
                        this.updateConnectionStatus('disconnected');
                        this.stopHeartbeat();
                        
                        if (event.code !== 1000) { // Not a normal closure
                            this.showError('Connection lost. Please try again.');
                            this.resetToLogin();
                        }
                    };
                    
                    this.ws.onerror = (error) => {
                        console.error('WebSocket error:', error);
                        this.updateConnectionStatus('disconnected');
                        this.showError('Connection failed. Please check your credentials and try again.');
                        this.resetToLogin();
                    };
                    
                } catch (error) {
                    console.error('Failed to create WebSocket:', error);
                    this.showError('Failed to connect. Please try again.');
                    this.resetToLogin();
                }
            }

            startHeartbeat() {
                // Send ping every 30 seconds
                this.heartbeatInterval = setInterval(() => {
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify({ type: 'ping' }));
                    }
                }, 30000);
            }

            stopHeartbeat() {
                if (this.heartbeatInterval) {
                    clearInterval(this.heartbeatInterval);
                    this.heartbeatInterval = null;
                }
            }

            handleWebSocketMessage(data) {
                if (data.type === 'auth_success') {
                    this.handleAuthSuccess(data);
                } else if (data.type === 'pong') {
                    // Heartbeat response - do nothing
                } else if (data.type === 'streaming_text') {
                    this.handleStreamingText(data);
                } else if (data.type === 'streaming_audio' || data.type === 'streaming_audio_final') {
                    this.handleStreamingAudio(data);
                } else if (data.type === 'complete_audio') {
                    this.handleCompleteAudio(data);
                } else if (data.type === 'streaming_complete') {
                    this.handleStreamingComplete(data);
                } else if (data.transcription) {
                    this.handleTranscription(data);
                } else if (data.error) {
                    this.showError(data.error);
                } else {
                    console.log('Unhandled message type:', data);
                }
            }

            handleAuthSuccess(data) {
                console.log('Authentication successful');
                this.currentPersona = data.persona;
                this.updatePersonaUI();
                this.showChatInterface();
            }

            handleStreamingText(data) {
                this.showTypingIndicator();
                // For now, just log streaming text - you could implement real-time text display here
                console.log('Streaming text:', data.text);
            }

            handleStreamingAudio(data) {
                this.hideTypingIndicator();
                
                // Create or update streaming message
                if (!this.currentStreamingMessage) {
                    this.currentStreamingMessage = document.createElement('div');
                    this.currentStreamingMessage.className = 'message assistant streaming';
                    this.chatMessages.appendChild(this.currentStreamingMessage);
                }
                
                // Update text content
                this.currentStreamingMessage.textContent = data.text;
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                
                // Play audio chunk immediately
                if (data.audio) {
                    this.playStreamingAudio(data.audio, data.type === 'streaming_audio_final');
                }
            }

            handleCompleteAudio(data) {
                this.hideTypingIndicator();
                
                if (data.text) {
                    this.addMessage('assistant', data.text);
                }
                
                if (data.audio) {
                    this.playAudio(data.audio);
                }
                
                if (data.error) {
                    this.showError(data.error);
                }
            }

            handleStreamingComplete(data) {
                this.hideTypingIndicator();
                
                // Finalize streaming message
                if (this.currentStreamingMessage) {
                    this.currentStreamingMessage.classList.remove('streaming');
                    this.currentStreamingMessage = null;
                }
                
                console.log('Streaming complete');
            }

            handleTranscription(data) {
                this.transcriptionText.textContent = data.transcription;
                this.transcriptionDisplay.classList.remove('hidden');
                
                // Hide transcription after 3 seconds
                setTimeout(() => {
                    this.transcriptionDisplay.classList.add('hidden');
                }, 3000);
            }

            updatePersonaUI() {
                const doctorInfo = {
                    'psychologist': { name: 'Dr. Ori - Psychologist', indicator: 'Psychologist' },
                    'dietician': { name: 'Dr. Maya - Dietician', indicator: 'Dietician' }
                };
                
                const info = doctorInfo[this.currentPersona];
                this.doctorName.textContent = info.name;
                this.personaIndicator.textContent = info.indicator;
            }

            showChatInterface() {
                this.loginForm.style.display = 'none';
                this.chatContainer.style.display = 'flex';
                this.messageInput.focus();
            }

            resetToLogin() {
                this.chatContainer.style.display = 'none';
                this.loginForm.style.display = 'block';
                this.loginBtn.disabled = false;
                this.loginBtn.textContent = 'Connect to Doctor';
                this.clearMessages();
            }

            showTypingIndicator() {
                this.typingIndicator.style.display = 'block';
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }

            hideTypingIndicator() {
                this.typingIndicator.style.display = 'none';
            }

            addMessage(role, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${role}`;
                messageDiv.textContent = content;
                
                this.chatMessages.appendChild(messageDiv);
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }

            clearMessages() {
                this.chatMessages.innerHTML = '';
            }

            sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message || !this.ws || this.ws.readyState !== WebSocket.OPEN) {
                    return;
                }

                // Add user message to chat
                this.addMessage('user', message);
                
                // Send to server
                this.ws.send(JSON.stringify({ text: message }));
                
                // Clear input and show typing indicator
                this.messageInput.value = '';
                this.showTypingIndicator();
            }

            async toggleVoiceRecording() {
                if (!this.isRecording) {
                    await this.startRecording();
                } else {
                    this.stopRecording();
                }
            }

            async startRecording() {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    this.mediaRecorder = new MediaRecorder(stream);
                    this.audioChunks = [];
                    
                    this.mediaRecorder.ondataavailable = (event) => {
                        this.audioChunks.push(event.data);
                    };
                    
                    this.mediaRecorder.onstop = () => {
                        const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
                        this.sendAudioMessage(audioBlob);
                        stream.getTracks().forEach(track => track.stop());
                    };
                    
                    this.mediaRecorder.start();
                    this.isRecording = true;
                    this.voiceBtn.textContent = '🛑 Stop';
                    this.voiceBtn.classList.add('recording');
                    
                } catch (error) {
                    console.error('Error starting recording:', error);
                    this.showError('Could not access microphone. Please check permissions.');
                }
            }

            stopRecording() {
                if (this.mediaRecorder && this.isRecording) {
                    this.mediaRecorder.stop();
                    this.isRecording = false;
                    this.voiceBtn.textContent = '🎤 Voice';
                    this.voiceBtn.classList.remove('recording');
                }
            }

            async sendAudioMessage(audioBlob) {
                if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
                    return;
                }

                try {
                    // Convert audio blob to base64
                    const reader = new FileReader();
                    reader.onload = () => {
                        const base64Audio = reader.result.split(',')[1]; // Remove data:audio/webm;base64, prefix
                        
                        // Send audio to server
                        this.ws.send(JSON.stringify({ audio: base64Audio }));
                        
                        // Show typing indicator
                        this.showTypingIndicator();
                    };
                    reader.readAsDataURL(audioBlob);
                    
                } catch (error) {
                    console.error('Error sending audio:', error);
                    this.showError('Failed to send audio message.');
                }
            }

            playStreamingAudio(base64Audio, isFinal = false) {
                if (!base64Audio) return;
                
                try {
                    const audioUrl = `data:audio/mp3;base64,${base64Audio}`;
                    const audio = new Audio(audioUrl);
                    
                    // Add to queue
                    this.audioQueue.push(audio);
                    
                    // Play immediately if it's the first audio or previous finished
                    if (this.audioQueue.length === 1) {
                        this.playNextAudio();
                    }
                    
                } catch (error) {
                    console.error('Error setting up streaming audio playback:', error);
                }
            }

            playNextAudio() {
                if (this.audioQueue.length === 0) return;
                
                const audio = this.audioQueue[0];
                
                audio.onended = () => {
                    this.audioQueue.shift(); // Remove played audio
                    this.playNextAudio(); // Play next audio in queue
                };
                
                audio.onerror = (e) => {
                    console.error('Streaming audio playback error:', e);
                    this.audioQueue.shift(); // Remove failed audio
                    this.playNextAudio(); // Try next audio
                };
                
                audio.play().catch(e => {
                    console.error('Error playing streaming audio:', e);
                    this.audioQueue.shift();
                    this.playNextAudio();
                });
            }

            logout() {
                if (this.ws) {
                    this.ws.close();
                }
                this.stopHeartbeat();
                this.resetToLogin();
                this.updateConnectionStatus('disconnected');
            }
        }

        // Initialize the app when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new ChatApp();
        });
    </script>
</body>
</html>