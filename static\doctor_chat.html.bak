<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Doctor <PERSON>t</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        #chat-container {
            border: 1px solid #ccc;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            padding: 10px;
            margin-bottom: 10px;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 5px;
            max-width: 70%;
        }
        .doctor {
            background-color: #e1f5fe;
            align-self: flex-end;
            margin-left: auto;
        }
        .assistant {
            background-color: #f1f1f1;
        }
        #input-container {
            display: flex;
        }
        #message-input {
            flex-grow: 1;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        button {
            padding: 10px 15px;
            margin-left: 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #doctor-id-container {
            margin-bottom: 20px;
        }
        #doctor-id {
            padding: 10px;
            width: 300px;
        }
        #connect-btn {
            background-color: #2196F3;
        }
        #connect-btn:hover {
            background-color: #0b7dda;
        }
        #status {
            margin-top: 10px;
            font-style: italic;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>Doctor Smart Chat</h1>

    <div id="doctor-id-container">
        <label for="doctor-id">Doctor ID:</label>
        <input type="text" id="doctor-id" placeholder="Enter doctor ID">
        <label for="jwt-token">JWT Token:</label>
        <input type="text" id="jwt-token" placeholder="Enter JWT token">
        <button id="static-token-btn">Use Static Token</button>
        <button id="connect-btn">Connect</button>
    </div>

    <div id="status">Not connected</div>

    <div id="chat-container"></div>

    <div id="input-container">
        <input type="text" id="message-input" placeholder="Type your message..." disabled>
        <button id="send-btn" disabled>Send</button>
        <button id="mic-btn" disabled>🎤</button>
    </div>

    <script>
        let socket;
        let isRecording = false;
        let mediaRecorder;
        let audioChunks = [];

        const doctorIdInput = document.getElementById('doctor-id');
        const jwtTokenInput = document.getElementById('jwt-token');
        const staticTokenBtn = document.getElementById('static-token-btn');
        const connectBtn = document.getElementById('connect-btn');
        const statusDiv = document.getElementById('status');
        const chatContainer = document.getElementById('chat-container');
        const messageInput = document.getElementById('message-input');
        const sendBtn = document.getElementById('send-btn');
        const micBtn = document.getElementById('mic-btn');

        // Static token for testing
        const STATIC_JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.3oZ2Ubh5rLBdHvQHd5Qr9GJczA5MXcxaVx5H5xLwvZ4";

        // Use static token
        staticTokenBtn.addEventListener('click', () => {
            jwtTokenInput.value = STATIC_JWT_TOKEN;
            setStatus('Static token loaded', '');
        });

        // Connect to WebSocket
        connectBtn.addEventListener('click', () => {
            const doctorId = doctorIdInput.value.trim();
            const jwtToken = jwtTokenInput.value.trim();

            if (!doctorId) {
                setStatus('Please enter a doctor ID', 'error');
                return;
            }

            // Close existing connection if any
            if (socket) {
                socket.close();
            }

            // Create WebSocket URL with token as query parameter
            const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${wsProtocol}//${window.location.host}/smart-chat-doc/${doctorId}?token=${encodeURIComponent(jwtToken)}`;

            try {
                // Connect to WebSocket
                socket = new WebSocket(wsUrl);

                setStatus('Connecting...', '');

                // WebSocket event handlers
                socket.onopen = () => {
                    setStatus('Connected', 'success');
                    enableChat(true);
                };

                socket.onmessage = (event) => {
                    const data = JSON.parse(event.data);

                    if (data.transcription) {
                        // This is a transcription response
                        console.log('Received transcription:', data.transcription);

                        // Find the placeholder message and replace it with the transcription
                        const messages = document.querySelectorAll('.message.doctor');
                        const placeholderMessage = Array.from(messages).find(
                            msg => msg.textContent.includes('🎤 Processing audio...')
                        );

                        if (placeholderMessage) {
                            // Replace the placeholder text with the transcription
                            placeholderMessage.textContent = data.transcription;
                        } else {
                            // If we can't find the placeholder, add a new message
                            addMessage('doctor', data.transcription);
                        }

                        setStatus('Audio processed', 'success');
                    } else if (data.response) {
                        // This is a normal response
                        addMessage('assistant', data.response);
                    } else if (data.error) {
                        // This is an error
                        setStatus(`Error: ${data.error}`, 'error');
                        addMessage('assistant', `Error: ${data.error}`);
                    }
                };

                socket.onclose = () => {
                    setStatus('Disconnected', 'error');
                    enableChat(false);
                };

                socket.onerror = (error) => {
                    setStatus(`Error: ${error.message}`, 'error');
                    enableChat(false);
                };
            } catch (error) {
                setStatus(`Connection error: ${error.message}`, 'error');
            }
        });

        // Send message
        sendBtn.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || !socket || socket.readyState !== WebSocket.OPEN) {
                return;
            }

            // Add message to chat
            addMessage('doctor', message);

            // Send message to server
            socket.send(JSON.stringify({ text: message }));

            // Clear input
            messageInput.value = '';
        }

        // Add message to chat
        function addMessage(role, text) {
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message', role);
            messageDiv.textContent = text;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Set status message
        function setStatus(message, type) {
            statusDiv.textContent = message;
            statusDiv.className = type;
        }

        // Enable/disable chat inputs
        function enableChat(enabled) {
            messageInput.disabled = !enabled;
            sendBtn.disabled = !enabled;
            micBtn.disabled = !enabled;

            if (enabled) {
                messageInput.focus();
            }
        }

        // Voice recording
        micBtn.addEventListener('click', toggleRecording);

        function toggleRecording() {
            if (isRecording) {
                stopRecording();
            } else {
                startRecording();
            }
        }

        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];

                mediaRecorder.ondataavailable = (event) => {
                    audioChunks.push(event.data);
                };

                mediaRecorder.onstop = sendAudio;

                mediaRecorder.start();
                isRecording = true;
                micBtn.textContent = '⏹️';
                setStatus('Recording...', '');
            } catch (error) {
                setStatus(`Microphone error: ${error.message}`, 'error');
            }
        }

        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                micBtn.textContent = '🎤';
                setStatus('Processing audio...', '');
            }
        }

        function sendAudio() {
            const audioBlob = new Blob(audioChunks, { type: 'audio/mp3' });
            const reader = new FileReader();

            // Add a placeholder message for the audio being processed
            addMessage('doctor', '🎤 Processing audio...');

            reader.onload = () => {
                const base64Audio = reader.result.split(',')[1];

                if (socket && socket.readyState === WebSocket.OPEN) {
                    socket.send(JSON.stringify({ audio: base64Audio }));
                    setStatus('Sending audio...', 'success');
                } else {
                    setStatus('WebSocket not connected', 'error');
                }
            };

            reader.readAsDataURL(audioBlob);
        }
    </script>
</body>
</html>
