<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Specialized PDF Upload and Processing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        textarea,
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .file-input {
            margin: 15px 0;
        }
        .btn {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        #status, #serviceStatus {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .hidden {
            display: none;
        }
        details {
            margin: 10px 0;
            padding: 5px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        summary {
            cursor: pointer;
            font-weight: bold;
            padding: 5px;
        }
        details ul {
            max-height: 200px;
            overflow-y: auto;
            margin: 5px 0;
            padding-left: 25px;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        .loading:after {
            content: "...";
            animation: dots 1.5s steps(5, end) infinite;
        }
        @keyframes dots {
            0%, 20% { content: "."; }
            40% { content: ".."; }
            60%, 100% { content: "..."; }
        }
        .index-type {
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f5f5f5;
        }
        .index-type-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .index-type-header h3 {
            margin: 0;
        }
        .index-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        .general-badge {
            background-color: #007bff;
        }
        .physician-badge {
            background-color: #6f42c1; /* Changed to purple for psychologist */
        }
        .dietician-badge {
            background-color: #dc3545;
        }
        .advanced-options {
            margin-top: 15px;
            padding: 10px;
            border: 1px dashed #ccc;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .advanced-options h4 {
            margin-top: 0;
        }
        .radio-group {
            display: flex;
            gap: 15px;
        }
        .radio-option {
            display: flex;
            align-items: center;
        }
        .radio-option input {
            margin-right: 5px;
        }

        /* Progress tracking styles */
        .progress-container {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
            display: none;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .progress-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .progress-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-uploading {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .status-processing {
            background-color: #fff3e0;
            color: #f57c00;
        }

        .status-completed {
            background-color: #e8f5e8;
            color: #2e7d32;
        }

        .status-failed {
            background-color: #ffebee;
            color: #c62828;
        }

        .progress-bar-container {
            width: 100%;
            height: 8px;
            background-color: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #81c784);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-bar.processing {
            background: linear-gradient(90deg, #ff9800, #ffb74d);
            animation: pulse 2s infinite;
        }

        .progress-bar.completed {
            background: linear-gradient(90deg, #4caf50, #66bb6a);
        }

        .progress-bar.failed {
            background: linear-gradient(90deg, #f44336, #e57373);
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            position: relative;
        }

        .progress-step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 15px;
            right: -50%;
            width: 100%;
            height: 2px;
            background-color: #e0e0e0;
            z-index: 1;
        }

        .progress-step.completed:not(:last-child)::after {
            background-color: #4caf50;
        }

        .step-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            color: #666;
            z-index: 2;
            position: relative;
        }

        .step-icon.completed {
            background-color: #4caf50;
            color: white;
        }

        .step-icon.active {
            background-color: #ff9800;
            color: white;
            animation: pulse 2s infinite;
        }

        .step-icon.failed {
            background-color: #f44336;
            color: white;
        }

        .step-label {
            margin-top: 8px;
            font-size: 12px;
            text-align: center;
            color: #666;
        }

        .step-label.completed {
            color: #4caf50;
            font-weight: bold;
        }

        .step-label.active {
            color: #ff9800;
            font-weight: bold;
        }

        .step-label.failed {
            color: #f44336;
            font-weight: bold;
        }

        .progress-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .progress-detail {
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        }

        .progress-detail-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .progress-detail-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .progress-log {
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-timestamp {
            color: #666;
            margin-right: 10px;
        }

        .log-message {
            color: #333;
        }

        .log-error {
            color: #d32f2f;
        }

        .log-success {
            color: #388e3c;
        }

        .log-info {
            color: #1976d2;
        }
    </style>
</head>
<body>
    <h1>Specialized PDF Upload and Processing</h1>

    <div class="container" id="serviceStatusContainer">
        <h2>Service Status</h2>
        <button class="btn" id="checkStatusBtn">Check Service Status</button>
        <div id="serviceStatus" class="hidden"></div>
    </div>

    <div class="container" id="uploadContainer">
        <h2>Upload PDF for Specialized Index</h2>
        <p>Upload PDFs to create specialized HNSW indexes for different medical specialties. These indexes will be used by the AI chat system to provide relevant information to patients.</p>

        <form id="uploadForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="title">Title (optional):</label>
                <input type="text" id="title" name="title" placeholder="Enter a title for the PDF">
            </div>

            <div class="form-group">
                <label for="description">Description (optional):</label>
                <textarea id="description" name="description" placeholder="Enter a description for the PDF"></textarea>
            </div>

            <div class="form-group">
                <label for="indexType">Select Index Type:</label>
                <select id="indexType" name="index_type" required>
                    <option value="">-- Select Index Type --</option>
                    <option value="general_index">General OPD</option>
                    <option value="psychologist_index">Psychologist</option>
                    <option value="dietician_index">Dietician</option>
                </select>
            </div>

            <div class="advanced-options">
                <h4>Advanced Chunking Options</h4>
                <div class="form-group">
                    <label for="chunkSize">Chunk Size (tokens):</label>
                    <input type="number" id="chunkSize" name="chunk_size" value="400" min="100" max="1000">
                </div>
                <div class="form-group">
                    <label for="chunkOverlap">Chunk Overlap (tokens):</label>
                    <input type="number" id="chunkOverlap" name="chunk_overlap" value="50" min="0" max="200">
                </div>
                <div class="form-group">
                    <label>Storage Location:</label>
                    <div class="radio-group">
                        <div class="radio-option">
                            <input type="radio" id="storageS3" name="storage_location" value="s3" checked>
                            <label for="storageS3">S3 Bucket</label>
                        </div>
                        <div class="radio-option">
                            <input type="radio" id="storageLocal" name="storage_location" value="local">
                            <label for="storageLocal">Local Storage</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group file-input">
                <label for="pdfFile">Select PDF file:</label>
                <input type="file" id="pdfFile" name="file" accept=".pdf" required>
            </div>

            <button type="submit" class="btn" id="uploadBtn">Upload and Process</button>
        </form>

        <div id="status" class="hidden"></div>

        <!-- Real-time Progress Tracking -->
        <div id="progressContainer" class="progress-container">
            <div class="progress-header">
                <div class="progress-title">Processing PDF</div>
                <div id="progressStatus" class="progress-status status-uploading">Uploading</div>
            </div>

            <div class="progress-bar-container">
                <div id="progressBar" class="progress-bar"></div>
            </div>

            <div class="progress-steps">
                <div class="progress-step" id="step-upload">
                    <div class="step-icon">1</div>
                    <div class="step-label">Upload</div>
                </div>
                <div class="progress-step" id="step-extract">
                    <div class="step-icon">2</div>
                    <div class="step-label">Extract Text</div>
                </div>
                <div class="progress-step" id="step-process">
                    <div class="step-icon">3</div>
                    <div class="step-label">Create Vectors</div>
                </div>
                <div class="progress-step" id="step-save">
                    <div class="step-icon">4</div>
                    <div class="step-label">Save to S3</div>
                </div>
                <div class="progress-step" id="step-complete">
                    <div class="step-icon">✓</div>
                    <div class="step-label">Complete</div>
                </div>
            </div>

            <div class="progress-details">
                <div class="progress-detail">
                    <div class="progress-detail-label">PDF ID</div>
                    <div id="progressPdfId" class="progress-detail-value">-</div>
                </div>
                <div class="progress-detail">
                    <div class="progress-detail-label">Index Type</div>
                    <div id="progressIndexType" class="progress-detail-value">-</div>
                </div>
                <div class="progress-detail">
                    <div class="progress-detail-label">Pages</div>
                    <div id="progressPages" class="progress-detail-value">-</div>
                </div>
                <div class="progress-detail">
                    <div class="progress-detail-label">Processing Time</div>
                    <div id="progressTime" class="progress-detail-value">0s</div>
                </div>
            </div>

            <div id="progressLog" class="progress-log"></div>
        </div>

        <div id="processingDetails" class="hidden">
            <h3>Processing Details</h3>
            <p><strong>PDF ID:</strong> <span id="pdfId"></span></p>
            <p><strong>Index Type:</strong> <span id="indexTypeDisplay"></span></p>
            <p><strong>Number of Pages:</strong> <span id="numPages"></span></p>
            <p><strong>Processing Time:</strong> <span id="processingTime"></span> seconds</p>
            <p id="s3UrlContainer" style="display: none;"><strong>S3 URL:</strong> <a id="s3Url" href="#" target="_blank"></a></p>
            <div>
                <h4>Text Sample:</h4>
                <pre id="textSample" style="white-space: pre-wrap; background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;"></pre>
            </div>
        </div>
    </div>

    <div class="container" id="indexStatusContainer">
        <h2>Index Status</h2>
        <div class="index-type">
            <div class="index-type-header">
                <h3>General OPD Index</h3>
                <span class="index-badge general-badge">General</span>
            </div>
            <p>Status: <span id="generalIndexStatus">Checking...</span></p>
            <p>Documents: <span id="generalIndexDocs">-</span></p>
        </div>

        <div class="index-type">
            <div class="index-type-header">
                <h3>Psychologist Index</h3>
                <span class="index-badge physician-badge">Psychologist</span>
            </div>
            <p>Status: <span id="psychologistIndexStatus">Checking...</span></p>
            <p>Documents: <span id="psychologistIndexDocs">-</span></p>
        </div>

        <div class="index-type">
            <div class="index-type-header">
                <h3>Dietician Index</h3>
                <span class="index-badge dietician-badge">Dietician</span>
            </div>
            <p>Status: <span id="dieticianIndexStatus">Checking...</span></p>
            <p>Documents: <span id="dieticianIndexDocs">-</span></p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const uploadForm = document.getElementById('uploadForm');
            const statusDiv = document.getElementById('status');
            const processingDetails = document.getElementById('processingDetails');
            const checkStatusBtn = document.getElementById('checkStatusBtn');
            const serviceStatus = document.getElementById('serviceStatus');
            const indexTypeSelect = document.getElementById('indexType');

            // Progress tracking elements
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const progressStatus = document.getElementById('progressStatus');
            const progressLog = document.getElementById('progressLog');

            // Progress tracking variables
            let currentPdfId = null;
            let progressStartTime = null;
            let progressInterval = null;

            // Initialize index status
            checkIndexStatus();

            // Progress tracking functions
            function initializeProgress(pdfId, indexType) {
                currentPdfId = pdfId;
                progressStartTime = Date.now();

                // Show progress container
                progressContainer.style.display = 'block';

                // Initialize progress details
                document.getElementById('progressPdfId').textContent = pdfId;
                document.getElementById('progressIndexType').textContent = getIndexTypeName(indexType);
                document.getElementById('progressPages').textContent = '-';
                document.getElementById('progressTime').textContent = '0s';

                // Reset progress bar and steps
                updateProgressBar(0, 'uploading');
                resetProgressSteps();
                updateProgressStep('upload', 'active');

                // Clear log
                progressLog.innerHTML = '';
                addLogEntry('Upload started', 'info');

                // Start progress timer
                if (progressInterval) clearInterval(progressInterval);
                progressInterval = setInterval(updateProgressTimer, 1000);
            }

            function updateProgressBar(percentage, status) {
                progressBar.style.width = percentage + '%';
                progressBar.className = 'progress-bar ' + status;

                // Update status badge
                progressStatus.textContent = status.charAt(0).toUpperCase() + status.slice(1);
                progressStatus.className = 'progress-status status-' + status;
            }

            function resetProgressSteps() {
                const steps = ['upload', 'extract', 'process', 'save', 'complete'];
                steps.forEach(step => {
                    const stepElement = document.getElementById('step-' + step);
                    const icon = stepElement.querySelector('.step-icon');
                    const label = stepElement.querySelector('.step-label');

                    stepElement.className = 'progress-step';
                    icon.className = 'step-icon';
                    label.className = 'step-label';
                });
            }

            function updateProgressStep(stepName, status) {
                const stepElement = document.getElementById('step-' + stepName);
                const icon = stepElement.querySelector('.step-icon');
                const label = stepElement.querySelector('.step-label');

                stepElement.className = 'progress-step ' + status;
                icon.className = 'step-icon ' + status;
                label.className = 'step-label ' + status;

                // Update progress bar based on step
                const stepProgress = {
                    'upload': 20,
                    'extract': 40,
                    'process': 70,
                    'save': 90,
                    'complete': 100
                };

                if (status === 'completed' || status === 'active') {
                    updateProgressBar(stepProgress[stepName], status === 'completed' ? 'completed' : 'processing');
                }
            }

            function updateProgressTimer() {
                if (progressStartTime) {
                    const elapsed = Math.floor((Date.now() - progressStartTime) / 1000);
                    document.getElementById('progressTime').textContent = elapsed + 's';
                }
            }

            function addLogEntry(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                logEntry.innerHTML = `
                    <span class="log-timestamp">[${timestamp}]</span>
                    <span class="log-message log-${type}">${message}</span>
                `;
                progressLog.appendChild(logEntry);
                progressLog.scrollTop = progressLog.scrollHeight;
            }

            function completeProgress(success, message) {
                if (progressInterval) {
                    clearInterval(progressInterval);
                    progressInterval = null;
                }

                if (success) {
                    updateProgressStep('complete', 'completed');
                    updateProgressBar(100, 'completed');
                    addLogEntry(message || 'Processing completed successfully', 'success');
                } else {
                    updateProgressBar(100, 'failed');
                    addLogEntry(message || 'Processing failed', 'error');

                    // Mark current step as failed
                    const activeStep = document.querySelector('.progress-step .step-icon.active');
                    if (activeStep) {
                        activeStep.className = 'step-icon failed';
                        activeStep.parentElement.querySelector('.step-label').className = 'step-label failed';
                    }
                }
            }

            // Service status check
            checkStatusBtn.addEventListener('click', async function() {
                checkStatusBtn.disabled = true;
                serviceStatus.innerHTML = '<div class="loading">Checking service status</div>';
                serviceStatus.className = 'info';
                serviceStatus.classList.remove('hidden');

                try {
                    const response = await fetch('/pdf/status');
                    const result = await response.json();

                    if (response.ok) {
                        // Format the status information
                        let statusHtml = '<h3>Service Status</h3>';

                        // Vector store status
                        statusHtml += `<p><strong>Vector Store:</strong> ${result.vector_store_status}</p>`;

                        // OpenAI API key status
                        statusHtml += `<p><strong>OpenAI API Key:</strong> ${result.openai_api_key_status}</p>`;

                        // S3 status
                        statusHtml += '<div><strong>S3 Status:</strong> ';
                        if (result.s3_status.enabled) {
                            statusHtml += `<span style="color: green;">Enabled</span> (Bucket: ${result.s3_status.bucket}, Region: ${result.s3_status.region})</p>`;

                            // S3 files
                            if (result.s3_status.file_count > 0) {
                                statusHtml += '<details>';
                                statusHtml += `<summary>S3 Files (${result.s3_status.file_count})</summary>`;
                                statusHtml += '<ul>';
                                result.s3_status.files.forEach(file => {
                                    statusHtml += `<li>${file}</li>`;
                                });
                                statusHtml += '</ul>';
                                statusHtml += '</details>';
                            } else {
                                statusHtml += '<p>No files in S3 bucket.</p>';
                            }
                        } else {
                            statusHtml += '<span style="color: orange;">Disabled</span></p>';
                        }
                        statusHtml += '</div>';

                        // Local storage
                        statusHtml += '<div><strong>Local Storage:</strong>';

                        // FAISS directory
                        statusHtml += `<p>FAISS Index: ${result.faiss_directory.exists ? 'Exists' : 'Does not exist'} (${result.faiss_directory.file_count} files)</p>`;
                        if (result.faiss_directory.file_count > 0) {
                            statusHtml += '<details>';
                            statusHtml += `<summary>FAISS Files (${result.faiss_directory.file_count})</summary>`;
                            statusHtml += '<ul>';
                            result.faiss_directory.files.forEach(file => {
                                statusHtml += `<li>${file}</li>`;
                            });
                            statusHtml += '</ul>';
                            statusHtml += '</details>';
                        }

                        // Temp directory
                        statusHtml += `<p>Temp Directory: ${result.temp_directory.exists ? 'Exists' : 'Does not exist'} (${result.temp_directory.file_count} files)</p>`;
                        if (result.temp_directory.file_count > 0) {
                            statusHtml += '<details>';
                            statusHtml += `<summary>Temp Files (${result.temp_directory.file_count})</summary>`;
                            statusHtml += '<ul>';
                            result.temp_directory.files.forEach(file => {
                                statusHtml += `<li>${file}</li>`;
                            });
                            statusHtml += '</ul>';
                            statusHtml += '</details>';
                        }
                        statusHtml += '</div>';

                        // Timestamp
                        statusHtml += `<p><em>Last updated: ${new Date(result.timestamp).toLocaleString()}</em></p>`;

                        serviceStatus.innerHTML = statusHtml;
                        serviceStatus.className = 'success';
                    } else {
                        serviceStatus.textContent = `Error: ${result.detail || 'Unknown error'}`;
                        serviceStatus.className = 'error';
                    }
                } catch (error) {
                    serviceStatus.textContent = `Error: ${error.message}`;
                    serviceStatus.className = 'error';
                } finally {
                    checkStatusBtn.disabled = false;
                }
            });

            // Upload form submission
            uploadForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = new FormData(uploadForm);
                const uploadBtn = document.getElementById('uploadBtn');
                const indexType = indexTypeSelect.value;

                // Validate file
                const fileInput = document.getElementById('pdfFile');
                if (!fileInput.files[0]) {
                    showStatus('Please select a PDF file', 'error');
                    return;
                }

                if (!fileInput.files[0].name.toLowerCase().endsWith('.pdf')) {
                    showStatus('File must be a PDF', 'error');
                    return;
                }

                // Validate index type
                if (!indexType) {
                    showStatus('Please select an index type', 'error');
                    return;
                }

                // Disable button and show loading
                uploadBtn.disabled = true;
                showStatus(`Uploading and processing PDF for ${getIndexTypeName(indexType)}...`, 'info');

                try {
                    // Add the specialized parameters to the form data
                    const chunkSize = document.getElementById('chunkSize').value;
                    const chunkOverlap = document.getElementById('chunkOverlap').value;
                    const storageLocation = document.querySelector('input[name="storage_location"]:checked').value;

                    formData.append('chunk_size', chunkSize);
                    formData.append('chunk_overlap', chunkOverlap);
                    formData.append('storage_location', storageLocation);

                    const response = await fetch('/pdf/upload/specialized', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (response.ok) {
                        showStatus(`PDF uploaded successfully and processing started for ${getIndexTypeName(indexType)} index`, 'success');

                        // Initialize progress tracking
                        initializeProgress(result.pdf_id, indexType);
                        addLogEntry('Upload completed, starting processing...', 'success');
                        updateProgressStep('upload', 'completed');
                        updateProgressStep('extract', 'active');

                        // Show processing details
                        document.getElementById('pdfId').textContent = result.pdf_id;
                        document.getElementById('indexTypeDisplay').textContent = getIndexTypeName(indexType);
                        document.getElementById('numPages').textContent = result.num_pages || 'Processing...';
                        document.getElementById('processingTime').textContent = result.processing_time || 'Processing...';
                        document.getElementById('textSample').textContent = result.extracted_text_sample || 'Processing...';

                        // Show S3 URL if available
                        const s3UrlContainer = document.getElementById('s3UrlContainer');
                        const s3UrlLink = document.getElementById('s3Url');
                        if (result.s3_url) {
                            s3UrlLink.href = result.s3_url;
                            s3UrlLink.textContent = result.s3_url;
                            s3UrlContainer.style.display = 'block';
                        } else {
                            s3UrlContainer.style.display = 'none';
                        }

                        processingDetails.classList.remove('hidden');

                        // Reset form
                        uploadForm.reset();

                        // Start polling for processing status with enhanced tracking
                        const pdfId = result.pdf_id;
                        pollProcessingStatusWithProgress(pdfId);
                    } else {
                        const errorDetail = result.detail || 'Unknown error';
                        console.error('Error from server:', errorDetail);
                        showStatus(`Error: ${errorDetail}`, 'error');

                        // Check for specific error messages
                        if (errorDetail.includes('Access denied')) {
                            showStatus(`Error: S3 access denied. Please check AWS credentials and permissions.`, 'error');
                        } else if (errorDetail.includes('bucket')) {
                            showStatus(`Error: S3 bucket issue. Please check bucket configuration.`, 'error');
                        }
                    }
                } catch (error) {
                    console.error('Client-side error:', error);

                    // Check if it's a network error
                    if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
                        showStatus(`Network error: Please check your connection and make sure the server is running.`, 'error');
                    } else {
                        showStatus(`Error: ${error.message}`, 'error');
                    }
                } finally {
                    uploadBtn.disabled = false;
                }
            });

            // Enhanced polling with progress tracking
            async function pollProcessingStatusWithProgress(pdfId) {
                try {
                    console.log(`Polling processing status for PDF ID: ${pdfId}`);

                    // Use the quick status endpoint for faster polling
                    const response = await fetch(`/pdf/processing/${pdfId}/quick`);

                    if (response.ok) {
                        const result = await response.json();
                        console.log("Processing status response:", result);

                        // Update progress details
                        if (result.num_pages > 0) {
                            document.getElementById('progressPages').textContent = result.num_pages;
                            document.getElementById('numPages').textContent = result.num_pages;
                        }

                        // Update processing time
                        if (result.processing_time > 0) {
                            document.getElementById('processingTime').textContent = result.processing_time.toFixed(2);
                        }

                        // Handle different statuses with progress updates
                        if (result.status === 'processing') {
                            // Simulate progress through steps based on processing time
                            const elapsed = result.processing_time;

                            if (elapsed > 5 && !document.getElementById('step-extract').classList.contains('completed')) {
                                updateProgressStep('extract', 'completed');
                                updateProgressStep('process', 'active');
                                addLogEntry('Text extraction completed, creating vectors...', 'info');
                            }

                            if (elapsed > 30 && !document.getElementById('step-process').classList.contains('completed')) {
                                updateProgressStep('process', 'completed');
                                updateProgressStep('save', 'active');
                                addLogEntry('Vector creation completed, saving to S3...', 'info');
                            }

                            // Show progress message for long processing
                            if (elapsed > 120 && elapsed % 60 === 0) { // Every minute after 2 minutes
                                addLogEntry(`Processing continues... (${Math.floor(elapsed/60)} minutes elapsed)`, 'info');
                            }

                            // Continue polling
                            setTimeout(() => pollProcessingStatusWithProgress(pdfId), 2000);

                        } else if (result.status === 'completed') {
                            // Complete all steps
                            updateProgressStep('extract', 'completed');
                            updateProgressStep('process', 'completed');
                            updateProgressStep('save', 'completed');

                            completeProgress(true, `PDF processing completed successfully in ${result.processing_time.toFixed(2)} seconds`);
                            showStatus(`PDF processing completed successfully in ${result.processing_time.toFixed(2)} seconds`, 'success');

                            // Update index status
                            checkIndexStatus();

                        } else if (result.status === 'failed') {
                            const errorMsg = result.error || 'Unknown error';
                            completeProgress(false, `Processing failed: ${errorMsg}`);
                            console.error("Processing failed:", errorMsg);
                            showStatus(`Error processing PDF: ${errorMsg}`, 'error');
                        }
                    } else {
                        // Fallback to full status endpoint
                        await pollProcessingStatusFallback(pdfId);
                    }
                } catch (error) {
                    console.error('Error polling processing status:', error);
                    addLogEntry(`Network error: ${error.message}`, 'error');

                    // Try again after a longer delay
                    setTimeout(() => pollProcessingStatusWithProgress(pdfId), 3000);
                }
            }

            // Fallback polling function (original)
            async function pollProcessingStatusFallback(pdfId) {
                try {
                    console.log(`Fallback polling for PDF ID: ${pdfId}`);
                    const response = await fetch(`/pdf/processing/${pdfId}`);

                    if (response.ok) {
                        const contentType = response.headers.get("content-type");
                        if (!contentType || !contentType.includes("application/json")) {
                            console.error("Non-JSON response received:", await response.text());
                            addLogEntry('Unexpected response format from server', 'error');
                            return;
                        }

                        const result = await response.json();
                        console.log("Fallback processing status response:", result);

                        // Update processing details
                        document.getElementById('numPages').textContent = result.num_pages || 'Processing...';
                        document.getElementById('processingTime').textContent = result.processing_time?.toFixed(2) || 'Processing...';
                        document.getElementById('textSample').textContent = result.extracted_text_sample || 'Processing...';

                        // Update progress details
                        if (result.num_pages > 0) {
                            document.getElementById('progressPages').textContent = result.num_pages;
                        }

                        // If still processing, poll again
                        if (result.status === 'processing') {
                            setTimeout(() => pollProcessingStatusFallback(pdfId), 2000);
                        } else if (result.status === 'completed') {
                            completeProgress(true, `PDF processing completed successfully in ${result.processing_time?.toFixed(2) || 0} seconds`);
                            showStatus(`PDF processing completed successfully in ${result.processing_time?.toFixed(2) || 0} seconds`, 'success');
                            checkIndexStatus();
                        } else if (result.status === 'failed') {
                            const errorMsg = result.error || 'Unknown error';
                            completeProgress(false, `Processing failed: ${errorMsg}`);
                            console.error("Processing failed:", errorMsg);
                            showStatus(`Error processing PDF: ${errorMsg}`, 'error');
                        }
                    } else {
                        addLogEntry(`Failed to get processing status (${response.status})`, 'error');
                        setTimeout(() => pollProcessingStatusFallback(pdfId), 5000);
                    }
                } catch (error) {
                    console.error('Error in fallback polling:', error);
                    addLogEntry(`Network error: ${error.message}`, 'error');
                    setTimeout(() => pollProcessingStatusFallback(pdfId), 5000);
                }
            }

            // Keep original function for backward compatibility
            async function pollProcessingStatus(pdfId) {
                return pollProcessingStatusWithProgress(pdfId);
            }

            // Check status of all indexes
            async function checkIndexStatus() {
                try {
                    console.log("Checking index status...");
                    const response = await fetch('/pdf/index/status');

                    if (response.ok) {
                        // Check if the response is JSON
                        const contentType = response.headers.get("content-type");
                        if (!contentType || !contentType.includes("application/json")) {
                            console.error("Non-JSON response received:", await response.text());
                            showStatus(`Error: Unexpected response format from server when checking index status`, 'error');
                            return;
                        }

                        const result = await response.json();
                        console.log("Index status response:", result);

                        // Update index status for each type
                        updateIndexStatus('general', result.general_index);
                        updateIndexStatus('psychologist', result.psychologist_index); // Changed from psychiatric
                        updateIndexStatus('dietician', result.dietician_index);

                        // Show success message
                        showStatus("Index status updated successfully", "success");
                    } else {
                        // If we get an error, try to parse the response
                        try {
                            const errorText = await response.text();
                            console.error('Error checking index status:', errorText);

                            // Try to parse as JSON
                            try {
                                const errorJson = JSON.parse(errorText);
                                showStatus(`Error checking index status: ${errorJson.detail || 'Unknown error'}`, 'error');
                            } catch {
                                // Not JSON, just show the text
                                showStatus(`Error checking index status: ${errorText}`, 'error');
                            }
                        } catch {
                            showStatus(`Error: Failed to check index status (${response.status})`, 'error');
                        }
                    }
                } catch (error) {
                    console.error('Error checking index status:', error);
                    showStatus(`Network error when checking index status: ${error.message}`, 'error');
                }
            }

            // Update the UI for a specific index
            function updateIndexStatus(indexType, status) {
                const statusElement = document.getElementById(`${indexType}IndexStatus`);
                const docsElement = document.getElementById(`${indexType}IndexDocs`);

                if (statusElement && docsElement) {
                    if (status.exists) {
                        statusElement.textContent = 'Available';
                        statusElement.style.color = 'green';
                        docsElement.textContent = status.document_count || 'Unknown';
                    } else {
                        statusElement.textContent = 'Not Available';
                        statusElement.style.color = 'red';
                        docsElement.textContent = '0';
                    }
                }
            }

            // Helper functions
            function showStatus(message, type) {
                statusDiv.textContent = message;
                statusDiv.className = type;
                statusDiv.classList.remove('hidden');
            }

            function getIndexTypeName(indexType) {
                const indexNames = {
                    'general_index': 'General OPD',
                    'psychologist_index': 'Psychologist', // Changed from physician
                    'dietician_index': 'Dietician'
                };
                return indexNames[indexType] || indexType;
            }
        });
    </script>
</body>
</html>
