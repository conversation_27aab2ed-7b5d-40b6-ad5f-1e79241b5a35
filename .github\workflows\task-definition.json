{"family": "prasha-health-task", "networkMode": "awsvpc", "executionRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskRole", "containerDefinitions": [{"name": "prasha-health-container", "image": "ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com/prasha-health-ai:latest", "essential": true, "portMappings": [{"containerPort": 8000, "hostPort": 8000, "protocol": "tcp"}], "environment": [{"name": "ENVIRONMENT", "value": "production"}], "secrets": [{"name": "OPENAI_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:PrashaHealth/OpenAI/ApiKey-SUFFIX"}, {"name": "MAIN_BACKEND_TOKEN", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:PrashaHealth/JWT/MainBackendToken-SUFFIX"}, {"name": "ADMIN_SERVICE_TOKEN", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:PrashaHealth/JWT/AdminServiceToken-SUFFIX"}, {"name": "MOBILE_APP_TOKEN", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:PrashaHealth/JWT/MobileAppToken-SUFFIX"}, {"name": "TEST_TOKEN", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:PrashaHealth/JWT/TestToken-SUFFIX"}, {"name": "POSTGRES_USERNAME", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:PrashaHealth/Database/Username-SUFFIX"}, {"name": "POSTGRES_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:PrashaHealth/Database/Password-SUFFIX"}, {"name": "POSTGRES_HOST", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:PrashaHealth/Database/Host-SUFFIX"}, {"name": "POSTGRES_PORT", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:PrashaHealth/Database/Port-SUFFIX"}, {"name": "POSTGRES_DATABASE", "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT_ID:secret:PrashaHealth/Database/Name-SUFFIX"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/prasha-health-task", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:8000/ || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}}], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "tags": [{"key": "Project", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "Environment", "value": "Production"}]}