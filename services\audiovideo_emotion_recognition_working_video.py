import os
import base64
import uuid
import torch
import librosa
import requests
import numpy as np
from datetime import datetime, timezone
from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel
from typing import List, Dict, Any
from dotenv import load_dotenv
from collections import defaultdict
from transformers import AutoFeatureExtractor, AutoModelForAudioClassification, pipeline
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
import logging
import cv2
import tensorflow as tf
from deepface import DeepFace
import tempfile
import shutil
import warnings

# Disable warnings
warnings.filterwarnings("ignore", category=FutureWarning, module="librosa")
warnings.filterwarnings("ignore", message="Some weights of.*were not initialized.*")
os.environ["HF_HUB_DISABLE_SYMLINKS_WARNING"] = "1"

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Load Environment Variables ---
load_dotenv()
HF_TOKEN = os.getenv("HF_API_Key2")
HF_API_URL = os.getenv("HF_URL_VISION") or "https://api-inference.huggingface.co/models/trpakov/vit-face-expression"
HF_IMAGE_DESCRIP_URL = os.getenv("Image_description_url")
NVIDIA_SCOUT_API = os.getenv("NVIDIA_LLAMA4_SCOUT_API_KEY")

# --- Import your SQLAlchemy models and session maker ---
from model.model_correct import AudioEmotion, ImageEmotion, SessionLocal

# --- Routers ---
audio_router = APIRouter()
image_router = APIRouter()

# --- DB Dependency ---
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# --- Request Models ---
class AudioInput(BaseModel):
    patient_id: str = "default_patient"
    conversation_id: str = "default_conversation"
    session_id: str = None
    audio_base64: str
    timestamp: datetime = datetime.now(timezone.utc)

class ImageInput(BaseModel):
    patient_id: str = "default_patient"
    conversation_id: str = "default_conversation"
    session_id: str = None
    image_base64: str
    timestamp: datetime = datetime.now(timezone.utc)

# --- Global Variables ---
_deepface_initialized = False
_audio_emotion_pipeline = None

# --- Emotion Mappings ---
emotion_mapping = {
    # Standard emotions
    'angry': 'anger', 'anger': 'anger', 'ang': 'anger',
    'disgust': 'disgust', 'disgusted': 'disgust', 'dis': 'disgust',
    'fear': 'fear', 'fearful': 'fear', 'afraid': 'fear', 'fea': 'fear',
    'happy': 'joy', 'happiness': 'joy', 'joy': 'joy', 'joyful': 'joy', 'hap': 'joy',
    'sad': 'sadness', 'sadness': 'sadness',
    'surprise': 'surprise', 'surprised': 'surprise', 'sur': 'surprise',
    'neutral': 'neutral', 'neu': 'neutral',
    # Additional emotion labels that models might output
    'calm': 'calm', 'peaceful': 'calm', 'relaxed': 'calm', 'cal': 'calm',
    'excited': 'excitement', 'excitement': 'excitement',
    'frustrated': 'frustration', 'frustration': 'frustration',
    'anxious': 'anxiety', 'anxiety': 'anxiety', 'worried': 'anxiety',
    'confused': 'confusion', 'confusion': 'confusion',
    'bored': 'boredom', 'boredom': 'boredom',
    'content': 'contentment', 'contentment': 'contentment',
    'stressed': 'stress', 'stress': 'stress'
}

arousal_valence_map = {
    # Core emotions
    'anger': (0.8, 0.2), 'joy': (0.7, 0.8), 'sadness': (0.3, 0.2),
    'fear': (0.8, 0.3), 'surprise': (0.8, 0.6), 'disgust': (0.6, 0.2),
    'neutral': (0.5, 0.5),
    # Extended emotions
    'calm': (0.2, 0.7), 'excitement': (0.9, 0.8), 'frustration': (0.7, 0.3),
    'anxiety': (0.8, 0.3), 'confusion': (0.6, 0.4), 'boredom': (0.2, 0.3),
    'contentment': (0.3, 0.8), 'stress': (0.8, 0.2)
}

# --- Initialize DeepFace ---
def initialize_deepface():
    global _deepface_initialized
    if not _deepface_initialized:
        try:
            logger.info("🔄 Initializing DeepFace...")
            # Warm up DeepFace with a dummy image
            dummy_img = np.zeros((224, 224, 3), dtype=np.uint8)
            DeepFace.analyze(dummy_img, actions=["emotion"], enforce_detection=False, silent=True)
            _deepface_initialized = True
            logger.info("✅ DeepFace initialized successfully")
        except Exception as e:
            logger.error(f"❌ DeepFace initialization failed: {str(e)}")
            raise e

# --- Image Emotion Detection (Fixed function name) ---
def detect_image_emotion(image_path: str) -> Dict[str, Any]:
    """Detect emotion from image using DeepFace."""
    try:
        if not _deepface_initialized:
            initialize_deepface()
        
        logger.info("🧠 Starting DeepFace emotion analysis...")
        
        result = DeepFace.analyze(
            img_path=image_path,
            actions=["emotion"],
            enforce_detection=False,
            detector_backend='retinaface',  # Using retinaface like in audiovideo_emotion.py
            silent=True
        )
        
        if isinstance(result, list):
            if len(result) == 0:
                logger.warning("⚠️ No faces detected in image")
                return {"emotion": "neutral", "confidence": 0.5, "details": {}}
            result = result[0]
        
        emotions = result.get('emotion', {})
        dominant_emotion = result.get('dominant_emotion', 'neutral').lower()
        confidence = emotions.get(dominant_emotion, 0.5) / 100.0
        
        mapped_emotion = emotion_mapping.get(dominant_emotion, dominant_emotion)
        
        logger.info(f"✅ Image emotion detected: {mapped_emotion} ({confidence:.2f})")
        
        return {
            "emotion": mapped_emotion,
            "confidence": confidence,
            "details": emotions
        }
        
    except Exception as e:
        logger.error(f"❌ Image emotion detection failed: {str(e)}")
        return {"emotion": "neutral", "confidence": 0.5, "details": {}}

# --- Audio Pipeline Initialization ---
def get_audio_emotion_pipeline():
    global _audio_emotion_pipeline

    if _audio_emotion_pipeline is None:
        # List of models to try in order of preference (using models from audiovideo_emotion.py)
        models_to_try = [
            "prithivMLmods/Speech-Emotion-Classification",  # Primary model from audiovideo_emotion.py
            "ehcalabres/wav2vec2-lg-xlsr-en-speech-emotion-recognition",  # Fallback model
            "superb/wav2vec2-base-superb-er",  # Additional fallback
        ]

        for model_name in models_to_try:
            try:
                logger.info(f"🔄 Attempting to load audio emotion pipeline: {model_name}")

                _audio_emotion_pipeline = pipeline(
                    "audio-classification",
                    model=model_name,
                    device=0 if torch.cuda.is_available() else -1,
                    trust_remote_code=True  # Allow custom models
                )

                logger.info(f"✅ Audio emotion pipeline loaded successfully: {model_name}")

                # Test the pipeline with a dummy audio to ensure it works
                dummy_audio = np.random.randn(16000).astype(np.float32)  # 1 second of random audio
                test_result = _audio_emotion_pipeline(dummy_audio)
                logger.info(f"🧪 Pipeline test successful: {len(test_result)} predictions returned")

                # Log the test result to check for bias
                if test_result:
                    top_test_pred = max(test_result, key=lambda x: x['score'])
                    logger.info(f"🧪 Test prediction: {top_test_pred['label']} ({top_test_pred['score']:.4f})")

                    # Check if test always gives the same result (bias indicator)
                    if top_test_pred['label'].lower() == 'calm' and top_test_pred['score'] < 0.2:
                        logger.warning("⚠️ Model test shows potential bias towards 'calm' with low confidence")
                        logger.warning("⚠️ This model might have inherent bias issues")

                break

            except Exception as e:
                logger.error(f"❌ Failed to load model {model_name}: {str(e)}")
                _audio_emotion_pipeline = None
                continue

        if _audio_emotion_pipeline is None:
            logger.error("❌ All audio emotion models failed to load!")
            raise Exception("Could not load any audio emotion recognition model")

    return _audio_emotion_pipeline

# --- Audio Preprocessing ---
def preprocess_audio(audio_path: str, target_sr: int = 16000) -> np.ndarray:
    """Preprocess audio for better emotion recognition."""
    try:
        logger.info(f"🔧 Preprocessing audio: {audio_path}")

        # Load audio
        audio, sr = librosa.load(audio_path, sr=target_sr)
        original_length = len(audio)

        logger.info(f"📊 Original audio - Length: {original_length} samples, Duration: {original_length/sr:.2f}s")

        # Check if audio is too short
        min_duration = 0.5  # Minimum 0.5 seconds
        min_samples = int(min_duration * sr)

        if len(audio) < min_samples:
            logger.warning(f"⚠️ Audio too short ({len(audio)/sr:.2f}s), padding to {min_duration}s")
            # Pad with silence or repeat the audio
            if len(audio) > 0:
                # Repeat the audio to reach minimum length
                repeats = int(np.ceil(min_samples / len(audio)))
                audio = np.tile(audio, repeats)[:min_samples]
            else:
                # Create silence if audio is empty
                audio = np.zeros(min_samples)

        # Normalize audio
        if np.max(np.abs(audio)) > 0:
            audio = audio / np.max(np.abs(audio))

        # Remove silence from beginning and end
        audio = librosa.effects.trim(audio, top_db=20)[0]

        # Ensure minimum length after trimming
        if len(audio) < min_samples:
            audio = np.pad(audio, (0, min_samples - len(audio)), mode='constant')

        logger.info(f"📊 Processed audio - Length: {len(audio)} samples, Duration: {len(audio)/sr:.2f}s")

        return audio

    except Exception as e:
        logger.error(f"❌ Audio preprocessing failed: {str(e)}")
        # Return original audio as fallback
        audio, sr = librosa.load(audio_path, sr=target_sr)
        return audio

# --- Audio Emotion Prediction ---
def predict_audio_emotion(audio_path: str) -> Dict[str, Any]:
    """Predict emotion from audio using HuggingFace pipeline."""
    try:
        logger.info(f"🎵 === AUDIO EMOTION PREDICTION STARTED ===")
        logger.info(f"📁 Audio file path: {audio_path}")

        pipeline_model = get_audio_emotion_pipeline()

        # Load and preprocess audio with detailed logging
        logger.info("🔊 Loading and preprocessing audio file...")
        audio = preprocess_audio(audio_path, target_sr=16000)
        sr = 16000
        logger.info(f"📊 Audio preprocessed - Duration: {len(audio)/sr:.2f}s, Sample rate: {sr}, Shape: {audio.shape}")

        # Additional audio quality checks
        if len(audio) < 8000:  # Less than 0.5 seconds
            logger.warning("⚠️ Audio still short after preprocessing, prediction quality may be affected")

        # Check for silence
        if np.max(np.abs(audio)) < 0.01:
            logger.warning("⚠️ Audio appears to be mostly silent, prediction may not be accurate")

        # Get predictions with detailed logging
        logger.info("🧠 Running emotion prediction...")
        results = pipeline_model(audio, top_k=None)  # Get all predictions
        logger.info(f"📊 Raw model predictions: {results}")

        if not results:
            raise ValueError("No predictions returned from model")

        # Handle different result formats
        if isinstance(results, dict):
            # Single prediction format
            results = [results]

        # Log all predictions for debugging
        logger.info("📋 All predictions:")
        for i, pred in enumerate(results):
            logger.info(f"  {i+1}. {pred['label']}: {pred['score']:.4f}")

        # Check if all predictions are similar (indicating bias)
        if len(results) > 1:
            scores = [pred['score'] for pred in results]
            max_score = max(scores)
            min_score = min(scores)
            score_range = max_score - min_score

            if score_range < 0.1:  # Very small difference between predictions
                logger.warning(f"⚠️ POTENTIAL BIAS: All predictions have similar scores (range: {score_range:.4f})")
                logger.warning("⚠️ This might indicate the model is not confident or has bias issues")

            # Check if the same emotion dominates
            labels = [pred['label'] for pred in results]
            unique_labels = set(labels)
            if len(unique_labels) == 1:
                logger.warning(f"⚠️ MODEL BIAS: All predictions are '{labels[0]}'")
            elif len(unique_labels) < len(results) / 2:
                logger.warning(f"⚠️ LIMITED DIVERSITY: Only {len(unique_labels)} unique emotions in {len(results)} predictions")

        # Get top prediction
        top_prediction = max(results, key=lambda x: x['score'])
        emotion_label = top_prediction['label'].lower()
        confidence = top_prediction['score']

        logger.info(f"🏆 Top prediction: {emotion_label} (confidence: {confidence:.4f})")

        # Map emotion with detailed logging (handle abbreviations from prithivMLmods model)
        original_emotion = emotion_label
        mapped_emotion = emotion_mapping.get(emotion_label, emotion_label)
        logger.info(f"🗺️ Emotion mapping: '{original_emotion}' -> '{mapped_emotion}'")

        arousal, valence = arousal_valence_map.get(mapped_emotion, (0.5, 0.5))
        logger.info(f"📈 Arousal: {arousal}, Valence: {valence}")

        logger.info(f"🎯 Final result - Emotion: {mapped_emotion}, Confidence: {confidence:.4f}")

        return {
            "emotion": mapped_emotion,
            "confidence": confidence,
            "arousal": arousal,
            "valence": valence,
            "all_predictions": results
        }

    except Exception as e:
        logger.error(f"❌ Audio emotion prediction failed: {str(e)}")
        logger.error(f"❌ Exception type: {type(e).__name__}")
        import traceback
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        # Try HuggingFace API fallback
        return predict_audio_emotion_hf_api(audio_path)

# --- Alternative Model Testing ---
def test_audio_emotion_models(audio_path: str) -> Dict[str, Any]:
    """Test multiple models and return the best prediction."""
    logger.info("🧪 Testing multiple audio emotion models...")

    models_to_test = [
        "prithivMLmods/Speech-Emotion-Classification",
        "ehcalabres/wav2vec2-lg-xlsr-en-speech-emotion-recognition",
        "superb/wav2vec2-base-superb-er"
    ]

    results = {}
    best_result = None
    best_confidence = 0.0

    for model_name in models_to_test:
        try:
            logger.info(f"🔬 Testing model: {model_name}")

            # Create pipeline for this model
            test_pipeline = pipeline(
                "audio-classification",
                model=model_name,
                device=0 if torch.cuda.is_available() else -1,
                trust_remote_code=True
            )

            # Preprocess audio
            audio = preprocess_audio(audio_path, target_sr=16000)

            # Get predictions (use top_k=None for prithivMLmods model)
            if "prithivMLmods" in model_name:
                predictions = test_pipeline(audio, top_k=None)
            else:
                predictions = test_pipeline(audio)

            # Handle different result formats
            if isinstance(predictions, dict):
                predictions = [predictions]

            if predictions:
                top_pred = max(predictions, key=lambda x: x['score'])
                emotion_label = top_pred['label'].lower()
                confidence = top_pred['score']
                mapped_emotion = emotion_mapping.get(emotion_label, emotion_label)

                results[model_name] = {
                    "emotion": mapped_emotion,
                    "confidence": confidence,
                    "raw_predictions": predictions
                }

                logger.info(f"📊 {model_name}: {mapped_emotion} ({confidence:.4f})")

                # Track best result
                if confidence > best_confidence:
                    best_confidence = confidence
                    best_result = results[model_name]
                    best_result["model_used"] = model_name

        except Exception as e:
            logger.error(f"❌ Model {model_name} failed: {str(e)}")
            results[model_name] = {"error": str(e)}

    if best_result:
        logger.info(f"🏆 Best result: {best_result['emotion']} from {best_result['model_used']} ({best_result['confidence']:.4f})")
        return best_result
    else:
        logger.error("❌ All models failed!")
        return {
            "emotion": "neutral",
            "confidence": 0.5,
            "arousal": 0.5,
            "valence": 0.5,
            "model_used": "fallback"
        }

# --- HuggingFace API Fallback ---
def predict_audio_emotion_hf_api(audio_path: str) -> Dict[str, Any]:
    """Fallback method using HuggingFace API for audio emotion detection."""
    logger.info("🌐 Using HuggingFace API for audio emotion detection...")
    
    try:
        with open(audio_path, "rb") as f:
            audio_bytes = f.read()
        
        api_url = "https://api-inference.huggingface.co/models/prithivMLmods/Speech-Emotion-Classification"
        
        headers = {
            "Authorization": f"Bearer {HF_TOKEN}",
            "Content-Type": "audio/wav"
        }
        
        response = requests.post(api_url, headers=headers, data=audio_bytes)
        response.raise_for_status()
        
        results = response.json()
        
        if not results:
            raise ValueError("No results from HuggingFace API")
        
        top_prediction = max(results, key=lambda x: x['score'])
        emotion_label = top_prediction['label'].lower()
        confidence = top_prediction['score']
        
        mapped_emotion = emotion_mapping.get(emotion_label, emotion_label)
        arousal, valence = arousal_valence_map.get(mapped_emotion, (0.5, 0.5))
        
        return {
            "emotion": mapped_emotion,
            "confidence": confidence,
            "arousal": arousal,
            "valence": valence,
            "all_predictions": results
        }
        
    except Exception as e:
        logger.error(f"❌ HuggingFace API prediction failed: {str(e)}")
        # Return neutral as final fallback
        return {
            "emotion": "neutral",
            "confidence": 0.5,
            "arousal": 0.5,
            "valence": 0.5,
            "all_predictions": []
        }

# --- Image Description Function ---
def describe_user_image(image_path: str, api_url: str, token: str) -> str:
    """Generate description of an image using NVIDIA API."""
    try:
        with open(image_path, "rb") as image_file:
            image_b64 = base64.b64encode(image_file.read()).decode()
        
        headers = {
            "Authorization": f"Bearer {NVIDIA_SCOUT_API}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "meta/llama-3.2-90b-vision-instruct",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "Describe this image in detail."},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_b64}"}}
                    ]
                }
            ],
            "max_tokens": 300,
            "temperature": 0.7
        }
        
        response = requests.post(api_url, headers=headers, json=payload)
        response.raise_for_status()
        
        return response.json()["choices"][0]["message"]["content"]
        
    except Exception as e:
        logger.error(f"❌ Image description failed: {str(e)}")
        return "Unable to generate image description."

# --- API Endpoints ---

@audio_router.post("/analyze-conversation-audio", status_code=status.HTTP_200_OK)
async def analyze_conversation_audio(input: AudioInput, db: Session = Depends(get_db)):
    """Analyze audio emotion during a conversation for real-time emotion tracking."""
    logger.info("🎤 === AUDIO EMOTION ANALYSIS STARTED ===")
    
    temp_audio_path = None
    
    try:
        # Decode base64 audio
        audio_data = base64.b64decode(input.audio_base64.split(",")[-1])
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
            tmp_file.write(audio_data)
            temp_audio_path = tmp_file.name
        
        # Analyze emotion
        result = predict_audio_emotion(temp_audio_path)
        
        # Generate session_id if not provided
        session_id = input.session_id or f"conv_{input.conversation_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Create database record
        sample_id = str(uuid.uuid4())
        record = AudioEmotion(
            sample_id=sample_id,
            patient_id=input.patient_id,
            conversation_id=input.conversation_id,
            session_id=session_id,
            timestamp=input.timestamp,
            source="audio",
            emotion=result["emotion"],
            confidence=result["confidence"],
            arousal=result["arousal"],
            valence=result["valence"]
        )
        
        db.add(record)
        db.commit()
        
        response_data = {
            "status": "success",
            "sample_id": record.sample_id,
            "patient_id": input.patient_id,
            "conversation_id": input.conversation_id,
            "emotion_data": {
                "emotion": result["emotion"],
                "confidence": result["confidence"],
                "arousal": result["arousal"],
                "valence": result["valence"]
            },
            "timestamp": record.timestamp.isoformat()
        }
        
        return response_data
        
    except Exception as e:
        logger.error(f"❌ Audio analysis failed: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Audio analysis failed: {str(e)}")
        
    finally:
        if temp_audio_path and os.path.exists(temp_audio_path):
            os.unlink(temp_audio_path)

@image_router.post("/analyze-conversation-image", status_code=status.HTTP_200_OK)
async def analyze_conversation_image(payload: ImageInput, db: Session = Depends(get_db)):
    """Analyze facial emotion from image during a conversation using DeepFace."""
    logger.info("📷 === IMAGE EMOTION ANALYSIS STARTED ===")
    
    temp_image_path = None
    
    try:
        # Decode base64 image
        image_data = base64.b64decode(payload.image_base64.split(",")[-1])
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_file:
            tmp_file.write(image_data)
            temp_image_path = tmp_file.name
        
        # Analyze emotion using DeepFace
        result = detect_image_emotion(temp_image_path)
        
        # Generate session_id if not provided
        session_id = payload.session_id or f"conv_{payload.conversation_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Create database record
        sample_id = str(uuid.uuid4())
        record = ImageEmotion(
            sample_id=sample_id,
            patient_id=payload.patient_id,
            conversation_id=payload.conversation_id,
            session_id=session_id,
            timestamp=payload.timestamp,
            source="image",
            emotion=result["emotion"],
            confidence=result["confidence"]
        )
        
        db.add(record)
        db.commit()
        
        response_data = {
            "status": "success",
            "sample_id": record.sample_id,
            "patient_id": payload.patient_id,
            "conversation_id": payload.conversation_id,
            "emotion_data": {
                "emotion": result["emotion"],
                "confidence": result["confidence"]
            },
            "timestamp": record.timestamp.isoformat()
        }
        
        return response_data
        
    except Exception as e:
        logger.error(f"❌ Image analysis failed: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Image analysis failed: {str(e)}")
        
    finally:
        if temp_image_path and os.path.exists(temp_image_path):
            os.unlink(temp_image_path)

@image_router.post("/image-description", status_code=status.HTTP_200_OK)
async def describe_image(payload: ImageInput):
    """Generate description of an image."""
    temp_image_path = None
    
    try:
        # Decode base64 image
        image_data = base64.b64decode(payload.image_base64.split(",")[-1])
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_file:
            tmp_file.write(image_data)
            temp_image_path = tmp_file.name
        
        # Generate description
        description = describe_user_image(temp_image_path, HF_IMAGE_DESCRIP_URL, HF_TOKEN)
        
        return {"description": description}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Image description failed: {str(e)}")
        
    finally:
        if temp_image_path and os.path.exists(temp_image_path):
            os.unlink(temp_image_path)

@audio_router.get("/conversation/{conversation_id}/emotions")
def get_conversation_emotions(conversation_id: str, db: Session = Depends(get_db)):
    """Get all emotions for a specific conversation."""
    try:
        # Get audio emotions
        audio_emotions = db.query(AudioEmotion).filter(
            AudioEmotion.conversation_id == conversation_id
        ).order_by(AudioEmotion.timestamp.desc()).all()
        
        # Get image emotions
        image_emotions = db.query(ImageEmotion).filter(
            ImageEmotion.conversation_id == conversation_id
        ).order_by(ImageEmotion.timestamp.desc()).all()
        
        # Format response
        audio_data = [
            {
                "sample_id": emotion.sample_id,
                "patient_id": emotion.patient_id,
                "timestamp": emotion.timestamp.isoformat(),
                "emotion": emotion.emotion,
                "confidence": emotion.confidence,
                "arousal": emotion.arousal,
                "valence": emotion.valence,
                "source": "audio"
            }
            for emotion in audio_emotions
        ]
        
        image_data = [
            {
                "sample_id": emotion.sample_id,
                "patient_id": emotion.patient_id,
                "timestamp": emotion.timestamp.isoformat(),
                "emotion": emotion.emotion,
                "confidence": emotion.confidence,
                "source": "image"
            }
            for emotion in image_emotions
        ]
        
        return {
            "conversation_id": conversation_id,
            "total_audio_samples": len(audio_data),
            "total_image_samples": len(image_data),
            "audio_emotions": audio_data,
            "image_emotions": image_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving conversation emotions: {str(e)}")

@audio_router.post("/test-audio-models", status_code=status.HTTP_200_OK)
async def test_audio_models(input: AudioInput):
    """Test multiple audio emotion models and return comparison results."""
    logger.info("🧪 === TESTING MULTIPLE AUDIO MODELS ===")

    temp_audio_path = None

    try:
        # Decode base64 audio
        audio_data = base64.b64decode(input.audio_base64.split(",")[-1])

        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
            tmp_file.write(audio_data)
            temp_audio_path = tmp_file.name

        # Test multiple models
        result = test_audio_emotion_models(temp_audio_path)

        return {
            "status": "success",
            "test_results": result,
            "message": "Model testing completed"
        }

    except Exception as e:
        logger.error(f"❌ Model testing failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Model testing failed: {str(e)}")

    finally:
        if temp_audio_path and os.path.exists(temp_audio_path):
            os.unlink(temp_audio_path)

@audio_router.get("/health")
def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "enhanced_audiovideo_emotion_recognition",
        "features": ["deepface_emotion", "huggingface_audio_emotion", "conversation_tracking", "model_testing"],
        "models": {
            "image_emotion": "DeepFace with RetinaFace backend",
            "audio_emotion": "prithivMLmods/Speech-Emotion-Classification",
            "fallback_audio": "HuggingFace API"
        }
    }
