import React, { useState, useEffect, useRef, useCallback } from 'react';
import './Chat.css'; // Make sure to create this CSS file and copy the styles

const Chat = () => {
    // --- STATE MANAGEMENT ---
    const [patientId, setPatientId] = useState('f31a95c6-76ef-4bb2-936c-b258285682d9');
    const [selectedVoice, setSelectedVoice] = useState('nova');
    const [connectionState, setConnectionState] = useState({ status: 'disconnected', message: 'Please connect to start chatting' }); // 'disconnected', 'connecting', 'connected', 'error'
    const [isLoggedIn, setIsLoggedIn] = useState(false);
    const [messages, setMessages] = useState([]);
    const [patientInfo, setPatientInfo] = useState(null);
    const [currentPersona, setCurrentPersona] = useState('general');
    const [messageInput, setMessageInput] = useState('');
    const [isRecording, setIsRecording] = useState(false);

    // --- REFS FOR DIRECT INSTANCE MANAGEMENT ---
    const socket = useRef(null);
    const mediaRecorder = useRef(null);
    const audioChunks = useRef([]);
    const currentAudio = useRef(null);
    const chatMessagesEndRef = useRef(null);

    // --- UI HELPER FUNCTIONS ---

    const scrollToBottom = () => {
        chatMessagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    const showStatusMessage = useCallback((message, autoHide = true) => {
        setConnectionState(prev => ({ ...prev, message }));
        if (autoHide) {
            setTimeout(() => {
                 setConnectionState(prev => ({ ...prev, message: '' }));
            }, 3000);
        }
    }, []);


    // --- CORE LOGIC: WEBSOCKET & MEDIA RECORDER ---

    const handleConnect = useCallback(() => {
        if (!patientId) {
            alert('Please enter a patient ID');
            return;
        }

        setConnectionState({ status: 'connecting', message: 'Connecting to server...' });

        // Close existing socket if any
        if (socket.current) {
            socket.current.close();
        }

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/chat-final/${patientId}`;

        socket.current = new WebSocket(wsUrl);

        socket.current.onopen = () => {
            // NOTE: The original code sent a JWT token here. In a real-world scenario,
            // you would fetch or retrieve the token and send it.
            // For this example, we assume authentication is handled.
            socket.current.send(JSON.stringify({
                // token: "your_jwt_token",
                voice: selectedVoice
            }));
            setConnectionState({ status: 'connected', message: 'Connected to healthcare chat' });
            setIsLoggedIn(true);
        };

        socket.current.onclose = () => {
            setIsLoggedIn(false);
            setConnectionState({ status: 'disconnected', message: 'Disconnected. Please reconnect.' });
            if (isRecording) {
                stopRecording();
            }
        };

        socket.current.onerror = (error) => {
            console.error('WebSocket error:', error);
            setConnectionState({ status: 'error', message: 'Connection error. Please try again.' });
        };

        socket.current.onmessage = (event) => {
            const data = JSON.parse(event.data);

            if (data.error) {
                showStatusMessage(`Error: ${data.error}`);
                return;
            }

            if (data.patient_info) {
                setPatientInfo(data.patient_info);
            }

            // --- STREAMING LOGIC ---
            if (data.type === 'streaming_text') {
                setMessages(prev => {
                    const lastMessage = prev[prev.length - 1];
                    // If the last message is a streaming one, append to it
                    if (lastMessage && lastMessage.isStreaming) {
                        lastMessage.text += data.content;
                        return [...prev.slice(0, -1), lastMessage];
                    } else {
                        // Otherwise, start a new streaming message
                        return [...prev, {
                            id: `streaming-${Date.now()}`,
                            sender: 'ai',
                            text: data.content,
                            isStreaming: true,
                            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                        }];
                    }
                });
            } else if (data.type === 'streaming_complete') {
                setMessages(prev => {
                    const lastMessage = prev[prev.length - 1];
                    if (lastMessage && lastMessage.isStreaming) {
                        lastMessage.isStreaming = false;
                        lastMessage.keywords = data.extracted_keywords || [];
                        // We will handle final audio when that message type comes
                    }
                    return [...prev];
                });
                if (data.current_persona) {
                    setCurrentPersona(data.current_persona);
                }
            } else if (data.type === 'streaming_audio_final') {
                 setMessages(prev => {
                    const lastMessage = prev[prev.length - 1];
                    if (lastMessage && lastMessage.sender === 'ai') {
                         lastMessage.audioData = data.audio;
                    }
                    return [...prev];
                });
            }

            // Handle non-streaming (e.g., persona transfer) messages
            else if (data.response) {
                setMessages(prev => [...prev, {
                    id: data.response_id || `msg-${Date.now()}`,
                    sender: 'ai',
                    text: data.response,
                    keywords: data.extracted_keywords || [],
                    audioData: data.audio,
                    timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                }]);
                 if (data.current_persona) {
                    setCurrentPersona(data.current_persona);
                    showStatusMessage(`Transferred to ${data.current_persona} specialist`);
                }
            }

            // Handle user's transcribed audio message
            else if (data.transcription) {
                 if (data.transcription.trim()) {
                    setMessages(prev => [...prev, {
                        id: `user-${Date.now()}`,
                        sender: 'user',
                        text: data.transcription,
                        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                    }]);
                }
            }
        };

    }, [patientId, selectedVoice, isRecording, showStatusMessage]);

    const sendMessage = () => {
        const messageText = messageInput.trim();
        if (!messageText || !socket.current || socket.current.readyState !== WebSocket.OPEN) {
            return;
        }

        const newMessage = {
            id: `user-${Date.now()}`,
            sender: 'user',
            text: messageText,
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        };
        setMessages(prev => [...prev, newMessage]);

        socket.current.send(JSON.stringify({
            text: messageText,
            voice: selectedVoice
        }));

        setMessageInput('');
        showStatusMessage('Processing your message...', false);
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter') {
            sendMessage();
        }
    };

    const startRecording = async () => {
        if (isRecording) return;
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            mediaRecorder.current = new MediaRecorder(stream);
            audioChunks.current = [];

            mediaRecorder.current.addEventListener('dataavailable', event => {
                audioChunks.current.push(event.data);
            });

            mediaRecorder.current.addEventListener('stop', () => {
                const audioBlob = new Blob(audioChunks.current, { type: 'audio/webm' });
                const reader = new FileReader();
                reader.onloadend = () => {
                    const base64data = reader.result.split(',')[1];
                    socket.current.send(JSON.stringify({
                        audio: base64data,
                        voice: selectedVoice
                    }));
                };
                reader.readAsDataURL(audioBlob);
                showStatusMessage('Processing audio...', true);
            });

            mediaRecorder.current.start();
            setIsRecording(true);
            showStatusMessage('Recording audio...', false);
        } catch (error) {
            console.error("Error accessing microphone:", error);
            showStatusMessage(`Mic error: ${error.message}`);
        }
    };

    const stopRecording = () => {
        if (!isRecording || !mediaRecorder.current) return;
        mediaRecorder.current.stop();
        mediaRecorder.current.stream.getTracks().forEach(track => track.stop());
        setIsRecording(false);
    };

    const toggleRecording = () => {
        isRecording ? stopRecording() : startRecording();
    };
    
    const selectPersona = (persona) => {
        if (persona === currentPersona || !socket.current || socket.current.readyState !== WebSocket.OPEN) {
            return;
        }
        socket.current.send(JSON.stringify({
            select_specialist: persona,
            voice: selectedVoice
        }));
        showStatusMessage(`Switching to ${persona} specialist...`);
        // The persona state will be updated via the websocket message confirmation
    };

    const personaDisplayNames = {
        'general': 'General OPD',
        'psychologist': 'Psychologist',
        'dietician': 'Dietician'
    };

    // --- RENDER ---
    if (!isLoggedIn) {
        return <LoginForm {...{ patientId, setPatientId, selectedVoice, setSelectedVoice, handleConnect, connectionState }} />;
    }

    return (
        <div className="container-fluid d-flex flex-column h-100 py-3">
            <div className="chat-container">
                <div className="chat-header">
                    <h2>
                        <i className="fas fa-hospital-user me-2"></i> Healthcare Chat
                        <span className={`persona-indicator persona-${currentPersona}`}>
                            {personaDisplayNames[currentPersona]}
                        </span>
                    </h2>
                    <div className="d-flex align-items-center">
                        <span className={`me-3 ${connectionState.status}`}>{connectionState.status}</span>
                    </div>
                </div>

                {connectionState.message && (
                     <div className={`status-bar ${!connectionState.message && 'fade-out'}`}>
                        <div id="statusMessage">{connectionState.message}</div>
                    </div>
                )}

                <div className="chat-body">
                    <Sidebar {...{ currentPersona, selectPersona, patientInfo }} />
                    <div className="chat-messages">
                        {messages.map((msg) => (
                            <Message key={msg.id} {...msg} currentAudio={currentAudio}/>
                        ))}
                         <div ref={chatMessagesEndRef} />
                    </div>
                </div>

                <div className="chat-input">
                    <input
                        type="text"
                        value={messageInput}
                        onChange={(e) => setMessageInput(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder="Type your message here..."
                        autoComplete="off"
                    />
                    <select value={selectedVoice} onChange={(e) => setSelectedVoice(e.target.value)} className="voice-select" title="Select AI voice">
                        <option value="nova">Nova</option>
                        <option value="alloy">Alloy</option>
                        <option value="echo">Echo</option>
                        <option value="fable">Fable</option>
                        <option value="onyx">Onyx</option>
                        <option value="shimmer">Shimmer</option>
                    </select>
                    <button onClick={toggleRecording} className={`btn-mic ${isRecording ? 'recording' : ''}`} title="Record audio">
                        <i className="fas fa-microphone"></i>
                    </button>
                    <button onClick={sendMessage} className="btn-send" title="Send message">
                        <i className="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    );
};

// --- SUB-COMPONENTS ---

const LoginForm = ({ patientId, setPatientId, selectedVoice, setSelectedVoice, handleConnect, connectionState }) => (
    <div className="login-container">
        <h2><i className="fas fa-hospital-user me-2"></i> Multi-Persona Healthcare Chat</h2>
        <div className="mb-3">
            <label htmlFor="patientId" className="form-label">Patient ID</label>
            <input
                type="text"
                className="form-control"
                id="patientId"
                value={patientId}
                onChange={(e) => setPatientId(e.target.value)}
            />
        </div>
        <div className="mb-3">
            <label htmlFor="voiceSelect" className="form-label">AI Voice</label>
            <select
                className="form-select"
                id="voiceSelect"
                value={selectedVoice}
                onChange={(e) => setSelectedVoice(e.target.value)}
            >
                <option value="nova">Nova (Female, Soft)</option>
                <option value="alloy">Alloy (Neutral)</option>
                <option value="echo">Echo (Male)</option>
                <option value="fable">Fable (Female)</option>
                <option value="onyx">Onyx (Male, Deep)</option>
                <option value="shimmer">Shimmer (Female, Clear)</option>
            </select>
        </div>
         <div className="alert alert-info mb-3">
            <i className="fas fa-info-circle me-2"></i>
            <strong>Multi-Persona Chat:</strong> Start with the General OPD doctor who will assess your needs.
        </div>
        <button onClick={handleConnect} className="btn btn-primary w-100" disabled={connectionState.status === 'connecting'}>
            {connectionState.status === 'connecting' ? (
                <>
                    <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Connecting...
                </>
            ) : 'Connect'}
        </button>
    </div>
);

const Sidebar = ({ currentPersona, selectPersona, patientInfo }) => (
    <div className="sidebar">
        <div className="persona-selector">
            <h3 className="mb-3">Healthcare Providers</h3>
            <button onClick={() => selectPersona('general')} className={`persona-btn ${currentPersona === 'general' ? 'active' : ''}`}>
                <i className="fas fa-user-md"></i> General OPD
            </button>
            <button onClick={() => selectPersona('psychologist')} className={`persona-btn ${currentPersona === 'psychologist' ? 'active' : ''}`}>
                <i className="fas fa-brain"></i> Psychologist
            </button>
            <button onClick={() => selectPersona('dietician')} className={`persona-btn ${currentPersona === 'dietician' ? 'active' : ''}`}>
                <i className="fas fa-apple-alt"></i> Dietician
            </button>
        </div>
        <div className="patient-info">
            <h3>Patient Information</h3>
            <PatientInfoContent info={patientInfo} />
        </div>
    </div>
);

const PatientInfoContent = ({ info }) => {
    if (!info) {
        return (
            <div className="d-flex justify-content-center align-items-center h-100">
                <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                </div>
            </div>
        );
    }
    return (
        <div id="patientInfoContent">
             {info.name && (
                <div className="info-section">
                    <h4>Basic Info</h4>
                    <div className="info-item"><strong>Name:</strong> {info.name}</div>
                    <div className="info-item"><strong>Gender:</strong> {info.gender || 'N/A'}</div>
                    <div className="info-item"><strong>DOB:</strong> {info.dob || 'N/A'}</div>
                </div>
            )}
             {info.medical_history?.length > 0 && (
                <div className="info-section">
                    <h4>Medical History</h4>
                    {info.medical_history.slice(0, 3).map((mh, i) => (
                        <div key={i} className="info-item">
                            <strong>{mh.condition || 'Condition'}:</strong> {mh.notes || 'N/A'}
                            {mh.date && <div className="text-muted small">{mh.date}</div>}
                        </div>
                    ))}
                </div>
            )}
             {info.prescriptions?.length > 0 && (
                <div className="info-section">
                     <h4>Current Medications</h4>
                    {info.prescriptions.slice(0, 3).map((p, i) => (
                        <div key={i} className="info-item">
                            <strong>{p.medication || 'Medication'}:</strong> {p.dosage || 'N/A'}
                             <div>{p.instructions || ''}</div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};


const Message = ({ sender, text, timestamp, keywords, audioData, isStreaming, currentAudio }) => {
    const isAi = sender === 'ai';

    return (
        <div className={`message ${isAi ? 'ai-message' : 'user-message'} ${isStreaming ? 'streaming-message' : ''}`}>
            <div className={isStreaming ? 'streaming-text' : ''}>{text}</div>
            <div className="message-time">{timestamp}</div>

            {isStreaming && (
                <div className="streaming-indicator">
                    <i className="fas fa-circle-notch fa-spin"></i> Generating response...
                </div>
            )}
            
            {isAi && keywords && keywords.length > 0 && (
                <div className="keywords">
                    {keywords.map(kw => <span key={kw} className="keyword">{kw}</span>)}
                </div>
            )}
            
            {isAi && audioData && <AudioControls audioData={audioData} currentAudio={currentAudio}/>}
        </div>
    );
};

const AudioControls = ({ audioData, currentAudio }) => {
    const audioRef = useRef(new Audio(`data:audio/mp3;base64,${audioData}`));
    const [isPlaying, setIsPlaying] = useState(false);
    
    const togglePlayPause = () => {
        if (isPlaying) {
            audioRef.current.pause();
            setIsPlaying(false);
        } else {
            // Pause any other playing audio
            if (currentAudio.current && currentAudio.current !== audioRef.current) {
                currentAudio.current.pause();
                 // This requires a way to update the other component's state, which is complex.
                 // A simpler way is to just pause it without updating its UI state.
            }
            audioRef.current.play();
            currentAudio.current = audioRef.current; // Track this as the current audio
            setIsPlaying(true);
        }
    };
    
    useEffect(() => {
        const audio = audioRef.current;
        const handleEnded = () => setIsPlaying(false);
        audio.addEventListener('ended', handleEnded);

        // Auto-play the audio when it first appears
        const autoPlayTimer = setTimeout(() => {
            togglePlayPause();
        }, 500);
        
        return () => {
            audio.removeEventListener('ended', handleEnded);
            clearTimeout(autoPlayTimer);
            if (currentAudio.current === audio) {
                currentAudio.current = null;
            }
        };
    }, []); // Empty dependency array ensures this runs once.

    return (
        <div className="audio-controls">
            <button onClick={togglePlayPause} title={isPlaying ? 'Pause' : 'Play'}>
                <i className={`fas ${isPlaying ? 'fa-pause' : 'fa-play'}`}></i>
            </button>
        </div>
    );
};


export default Chat;