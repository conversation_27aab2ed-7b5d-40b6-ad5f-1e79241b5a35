import os
import base64
import threading
import uuid
import torch
import librosa
import requests
from datetime import datetime, timezone
from fastapi import FastAPI, APIRouter, HTTPException, status, Depends
from pydantic import BaseModel

from dotenv import load_dotenv
from transformers import pipeline
from deepface import DeepFace


load_dotenv()
HF_TOKEN = os.getenv("HF_API_Key")
#HF_API_URL = os.getenv("HF_URL_VISION")  # For image emotion
HF_IMAGE_DESCRIP_URL = os.getenv("Image_description_url")

headers_image = {
    "Authorization": f"Bearer {HF_TOKEN}",
    "Content-Type": "image/jpeg"
}


#---------------Image Emotion Detection---------------
def detect_image_emotion(image):
    result = DeepFace.analyze(
    img_path=image,           
    actions=["emotion"],              
    enforce_detection=True,          
    detector_backend='retinaface'     
    )
    return result[0]['dominant_emotion']

#---------------Audio Emotion Detection---------------
def predict_audio_emotion(audio_path: str) -> dict:

    classifier = pipeline(
    "audio-classification",
    model="prithivMLmods/Speech-Emotion-Classification",) 
    results = classifier(audio_path, top_k=None)

    # Map abbreviations to full names
    label_map = {
        "DIS": "Disgust",
        "SAD": "Sad",
        "ANG": "Angry",
        "NEU": "Neutral",
        "HAP": "Happy",
        "FEA": "Fear",
        "CAL": "Calm",
        "SUR": "Surprised"
    }


    # Sort by score descending
    sorted_results = sorted(results, key=lambda x: x['score'], reverse=True)

    top1 = sorted_results[0]
    top2 = sorted_results[1]

    diff = top1['score'] - top2['score']
    top1_label = label_map.get(top1['label'], top1['label'])
    top2_label = label_map.get(top2['label'], top2['label'])

    if diff < 0.1:
        return {
            "emotion": f"{top1_label}, {top2_label}",
            "confidence": f"{top1['score']:.3f}, {top2['score']:.3f}"
        }
    else:
        return {
            "emotion": top1_label,
            "confidence": f"{top1['score']:.3f}"
        }

    

#---------------Image Description---------------

def describe_user_image(image_path, hf_api_url, hf_token):
    if not os.path.isfile(image_path):
        raise FileNotFoundError(f"Image not found: {image_path}")

    with open(image_path, "rb") as f:
        image_bytes = f.read()
    image_b64 = base64.b64encode(image_bytes).decode("utf-8")

    headers = {
        "Authorization": f"Bearer {hf_token}",
        "Content-Type": "application/json"
    }

    payload = {
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "Describe this image in one sentence."},
                    {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_b64}"}}
                ]
            }
        ],
        "model": "accounts/fireworks/models/llama4-scout-instruct-basic"
    }

    response = requests.post(hf_api_url, headers=headers, json=payload)
    if response.status_code != 200:
        raise Exception(f"Error {response.status_code}: {response.text}")

    return response.json()["choices"][0]["message"]["content"]


# FastAPI app
app = FastAPI()

_audio_emotion = "unknown"
_image_emotion = "unknown"
_image_description = "No description"
_lock = threading.Lock()

def get_audio_emotion():
    with _lock:
        return _audio_emotion

def get_image_emotion():
    with _lock:
        return _image_emotion

def get_image_description():
    with _lock:
        return _image_description

def update_audio_emotion(emotion: str):
    global _audio_emotion
    with _lock:
        _audio_emotion = emotion

def update_image_emotion(emotion: str):
    global _image_emotion
    with _lock:
        _image_emotion = emotion

def update_image_description(description: str):
    global _image_description
    with _lock:
        _image_description = description

class AudioInput(BaseModel):
    session_id: str
    audio_base64: str
    timestamp: datetime = datetime.now(timezone.utc)

class ImageInput(BaseModel):
    session_id: str
    image_base64: str

@app.post("/audio/audio-emotion", status_code=status.HTTP_202_ACCEPTED)
async def analyze_audio(input: AudioInput):
    try:
        audio_data = base64.b64decode(input.audio_base64.split(",")[-1])
        audio_path = f"temp_audio_{uuid.uuid4()}.wav"
        with open(audio_path, "wb") as f:
            f.write(audio_data)

        result = predict_audio_emotion(audio_path)
        update_audio_emotion(result.get("emotion", "unknown"))

        return {"message": "Audio analyzed", "data": result}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

    finally:
        if 'audio_path' in locals() and os.path.exists(audio_path):
            os.remove(audio_path)

@app.post("/image/image-emotion", status_code=status.HTTP_200_OK)
async def analyze_image_emotion(input: ImageInput):
    try:
        image_data = base64.b64decode(input.image_base64.split(",")[-1])
        image_path = f"temp_img_{uuid.uuid4()}.jpg"
        with open(image_path, "wb") as f:
            f.write(image_data)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid base64 image. {e}")

    try:
        emotion = detect_image_emotion(image_path)
    finally:
        if os.path.exists(image_path):
            os.remove(image_path)

    update_image_emotion(emotion)
    return {"emotion": emotion}

@app.post("/image/image-description", status_code=status.HTTP_200_OK)
async def describe_image(input: ImageInput):
    try:
        image_data = base64.b64decode(input.image_base64.split(",")[-1])
        image_path = f"temp_desc_{uuid.uuid4()}.png"
        with open(image_path, "wb") as f:
            f.write(image_data)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid base64 image. {e}")

    try:
        description = describe_user_image(image_path, HF_IMAGE_DESCRIP_URL, HF_TOKEN)
    finally:
        if os.path.exists(image_path):
            os.remove(image_path)

    update_image_description(description)
    return {"description": description}

@app.get("/emotions/state")
async def get_emotions_state():
    return {
        "audio_emotion": get_audio_emotion(),
        "image_emotion": get_image_emotion(),
        "image_description": get_image_description()
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("audiovideo_emotion_recognition:app", host="0.0.0.0", port=8000, reload=True)
