{"cells": [{"cell_type": "code", "execution_count": 3, "id": "9318df2f", "metadata": {}, "outputs": [], "source": ["import tqdm as notebook_tqdm"]}, {"cell_type": "code", "execution_count": 4, "id": "991911ff", "metadata": {}, "outputs": [], "source": ["# Load model directly\n", "from transformers import AutoModel\n", "model = AutoModel.from_pretrained(\"vishrutjha/pph-emotion-classification-model\", trust_remote_code=True)"]}, {"cell_type": "code", "execution_count": 6, "id": "1842bd41", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Some weights of Wav2Vec2Model were not initialized from the model checkpoint at facebook/wav2vec2-base-960h and are newly initialized: ['wav2vec2.masked_spec_embed']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}], "source": ["from transformers import AutoFeatureExtractor, AutoModelForAudioClassification\n", "import librosa\n", "import torch\n", "import sys\n", "audio_path = r\"D:\\dev-dev\\developer3\\dr_tova_response.wav\" \n", "model_id = \"vishrutjha/pph-emotion-classification-model\"\n", "feature_extractor = AutoFeatureExtractor.from_pretrained(model_id, trust_remote_code=True)\n", "model = AutoModelForAudioClassification.from_pretrained(model_id, trust_remote_code=True)\n", "\n", "audio, sr = librosa.load(audio_path, sr=feature_extractor.sampling_rate)\n", "inputs = feature_extractor(audio, sampling_rate=feature_extractor.sampling_rate, return_tensors=\"pt\")\n", "\n", "model.eval()\n", "with torch.no_grad():\n", "    outputs = model(**inputs)\n", "\n", "logits = outputs.emotion_logits\n", "probs = torch.softmax(logits, dim=-1)\n", "predicted_id = torch.argmax(probs, dim=-1).item()\n", "\n", "emotion = model.config.id2label[predicted_id]\n", "confidence = probs[0][predicted_id].item()\n", "arousal, valence = outputs.arousal_valence[0].tolist()"]}, {"cell_type": "code", "execution_count": 7, "id": "7795bbda", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Predicted Emotion: disgust\n", "0.4207732677459717\n"]}], "source": ["print(f\"Predicted Emotion: {emotion}\")\n", "print(confidence)"]}, {"cell_type": "code", "execution_count": 5, "id": "434a4fa8", "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "\n", "\n", "\n", "# --- Load Environment Variables ---\n", "load_dotenv()\n", "HF_TOKEN = os.getenv(\"HF_API_Key2\")\n", "HF_API_URL = os.getenv(\"HF_URL_VISION\") or \"https://api-inference.huggingface.co/models/trpakov/vit-face-expression\"\n", "# HF_AUDIO_URL = os.getenv(\"Audio_HF_URL\")\n", "HF_IMAGE_DESCRIP_URL = os.getenv(\"Image_description_url\")\n", "NVIDIA_SCOUT_API = os.getenv(\"NVIDIA_LLAMA4_SCOUT_API_KEY\")"]}, {"cell_type": "code", "execution_count": 3, "id": "c85231b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<Response [401]>\n"]}], "source": ["\n", "\n", "import requests\n", "import base64\n", "with open(r\"C:\\\\Users\\\\<USER>\\Downloads\\\\ang.jpg\", \"rb\") as f:\n", "    image_bytes = f.read()\n", "image_b64 = base64.b64encode(image_bytes).decode(\"utf-8\")\n", "\n", "headers = {\n", "    \"Authorization\": f\"Bearer {HF_TOKEN}\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "payload = {\n", "    \"messages\": [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [\n", "                {\"type\": \"text\", \"text\": \"Describe this image in one sentence.\"},\n", "                {\"type\": \"image_url\", \"image_url\": {\"url\": f\"data:image/png;base64,{image_b64}\"}}\n", "            ]\n", "        }\n", "    ],\n", "    \"model\": \"accounts/fireworks/models/llama4-scout-instruct-basic\"\n", "}\n", "\n", "response = requests.post(HF_API_URL, headers=headers, json=payload)\n", "print(response)"]}, {"cell_type": "code", "execution_count": 8, "id": "6104f161", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["neutral\n"]}], "source": ["HF_TOKEN = \"*************************************\"\n", "headers_image = {\n", "    \"Authorization\": f\"Bearer {HF_TOKEN }\",\n", "    \"Content-Type\": \"image/jpeg\"\n", "}\n", "\n", "\n", "with open(r\"C:\\\\Users\\\\<USER>\\Downloads\\\\anger.jpg\", \"rb\") as f:\n", "    data = f.read()\n", "response = requests.post(HF_API_URL, headers=headers_image, data=data)\n", "response.raise_for_status()\n", "predictions = response.json()\n", "if predictions:\n", "    top = max(predictions, key=lambda x: x[\"score\"])\n", "    print(top[\"label\"].lower())\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "id": "65dc7719", "metadata": {}, "outputs": [{"ename": "NotFoundError", "evalue": "Error code: 404 - {'error': {'code': 'DeploymentNotFound', 'message': 'The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.'}}", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mNotFoundError\u001b[39m                             <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[7]\u001b[39m\u001b[32m, line 11\u001b[39m\n\u001b[32m      4\u001b[39m \u001b[38;5;66;03m# Set your API key as an environment variable for security\u001b[39;00m\n\u001b[32m      5\u001b[39m client = AzureOpenAI(\n\u001b[32m      6\u001b[39m     api_key=\u001b[33m\"\u001b[39m\u001b[33m7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL\u001b[39m\u001b[33m\"\u001b[39m,  \u001b[38;5;66;03m# Set this in your environment\u001b[39;00m\n\u001b[32m      7\u001b[39m     api_version=\u001b[33m\"\u001b[39m\u001b[33m2024-02-01\u001b[39m\u001b[33m\"\u001b[39m,  \u001b[38;5;66;03m# More recent version\u001b[39;00m\n\u001b[32m      8\u001b[39m     azure_endpoint=\u001b[33m\"\u001b[39m\u001b[33mhttps://mentalhealth-bot.openai.azure.com/\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m      9\u001b[39m )\n\u001b[32m---> \u001b[39m\u001b[32m11\u001b[39m response = \u001b[43mclient\u001b[49m\u001b[43m.\u001b[49m\u001b[43mchat\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcompletions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     12\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mgpt4o-deployment\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# Your deployment name\u001b[39;49;00m\n\u001b[32m     13\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m=\u001b[49m\u001b[43m[\u001b[49m\n\u001b[32m     14\u001b[39m \u001b[43m        \u001b[49m\u001b[43m{\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrole\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43msystem\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcontent\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mYou are a JSON-generating assistant.\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     15\u001b[39m \u001b[43m        \u001b[49m\u001b[43m{\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrole\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43muser\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcontent\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mGive me a summary in JSON.\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m}\u001b[49m\n\u001b[32m     16\u001b[39m \u001b[43m    \u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     17\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0.1\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     18\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m850\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     19\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtop_p\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m1.0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     20\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfrequency_penalty\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0.0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     21\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpresence_penalty\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0.0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     22\u001b[39m \u001b[43m    \u001b[49m\u001b[43mresponse_format\u001b[49m\u001b[43m=\u001b[49m\u001b[43m{\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtype\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mjson_object\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m}\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# ✅ Correct format\u001b[39;49;00m\n\u001b[32m     23\u001b[39m \u001b[43m)\u001b[49m\n\u001b[32m     25\u001b[39m \u001b[38;5;28mprint\u001b[39m(response.choices[\u001b[32m0\u001b[39m].message.content)\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\dev-dev\\developer3\\env\\Lib\\site-packages\\openai\\_utils\\_utils.py:279\u001b[39m, in \u001b[36mrequired_args.<locals>.inner.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    277\u001b[39m             msg = \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mMissing required argument: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mquote(missing[\u001b[32m0\u001b[39m])\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    278\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[32m--> \u001b[39m\u001b[32m279\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\dev-dev\\developer3\\env\\Lib\\site-packages\\openai\\resources\\chat\\completions.py:859\u001b[39m, in \u001b[36mCompletions.create\u001b[39m\u001b[34m(self, messages, model, audio, frequency_penalty, function_call, functions, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, modalities, n, parallel_tool_calls, prediction, presence_penalty, reasoning_effort, response_format, seed, service_tier, stop, store, stream, stream_options, temperature, tool_choice, tools, top_logprobs, top_p, user, extra_headers, extra_query, extra_body, timeout)\u001b[39m\n\u001b[32m    817\u001b[39m \u001b[38;5;129m@required_args\u001b[39m([\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m], [\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m])\n\u001b[32m    818\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcreate\u001b[39m(\n\u001b[32m    819\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    856\u001b[39m     timeout: \u001b[38;5;28mfloat\u001b[39m | httpx.Timeout | \u001b[38;5;28;01mNone\u001b[39;00m | NotGiven = NOT_GIVEN,\n\u001b[32m    857\u001b[39m ) -> ChatCompletion | Stream[ChatCompletionChunk]:\n\u001b[32m    858\u001b[39m     validate_response_format(response_format)\n\u001b[32m--> \u001b[39m\u001b[32m859\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_post\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    860\u001b[39m \u001b[43m        \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m/chat/completions\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    861\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmaybe_transform\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    862\u001b[39m \u001b[43m            \u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m    863\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmessages\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    864\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmodel\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    865\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43maudio\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43maudio\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    866\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfrequency_penalty\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfrequency_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    867\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfunction_call\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunction_call\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    868\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfunctions\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    869\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mlogit_bias\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogit_bias\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    870\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mlogprobs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    871\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmax_completion_tokens\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_completion_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    872\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmax_tokens\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    873\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmetadata\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    874\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmodalities\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodalities\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    875\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mn\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    876\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mparallel_tool_calls\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mparallel_tool_calls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    877\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mprediction\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mprediction\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    878\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mpresence_penalty\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpresence_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    879\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mreasoning_effort\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mreasoning_effort\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    880\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mresponse_format\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mresponse_format\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    881\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mseed\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    882\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mservice_tier\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mservice_tier\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    883\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstop\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    884\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstore\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstore\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    885\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstream\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    886\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstream_options\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    887\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtemperature\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    888\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtool_choice\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtool_choice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    889\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtools\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    890\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtop_logprobs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_logprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    891\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtop_p\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_p\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    892\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43muser\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43muser\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    893\u001b[39m \u001b[43m            \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    894\u001b[39m \u001b[43m            \u001b[49m\u001b[43mcompletion_create_params\u001b[49m\u001b[43m.\u001b[49m\u001b[43mCompletionCreateParams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    895\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    896\u001b[39m \u001b[43m        \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmake_request_options\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    897\u001b[39m \u001b[43m            \u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\n\u001b[32m    898\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    899\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m=\u001b[49m\u001b[43mChatCompletion\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    900\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    901\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mStream\u001b[49m\u001b[43m[\u001b[49m\u001b[43mChatCompletionChunk\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    902\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\dev-dev\\developer3\\env\\Lib\\site-packages\\openai\\_base_client.py:1283\u001b[39m, in \u001b[36mSyncAPIClient.post\u001b[39m\u001b[34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[39m\n\u001b[32m   1269\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mpost\u001b[39m(\n\u001b[32m   1270\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   1271\u001b[39m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1278\u001b[39m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   1279\u001b[39m ) -> ResponseT | _StreamT:\n\u001b[32m   1280\u001b[39m     opts = FinalRequestOptions.construct(\n\u001b[32m   1281\u001b[39m         method=\u001b[33m\"\u001b[39m\u001b[33mpost\u001b[39m\u001b[33m\"\u001b[39m, url=path, json_data=body, files=to_httpx_files(files), **options\n\u001b[32m   1282\u001b[39m     )\n\u001b[32m-> \u001b[39m\u001b[32m1283\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m)\u001b[49m)\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\dev-dev\\developer3\\env\\Lib\\site-packages\\openai\\_base_client.py:960\u001b[39m, in \u001b[36mSyncAPIClient.request\u001b[39m\u001b[34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[39m\n\u001b[32m    957\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    958\u001b[39m     retries_taken = \u001b[32m0\u001b[39m\n\u001b[32m--> \u001b[39m\u001b[32m960\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    961\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    962\u001b[39m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    963\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    964\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    965\u001b[39m \u001b[43m    \u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    966\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\dev-dev\\developer3\\env\\Lib\\site-packages\\openai\\_base_client.py:1064\u001b[39m, in \u001b[36mSyncAPIClient._request\u001b[39m\u001b[34m(self, cast_to, options, retries_taken, stream, stream_cls)\u001b[39m\n\u001b[32m   1061\u001b[39m         err.response.read()\n\u001b[32m   1063\u001b[39m     log.debug(\u001b[33m\"\u001b[39m\u001b[33mRe-raising status error\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m1064\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m._make_status_error_from_response(err.response) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1066\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._process_response(\n\u001b[32m   1067\u001b[39m     cast_to=cast_to,\n\u001b[32m   1068\u001b[39m     options=options,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1072\u001b[39m     retries_taken=retries_taken,\n\u001b[32m   1073\u001b[39m )\n", "\u001b[31mNotFoundError\u001b[39m: Error code: 404 - {'error': {'code': 'DeploymentNotFound', 'message': 'The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.'}}"]}], "source": ["import os\n", "from openai import AzureOpenAI\n", "\n", "# Set your API key as an environment variable for security\n", "client = AzureOpenAI(\n", "    api_key=\"7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL\",  # Set this in your environment\n", "    api_version=\"2024-02-01\",  # More recent version\n", "    azure_endpoint=\"https://mentalhealth-bot.openai.azure.com/\"\n", ")\n", "\n", "response = client.chat.completions.create(\n", "    model=\"gpt4o-deployment\",  # Your deployment name\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": \"You are a JSON-generating assistant.\"},\n", "        {\"role\": \"user\", \"content\": \"Give me a summary in JSON.\"}\n", "    ],\n", "    temperature=0.1,\n", "    max_tokens=850,\n", "    top_p=1.0,\n", "    frequency_penalty=0.0,\n", "    presence_penalty=0.0,\n", "    response_format={\"type\": \"json_object\"}  # ✅ Correct format\n", ")\n", "\n", "print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "4b457d42", "metadata": {}, "outputs": [{"ename": "NotFoundError", "evalue": "Error code: 404 - {'error': {'code': 'DeploymentNotFound', 'message': 'The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.'}}", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mNotFoundError\u001b[39m                             <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 9\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mo<PERSON>ai\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m AzureOpenAI\n\u001b[32m      3\u001b[39m client = AzureOpenAI(\n\u001b[32m      4\u001b[39m     api_key=\u001b[33m\"\u001b[39m\u001b[33m7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m      5\u001b[39m     api_version=\u001b[33m\"\u001b[39m\u001b[33m2023-12-01-preview\u001b[39m\u001b[33m\"\u001b[39m,  \u001b[38;5;66;03m# latest for GPT-4o, JSON format, etc.\u001b[39;00m\n\u001b[32m      6\u001b[39m     azure_endpoint=\u001b[33m\"\u001b[39m\u001b[33mhttps://mentalhealth-bot.openai.azure.com/\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m      7\u001b[39m )\n\u001b[32m----> \u001b[39m\u001b[32m9\u001b[39m response = \u001b[43mclient\u001b[49m\u001b[43m.\u001b[49m\u001b[43mchat\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcompletions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     10\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mgpt4o-deployment\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# 🔁 Use your Azure deployment name here, NOT \"gpt-4o\"\u001b[39;49;00m\n\u001b[32m     11\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m=\u001b[49m\u001b[43m[\u001b[49m\n\u001b[32m     12\u001b[39m \u001b[43m        \u001b[49m\u001b[43m{\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrole\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43msystem\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcontent\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mYou are a JSON-generating assistant.\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     13\u001b[39m \u001b[43m        \u001b[49m\u001b[43m{\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrole\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43muser\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcontent\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mGive me a summary in JSON.\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m}\u001b[49m\n\u001b[32m     14\u001b[39m \u001b[43m    \u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     15\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0.1\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     16\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m850\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     17\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtop_p\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m1.0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     18\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfrequency_penalty\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0.0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     19\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpresence_penalty\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0.0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     20\u001b[39m \u001b[43m    \u001b[49m\u001b[43mresponse_format\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mjson_object\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# ✅ string, not dict\u001b[39;49;00m\n\u001b[32m     21\u001b[39m \u001b[43m)\u001b[49m\n\u001b[32m     23\u001b[39m \u001b[38;5;28mprint\u001b[39m(response.choices[\u001b[32m0\u001b[39m].message.content)\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\dev-dev\\developer3\\env\\Lib\\site-packages\\openai\\_utils\\_utils.py:279\u001b[39m, in \u001b[36mrequired_args.<locals>.inner.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    277\u001b[39m             msg = \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mMissing required argument: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mquote(missing[\u001b[32m0\u001b[39m])\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    278\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[32m--> \u001b[39m\u001b[32m279\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\dev-dev\\developer3\\env\\Lib\\site-packages\\openai\\resources\\chat\\completions.py:859\u001b[39m, in \u001b[36mCompletions.create\u001b[39m\u001b[34m(self, messages, model, audio, frequency_penalty, function_call, functions, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, modalities, n, parallel_tool_calls, prediction, presence_penalty, reasoning_effort, response_format, seed, service_tier, stop, store, stream, stream_options, temperature, tool_choice, tools, top_logprobs, top_p, user, extra_headers, extra_query, extra_body, timeout)\u001b[39m\n\u001b[32m    817\u001b[39m \u001b[38;5;129m@required_args\u001b[39m([\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m], [\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m])\n\u001b[32m    818\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcreate\u001b[39m(\n\u001b[32m    819\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    856\u001b[39m     timeout: \u001b[38;5;28mfloat\u001b[39m | httpx.Timeout | \u001b[38;5;28;01mNone\u001b[39;00m | NotGiven = NOT_GIVEN,\n\u001b[32m    857\u001b[39m ) -> ChatCompletion | Stream[ChatCompletionChunk]:\n\u001b[32m    858\u001b[39m     validate_response_format(response_format)\n\u001b[32m--> \u001b[39m\u001b[32m859\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_post\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    860\u001b[39m \u001b[43m        \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m/chat/completions\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    861\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmaybe_transform\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    862\u001b[39m \u001b[43m            \u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m    863\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmessages\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    864\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmodel\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    865\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43maudio\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43maudio\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    866\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfrequency_penalty\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfrequency_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    867\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfunction_call\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunction_call\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    868\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfunctions\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    869\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mlogit_bias\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogit_bias\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    870\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mlogprobs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    871\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmax_completion_tokens\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_completion_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    872\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmax_tokens\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    873\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmetadata\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    874\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmodalities\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodalities\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    875\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mn\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    876\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mparallel_tool_calls\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mparallel_tool_calls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    877\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mprediction\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mprediction\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    878\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mpresence_penalty\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpresence_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    879\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mreasoning_effort\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mreasoning_effort\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    880\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mresponse_format\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mresponse_format\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    881\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mseed\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    882\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mservice_tier\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mservice_tier\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    883\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstop\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    884\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstore\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstore\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    885\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstream\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    886\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstream_options\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    887\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtemperature\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    888\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtool_choice\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtool_choice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    889\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtools\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    890\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtop_logprobs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_logprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    891\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtop_p\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_p\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    892\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43muser\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43muser\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    893\u001b[39m \u001b[43m            \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    894\u001b[39m \u001b[43m            \u001b[49m\u001b[43mcompletion_create_params\u001b[49m\u001b[43m.\u001b[49m\u001b[43mCompletionCreateParams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    895\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    896\u001b[39m \u001b[43m        \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmake_request_options\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    897\u001b[39m \u001b[43m            \u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\n\u001b[32m    898\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    899\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m=\u001b[49m\u001b[43mChatCompletion\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    900\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    901\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mStream\u001b[49m\u001b[43m[\u001b[49m\u001b[43mChatCompletionChunk\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    902\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\dev-dev\\developer3\\env\\Lib\\site-packages\\openai\\_base_client.py:1283\u001b[39m, in \u001b[36mSyncAPIClient.post\u001b[39m\u001b[34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[39m\n\u001b[32m   1269\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mpost\u001b[39m(\n\u001b[32m   1270\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   1271\u001b[39m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1278\u001b[39m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   1279\u001b[39m ) -> ResponseT | _StreamT:\n\u001b[32m   1280\u001b[39m     opts = FinalRequestOptions.construct(\n\u001b[32m   1281\u001b[39m         method=\u001b[33m\"\u001b[39m\u001b[33mpost\u001b[39m\u001b[33m\"\u001b[39m, url=path, json_data=body, files=to_httpx_files(files), **options\n\u001b[32m   1282\u001b[39m     )\n\u001b[32m-> \u001b[39m\u001b[32m1283\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m)\u001b[49m)\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\dev-dev\\developer3\\env\\Lib\\site-packages\\openai\\_base_client.py:960\u001b[39m, in \u001b[36mSyncAPIClient.request\u001b[39m\u001b[34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[39m\n\u001b[32m    957\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    958\u001b[39m     retries_taken = \u001b[32m0\u001b[39m\n\u001b[32m--> \u001b[39m\u001b[32m960\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    961\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    962\u001b[39m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    963\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    964\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    965\u001b[39m \u001b[43m    \u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    966\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\dev-dev\\developer3\\env\\Lib\\site-packages\\openai\\_base_client.py:1064\u001b[39m, in \u001b[36mSyncAPIClient._request\u001b[39m\u001b[34m(self, cast_to, options, retries_taken, stream, stream_cls)\u001b[39m\n\u001b[32m   1061\u001b[39m         err.response.read()\n\u001b[32m   1063\u001b[39m     log.debug(\u001b[33m\"\u001b[39m\u001b[33mRe-raising status error\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m1064\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m._make_status_error_from_response(err.response) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1066\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._process_response(\n\u001b[32m   1067\u001b[39m     cast_to=cast_to,\n\u001b[32m   1068\u001b[39m     options=options,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1072\u001b[39m     retries_taken=retries_taken,\n\u001b[32m   1073\u001b[39m )\n", "\u001b[31mNotFoundError\u001b[39m: Error code: 404 - {'error': {'code': 'DeploymentNotFound', 'message': 'The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.'}}"]}], "source": ["from openai import AzureOpenAI\n", "\n", "client = AzureOpenAI(\n", "    api_key=\"7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL\",\n", "    api_version=\"2023-12-01-preview\",  # latest for GPT-4o, JSON format, etc.\n", "    azure_endpoint=\"https://mentalhealth-bot.openai.azure.com/\"\n", ")\n", "\n", "response = client.chat.completions.create(\n", "    model=\"gpt4o-deployment\",  # 🔁 Use your Azure deployment name here, NOT \"gpt-4o\"\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": \"You are a JSON-generating assistant.\"},\n", "        {\"role\": \"user\", \"content\": \"Give me a summary in JSON.\"}\n", "    ],\n", "    temperature=0.1,\n", "    max_tokens=850,\n", "    top_p=1.0,\n", "    frequency_penalty=0.0,\n", "    presence_penalty=0.0,\n", "    response_format=\"json_object\"  # ✅ string, not dict\n", ")\n", "\n", "print(response.choices[0].message.content)\n"]}, {"cell_type": "code", "execution_count": null, "id": "786d07fd", "metadata": {}, "outputs": [{"ename": "NotFoundError", "evalue": "Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mNotFoundError\u001b[39m                             <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 9\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mo<PERSON>ai\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m AzureOpenAI\n\u001b[32m      3\u001b[39m client = AzureOpenAI(\n\u001b[32m      4\u001b[39m     api_key=\u001b[33m\"\u001b[39m\u001b[33m7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m      5\u001b[39m     api_version=\u001b[33m\"\u001b[39m\u001b[33m22024-11-20\u001b[39m\u001b[33m\"\u001b[39m,  \u001b[38;5;66;03m# or latest version\u001b[39;00m\n\u001b[32m      6\u001b[39m     azure_endpoint=\u001b[33m\"\u001b[39m\u001b[33mhttps://mentalhealth-bot.openai.azure.com/\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m      7\u001b[39m )\n\u001b[32m----> \u001b[39m\u001b[32m9\u001b[39m response = \u001b[43mclient\u001b[49m\u001b[43m.\u001b[49m\u001b[43mchat\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcompletions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     10\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mazureml://registries/azure-openai/models/gpt-4o/versions/2024-11-20\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# This should match your deployment name\u001b[39;49;00m\n\u001b[32m     11\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mHello\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     12\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0.1\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     13\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m850\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     14\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtop_p\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m1.0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     15\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfrequency_penalty\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0.0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     16\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpresence_penalty\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0.0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     17\u001b[39m \u001b[43m    \u001b[49m\u001b[43mresponse_format\u001b[49m\u001b[43m=\u001b[49m\u001b[43m{\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtype\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mjson_object\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m}\u001b[49m\n\u001b[32m     18\u001b[39m \u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\dev-dev\\developer3\\env\\Lib\\site-packages\\openai\\_utils\\_utils.py:279\u001b[39m, in \u001b[36mrequired_args.<locals>.inner.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    277\u001b[39m             msg = \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mMissing required argument: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mquote(missing[\u001b[32m0\u001b[39m])\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    278\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[32m--> \u001b[39m\u001b[32m279\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\dev-dev\\developer3\\env\\Lib\\site-packages\\openai\\resources\\chat\\completions.py:859\u001b[39m, in \u001b[36mCompletions.create\u001b[39m\u001b[34m(self, messages, model, audio, frequency_penalty, function_call, functions, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, modalities, n, parallel_tool_calls, prediction, presence_penalty, reasoning_effort, response_format, seed, service_tier, stop, store, stream, stream_options, temperature, tool_choice, tools, top_logprobs, top_p, user, extra_headers, extra_query, extra_body, timeout)\u001b[39m\n\u001b[32m    817\u001b[39m \u001b[38;5;129m@required_args\u001b[39m([\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m], [\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m])\n\u001b[32m    818\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcreate\u001b[39m(\n\u001b[32m    819\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    856\u001b[39m     timeout: \u001b[38;5;28mfloat\u001b[39m | httpx.Timeout | \u001b[38;5;28;01mNone\u001b[39;00m | NotGiven = NOT_GIVEN,\n\u001b[32m    857\u001b[39m ) -> ChatCompletion | Stream[ChatCompletionChunk]:\n\u001b[32m    858\u001b[39m     validate_response_format(response_format)\n\u001b[32m--> \u001b[39m\u001b[32m859\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_post\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    860\u001b[39m \u001b[43m        \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m/chat/completions\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    861\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmaybe_transform\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    862\u001b[39m \u001b[43m            \u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m    863\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmessages\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    864\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmodel\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    865\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43maudio\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43maudio\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    866\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfrequency_penalty\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfrequency_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    867\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfunction_call\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunction_call\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    868\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfunctions\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    869\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mlogit_bias\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogit_bias\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    870\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mlogprobs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    871\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmax_completion_tokens\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_completion_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    872\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmax_tokens\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    873\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmetadata\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    874\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmodalities\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodalities\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    875\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mn\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    876\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mparallel_tool_calls\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mparallel_tool_calls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    877\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mprediction\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mprediction\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    878\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mpresence_penalty\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpresence_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    879\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mreasoning_effort\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mreasoning_effort\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    880\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mresponse_format\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mresponse_format\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    881\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mseed\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    882\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mservice_tier\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mservice_tier\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    883\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstop\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    884\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstore\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstore\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    885\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstream\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    886\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstream_options\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    887\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtemperature\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    888\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtool_choice\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtool_choice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    889\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtools\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    890\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtop_logprobs\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_logprobs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    891\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtop_p\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_p\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    892\u001b[39m \u001b[43m                \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43muser\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43muser\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    893\u001b[39m \u001b[43m            \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    894\u001b[39m \u001b[43m            \u001b[49m\u001b[43mcompletion_create_params\u001b[49m\u001b[43m.\u001b[49m\u001b[43mCompletionCreateParams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    895\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    896\u001b[39m \u001b[43m        \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmake_request_options\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    897\u001b[39m \u001b[43m            \u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\n\u001b[32m    898\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    899\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m=\u001b[49m\u001b[43mChatCompletion\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    900\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    901\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mStream\u001b[49m\u001b[43m[\u001b[49m\u001b[43mChatCompletionChunk\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    902\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\dev-dev\\developer3\\env\\Lib\\site-packages\\openai\\_base_client.py:1283\u001b[39m, in \u001b[36mSyncAPIClient.post\u001b[39m\u001b[34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[39m\n\u001b[32m   1269\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mpost\u001b[39m(\n\u001b[32m   1270\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   1271\u001b[39m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1278\u001b[39m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   1279\u001b[39m ) -> ResponseT | _StreamT:\n\u001b[32m   1280\u001b[39m     opts = FinalRequestOptions.construct(\n\u001b[32m   1281\u001b[39m         method=\u001b[33m\"\u001b[39m\u001b[33mpost\u001b[39m\u001b[33m\"\u001b[39m, url=path, json_data=body, files=to_httpx_files(files), **options\n\u001b[32m   1282\u001b[39m     )\n\u001b[32m-> \u001b[39m\u001b[32m1283\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m)\u001b[49m)\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\dev-dev\\developer3\\env\\Lib\\site-packages\\openai\\_base_client.py:960\u001b[39m, in \u001b[36mSyncAPIClient.request\u001b[39m\u001b[34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[39m\n\u001b[32m    957\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    958\u001b[39m     retries_taken = \u001b[32m0\u001b[39m\n\u001b[32m--> \u001b[39m\u001b[32m960\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    961\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    962\u001b[39m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    963\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    964\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    965\u001b[39m \u001b[43m    \u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    966\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\dev-dev\\developer3\\env\\Lib\\site-packages\\openai\\_base_client.py:1064\u001b[39m, in \u001b[36mSyncAPIClient._request\u001b[39m\u001b[34m(self, cast_to, options, retries_taken, stream, stream_cls)\u001b[39m\n\u001b[32m   1061\u001b[39m         err.response.read()\n\u001b[32m   1063\u001b[39m     log.debug(\u001b[33m\"\u001b[39m\u001b[33mRe-raising status error\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m1064\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m._make_status_error_from_response(err.response) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1066\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._process_response(\n\u001b[32m   1067\u001b[39m     cast_to=cast_to,\n\u001b[32m   1068\u001b[39m     options=options,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1072\u001b[39m     retries_taken=retries_taken,\n\u001b[32m   1073\u001b[39m )\n", "\u001b[31mNotFoundError\u001b[39m: Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}"]}], "source": ["from openai import AzureOpenAI\n", "\n", "client = AzureOpenAI(\n", "    api_key=\"7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL\",\n", "    api_version=\"2024-11-20\",  # or latest version\n", "    azure_endpoint=\"https://mentalhealth-bot.openai.azure.com/\"\n", ")\n", "\n", "response = client.chat.completions.create(\n", "    model=\"gpt-4o\",  # This should match your deployment name\n", "    messages=\"Hello\",\n", "    temperature=0.1,\n", "    max_tokens=850,\n", "    top_p=1.0,\n", "    frequency_penalty=0.0,\n", "    presence_penalty=0.0,\n", "    response_format={\"type\": \"json_object\"}\n", ")"]}, {"cell_type": "code", "execution_count": 9, "id": "f7b375d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error: 404, {\"error\":{\"code\":\"DeploymentNotFound\",\"message\":\"The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.\"}}\n"]}], "source": ["import requests  \n", "  \n", "endpoint = \"https://mentalhealth-bot.openai.azure.com/openai/deployments/<deployment-name>/completions?api-version=2023-03-15-preview\"  \n", "api_key = \"7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL\"  \n", "  \n", "headers = {  \n", "    \"Content-Type\": \"application/json\",  \n", "    \"api-key\": api_key  \n", "}  \n", "  \n", "data = {  \n", "    \"prompt\": \"Write a short story about AI and humanity.\",  \n", "    \"max_tokens\": 100,  \n", "    \"temperature\": 0.7  \n", "}  \n", "  \n", "response = requests.post(endpoint, headers=headers, json=data)  \n", "  \n", "if response.status_code == 200:  \n", "    print(response.json())  \n", "else:  \n", "    print(f\"Error: {response.status_code}, {response.text}\")  "]}, {"cell_type": "code", "execution_count": 12, "id": "50a7ee82", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<openai.Stream object at 0x0000019516CBEB10>\n"]}], "source": ["\n", "import os\n", "import base64\n", "from openai import AzureOpenAI\n", "\n", "endpoint = os.getenv(\"ENDPOINT_URL\", \"https://mentalhealth-bot.openai.azure.com/\")\n", "deployment = os.getenv(\"DEPLOYMENT_NAME\", \"gpt-4o\")\n", "subscription_key = os.getenv(\"AZURE_OPENAI_API_KEY\", \"7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL\")\n", "\n", "# Initialize Azure OpenAI client with key-based authentication\n", "client = AzureOpenAI(\n", "    azure_endpoint=endpoint,\n", "    api_key=subscription_key,\n", "    api_version=\"2025-01-01-preview\",\n", ")\n", "\n", "# IMAGE_PATH = \"YOUR_IMAGE_PATH\"\n", "# encoded_image = base64.b64encode(open(IMAGE_PATH, 'rb').read()).decode('ascii')\n", "\n", "#Prepare the chat prompt\n", "chat_prompt = [\n", "    {\n", "        \"role\": \"system\",\n", "        \"content\": [\n", "            {\n", "                \"type\": \"text\",\n", "                \"text\": \"You are an AI assistant that helps people find information.\"\n", "            }\n", "        ]\n", "    },\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [\n", "            {\n", "                \"type\": \"text\",\n", "                \"text\": \"I am going to Paris, what should I see?\"\n", "            }\n", "        ]\n", "    },\n", "    {\n", "        \"role\": \"assistant\",\n", "        \"content\": [\n", "            {\n", "                \"type\": \"text\",\n", "                \"text\": \"Paris, the capital of France, is known for its stunning architecture, art museums, historical landmarks, and romantic atmosphere. Here are some of the top attractions to see in Paris:\\n\\n1. The Eiffel Tower: The iconic Eiffel Tower is one of the most recognizable landmarks in the world and offers breathtaking views of the city.\\n2. The Louvre Museum: The Louvre is one of the world's largest and most famous museums, housing an impressive collection of art and artifacts, including the Mona Lisa.\\n3. Notre-Dame Cathedral: This beautiful cathedral is one of the most famous landmarks in Paris and is known for its Gothic architecture and stunning stained glass windows.\\n\\nThese are just a few of the many attractions that Paris has to offer. With so much to see and do, it's no wonder that Paris is one of the most popular tourist destinations in the world.\"\n", "            }\n", "        ]\n", "    },\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [\n", "            {\n", "                \"type\": \"text\",\n", "                \"text\": \"What is so great about #1?\"\n", "            }\n", "        ]\n", "    }\n", "]\n", "\n", "# Include speech result if speech is enabled\n", "messages = chat_prompt\n", "\n", "# Generate the completion\n", "completion = client.chat.completions.create(\n", "    model=deployment,\n", "    messages=messages,\n", "    max_tokens=800,\n", "    temperature=0.7,\n", "    top_p=0.95,\n", "    frequency_penalty=0,\n", "    presence_penalty=0,\n", "    stop=None,\n", "    stream=True\n", ")\n", "\n", "print(completion)\n", "    "]}, {"cell_type": "code", "execution_count": 13, "id": "1815755c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Streaming response:\n", "\n"]}], "source": ["import os  \n", "from openai import AzureOpenAI  \n", "  \n", "# Set up environment variables or direct values  \n", "endpoint = os.getenv(\"ENDPOINT_URL\", \"https://mentalhealth-bot.openai.azure.com/\")\n", "deployment = os.getenv(\"DEPLOYMENT_NAME\", \"gpt-4o\")\n", "subscription_key = os.getenv(\"AZURE_OPENAI_API_KEY\", \"7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL\")\n", "\n", "  \n", "# Initialize Azure OpenAI client  \n", "client = AzureOpenAI(  \n", "    azure_endpoint=endpoint,  \n", "    api_key=subscription_key,  \n", "    api_version=\"2025-01-01-preview\",  \n", ")  \n", "  \n", "# Prepare the chat prompt  \n", "chat_prompt = [  \n", "    {  \n", "        \"role\": \"system\",  \n", "        \"content\": \"You are an AI assistant that helps people find information.\",  \n", "    },  \n", "    {  \n", "        \"role\": \"user\",  \n", "        \"content\": \"I am going to Paris, what should I see?\",  \n", "    },  \n", "    {  \n", "        \"role\": \"assistant\",  \n", "        \"content\": (  \n", "            \"Paris, the capital of France, is known for its stunning architecture, \"  \n", "            \"art museums, historical landmarks, and romantic atmosphere. Here are \"  \n", "            \"some of the top attractions to see in Paris:\\n\\n\"  \n", "            \"1. The Eiffel Tower: The iconic Eiffel Tower is one of the most recognizable \"  \n", "            \"landmarks in the world and offers breathtaking views of the city.\\n\"  \n", "            \"2. The Louvre Museum: The Louvre is one of the world's largest and most famous \"  \n", "            \"museums, housing an impressive collection of art and artifacts, including the Mona Lisa.\\n\"  \n", "            \"3. Notre-Dame Cathedral: This beautiful cathedral is one of the most famous landmarks \"  \n", "            \"in Paris and is known for its Gothic architecture and stunning stained glass windows.\\n\\n\"  \n", "            \"These are just a few of the many attractions that Paris has to offer. With so much to see \"  \n", "            \"and do, it's no wonder that Paris is one of the most popular tourist destinations in the world.\"  \n", "        ),  \n", "    },  \n", "    {  \n", "        \"role\": \"user\",  \n", "        \"content\": \"What is so great about #1?\",  \n", "    },  \n", "]  \n", "  \n", "# Generate the completion with streaming  \n", "response = client.chat.completions.create(  \n", "    model=deployment,  \n", "    messages=chat_prompt,  \n", "    max_tokens=800,  \n", "    temperature=0.7,  \n", "    top_p=0.95,  \n", "    frequency_penalty=0,  \n", "    presence_penalty=0,  \n", "    stop=None,  \n", "    stream=True,  # Enable streaming  \n", ")  \n", "  \n", "# Handle the streaming response  \n", "print(\"Streaming response:\")  \n", "for chunk in response:  # Stream yields chunks of data  \n", "    if \"choices\" in chunk and chunk[\"choices\"]:  \n", "        for choice in chunk[\"choices\"]:  \n", "            if \"delta\" in choice and \"content\" in choice[\"delta\"]:  \n", "                print(choice[\"delta\"][\"content\"], end=\"\", flush=True)  \n", "print()  # Add a newline at the end  "]}, {"cell_type": "code", "execution_count": 15, "id": "47a787df", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting streaming chat...\n", "Error during streaming: Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}\n", "\n", "Trying alternative streaming method...\n", "Error during streaming: Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}\n"]}], "source": ["import os  \n", "from openai import AzureOpenAI  \n", "  \n", "# Set up environment variables or direct values  \n", "endpoint = os.getenv(\"ENDPOINT_URL\", \"https://mentalhealth-bot.openai.azure.com/\")\n", "deployment = os.getenv(\"DEPLOYMENT_NAME\", \"gpt-4o\")\n", "subscription_key = os.getenv(\"AZURE_OPENAI_API_KEY\", \"7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL\")\n", "\n", "# Initialize Azure OpenAI client  \n", "client = AzureOpenAI(  \n", "    azure_endpoint=endpoint,  \n", "    api_key=subscription_key,  \n", "    api_version=\"2024-11-20\",  # Use stable API version\n", ")  \n", "  \n", "# Prepare the chat prompt  \n", "chat_prompt = [  \n", "    {  \n", "        \"role\": \"system\",  \n", "        \"content\": \"You are an AI assistant that helps people find information.\",  \n", "    },  \n", "    {  \n", "        \"role\": \"user\",  \n", "        \"content\": \"I am going to Paris, what should I see?\",  \n", "    },  \n", "    {  \n", "        \"role\": \"assistant\",  \n", "        \"content\": (  \n", "            \"Paris, the capital of France, is known for its stunning architecture, \"  \n", "            \"art museums, historical landmarks, and romantic atmosphere. Here are \"  \n", "            \"some of the top attractions to see in Paris:\\n\\n\"  \n", "            \"1. The Eiffel Tower: The iconic Eiffel Tower is one of the most recognizable \"  \n", "            \"landmarks in the world and offers breathtaking views of the city.\\n\"  \n", "            \"2. The Louvre Museum: The Louvre is one of the world's largest and most famous \"  \n", "            \"museums, housing an impressive collection of art and artifacts, including the Mona Lisa.\\n\"  \n", "            \"3. Notre-Dame Cathedral: This beautiful cathedral is one of the most famous landmarks \"  \n", "            \"in Paris and is known for its Gothic architecture and stunning stained glass windows.\\n\\n\"  \n", "            \"These are just a few of the many attractions that Paris has to offer. With so much to see \"  \n", "            \"and do, it's no wonder that Paris is one of the most popular tourist destinations in the world.\"  \n", "        ),  \n", "    },  \n", "    {  \n", "        \"role\": \"user\",  \n", "        \"content\": \"What is so great about #1?\",  \n", "    },  \n", "]  \n", "\n", "def stream_chat_response():\n", "    \"\"\"Function to handle streaming chat response\"\"\"\n", "    try:\n", "        # Generate the completion with streaming  \n", "        response = client.chat.completions.create(  \n", "            model=deployment,  \n", "            messages=chat_prompt,  \n", "            max_tokens=800,  \n", "            temperature=0.7,  \n", "            top_p=0.95,  \n", "            frequency_penalty=0,  \n", "            presence_penalty=0,  \n", "            stop=None,  \n", "            stream=True,  # Enable streaming  \n", "        )  \n", "        \n", "        # Handle the streaming response  \n", "        print(\"Streaming response:\")  \n", "        print(\"-\" * 50)\n", "        \n", "        collected_content = \"\"\n", "        \n", "        for chunk in response:  \n", "            # Check if chunk has choices and content\n", "            if chunk.choices and len(chunk.choices) > 0:\n", "                delta = chunk.choices[0].delta\n", "                if hasattr(delta, 'content') and delta.content:\n", "                    content = delta.content\n", "                    print(content, end=\"\", flush=True)\n", "                    collected_content += content\n", "        \n", "        print(\"\\n\" + \"-\" * 50)\n", "        print(\"Streaming complete!\")\n", "        \n", "        return collected_content\n", "        \n", "    except Exception as e:\n", "        print(f\"Error during streaming: {e}\")\n", "        return None\n", "\n", "# Alternative method using dictionary access (if the above doesn't work)\n", "def stream_chat_response_alt():\n", "    \"\"\"Alternative streaming method using dictionary access\"\"\"\n", "    try:\n", "        response = client.chat.completions.create(  \n", "            model=deployment,  \n", "            messages=chat_prompt,  \n", "            max_tokens=800,  \n", "            temperature=0.7,  \n", "            top_p=0.95,  \n", "            frequency_penalty=0,  \n", "            presence_penalty=0,  \n", "            stop=None,  \n", "            stream=True,  \n", "        )  \n", "        \n", "        print(\"Streaming response (Alternative method):\")  \n", "        print(\"-\" * 50)\n", "        \n", "        for chunk in response:\n", "            chunk_dict = chunk.model_dump()  # Convert to dictionary\n", "            if chunk_dict.get(\"choices\"):\n", "                for choice in chunk_dict[\"choices\"]:\n", "                    delta = choice.get(\"delta\", {})\n", "                    if \"content\" in delta and delta[\"content\"]:\n", "                        print(delta[\"content\"], end=\"\", flush=True)\n", "        \n", "        print(\"\\n\" + \"-\" * 50)\n", "        print(\"Streaming complete!\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error during streaming: {e}\")\n", "\n", "if __name__ == \"__main__\":\n", "    # Try the first method\n", "    print(\"Starting streaming chat...\")\n", "    result = stream_chat_response()\n", "    \n", "    # If first method fails, try alternative\n", "    if result is None:\n", "        print(\"\\nTrying alternative streaming method...\")\n", "        stream_chat_response_alt()"]}, {"cell_type": "code", "execution_count": 17, "id": "f1fdab8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DEBUGGING AZURE OPENAI CONNECTION ===\n", "Endpoint: https://mentalhealth-bot.openai.azure.com/\n", "Deployment: gpt-4o\n", "API Key: 7KpKDJHlKN...\n", "\n", "✅ Client initialized successfully\n", "\n", "=== AVAILABLE DEPLOYMENTS ===\n", "❌ Could not list deployments: Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}\n", "\n", "=== TESTING DEPLOYMENT NAMES ===\n", "Testing 'openai-gpt-4o'... ❌ Not found\n", "Testing 'gpt-35-turbo'... ❌ Not found\n", "Testing 'gpt-35-turbo-16k'... ❌ Not found\n", "Testing 'gpt4o'... ❌ Not found\n", "Testing 'gpt-4o-deployment'... ❌ Not found\n", "Testing 'gpt-4o-2024-11-20'... ❌ Not found\n", "Testing 'gpt-4o'... ❌ Not found\n", "\n", "❌ NO WORKING DEPLOYMENTS FOUND!\n", "\n", "You need to create a deployment in Azure Portal:\n", "1. Go to Azure Portal → Your OpenAI Resource\n", "2. Navigate to 'Model deployments' or 'Deployments'\n", "3. Click '+ Create new deployment'\n", "4. Select GPT-4o model and create deployment\n", "5. Note the deployment name and use it in your code\n"]}], "source": ["import os  \n", "from openai import AzureOpenAI  \n", "\n", "\n", "\n", "print(\"=== DEBUGGING AZURE OPENAI CONNECTION ===\")\n", "print(f\"Endpoint: {endpoint}\")\n", "print(f\"Deployment: {deployment}\")\n", "print(f\"API Key: {subscription_key[:10]}...\" if subscription_key else \"No API Key\")\n", "print()\n", "\n", "# Step 1: Test basic connection and list available models/deployments\n", "def test_connection():\n", "    \"\"\"Test basic connection to Azure OpenAI\"\"\"\n", "    try:\n", "        client = AzureOpenAI(  \n", "            azure_endpoint=endpoint,  \n", "            api_key=subscription_key,  \n", "            api_version=\"2024-11-20\",\n", "        )\n", "        \n", "        print(\"✅ Client initialized successfully\")\n", "        \n", "        # Try to list available models/deployments\n", "        print(\"\\n=== AVAILABLE DEPLOYMENTS ===\")\n", "        try:\n", "            models = client.models.list()\n", "            print(\"Available deployments:\")\n", "            for model in models:\n", "                print(f\"  - {model.id}\")\n", "            return client, [model.id for model in models]\n", "        except Exception as e:\n", "            print(f\"❌ Could not list deployments: {e}\")\n", "            return client, []\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Failed to initialize client: {e}\")\n", "        return None, []\n", "\n", "# Step 2: Test different common deployment names\n", "def test_deployment_names(client, available_deployments):\n", "    \"\"\"Test common deployment names\"\"\"\n", "    if not client:\n", "        return None\n", "        \n", "    # Common deployment names to try\n", "    common_names = [\n", "        \"gpt-4o\",\n", "        \"gpt4o\", \n", "        \"gpt-4o-2024-11-20\",\n", "        \"gpt-4o-deployment\",\n", "        \"openai-gpt-4o\",\n", "        \"gpt-35-turbo\",  # Fallback option\n", "        \"gpt-35-turbo-16k\"  # Another fallback\n", "    ]\n", "    \n", "    # Add available deployments to test list\n", "    all_names_to_test = list(set(available_deployments + common_names))\n", "    \n", "    print(f\"\\n=== TESTING DEPLOYMENT NAMES ===\")\n", "    working_deployments = []\n", "    \n", "    for name in all_names_to_test:\n", "        try:\n", "            print(f\"Testing '{name}'...\", end=\" \")\n", "            \n", "            # Try a simple completion\n", "            response = client.chat.completions.create(\n", "                model=name,\n", "                messages=[{\"role\": \"user\", \"content\": \"Hi\"}],\n", "                max_tokens=5,\n", "                stream=False\n", "            )\n", "            \n", "            print(\"✅ WORKS!\")\n", "            working_deployments.append(name)\n", "            \n", "        except Exception as e:\n", "            error_msg = str(e)\n", "            if \"404\" in error_msg or \"DeploymentNotFound\" in error_msg:\n", "                print(\"❌ Not found\")\n", "            else:\n", "                print(f\"❌ Error: {error_msg}\")\n", "    \n", "    return working_deployments\n", "\n", "# Step 3: Test streaming with working deployment\n", "def test_streaming(client, deployment_name):\n", "    \"\"\"Test streaming with a working deployment\"\"\"\n", "    if not client or not deployment_name:\n", "        return False\n", "        \n", "    print(f\"\\n=== TESTING STREAMING WITH '{deployment_name}' ===\")\n", "    try:\n", "        response = client.chat.completions.create(\n", "            model=deployment_name,\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "                {\"role\": \"user\", \"content\": \"Say hello in a friendly way\"}\n", "            ],\n", "            max_tokens=50,\n", "            temperature=0.7,\n", "            stream=True\n", "        )\n", "        \n", "        print(\"Streaming test:\")\n", "        print(\"-\" * 30)\n", "        \n", "        for chunk in response:\n", "            if chunk.choices and len(chunk.choices) > 0:\n", "                delta = chunk.choices[0].delta\n", "                if hasattr(delta, 'content') and delta.content:\n", "                    print(delta.content, end=\"\", flush=True)\n", "        \n", "        print(\"\\n\" + \"-\" * 30)\n", "        print(\"✅ Streaming works!\")\n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Streaming failed: {e}\")\n", "        return False\n", "\n", "# Main execution\n", "if __name__ == \"__main__\":\n", "    # Step 1: Test connection\n", "    client, available_deployments = test_connection()\n", "    \n", "    if not client:\n", "        print(\"\\n❌ CONNECTION FAILED!\")\n", "        print(\"\\nPlease check:\")\n", "        print(\"1. Your API key is correct and active\")\n", "        print(\"2. Your endpoint URL is correct\")\n", "        print(\"3. Your Azure OpenAI resource exists and is accessible\")\n", "        exit(1)\n", "    \n", "    # Step 2: Find working deployments\n", "    working_deployments = test_deployment_names(client, available_deployments)\n", "    \n", "    if not working_deployments:\n", "        print(\"\\n❌ NO WORKING DEPLOYMENTS FOUND!\")\n", "        print(\"\\nYou need to create a deployment in Azure Portal:\")\n", "        print(\"1. Go to Azure Portal → Your OpenAI Resource\")\n", "        print(\"2. Navigate to 'Model deployments' or 'Deployments'\")\n", "        print(\"3. Click '+ Create new deployment'\")\n", "        print(\"4. Select GPT-4o model and create deployment\")\n", "        print(\"5. Note the deployment name and use it in your code\")\n", "    else:\n", "        print(f\"\\n✅ WORKING DEPLOYMENTS FOUND: {working_deployments}\")\n", "        \n", "        # Step 3: Test streaming with first working deployment\n", "        best_deployment = working_deployments[0]\n", "        streaming_works = test_streaming(client, best_deployment)\n", "        \n", "        if streaming_works:\n", "            print(f\"\\n🎉 SUCCESS! Use deployment name: '{best_deployment}'\")\n", "            print(\"\\nUpdate your code with:\")\n", "            print(f\"deployment = '{best_deployment}'\")\n", "        else:\n", "            print(f\"\\n⚠️  Deployment works but streaming failed with '{best_deployment}'\")\n", "            print(\"Try using stream=False for non-streaming responses\")"]}, {"cell_type": "code", "execution_count": 18, "id": "f9762ca9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["❌ Error: Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}\n"]}], "source": ["import os  \n", "from openai import AzureOpenAI  \n", "  \n", "# Your working deployment names\n", "endpoint = \"https://mentalhealth-bot.openai.azure.com/\"\n", "deployment = \"gpt-4o\"  # or \"gpt-4o-2\"\n", "# subscription_key = \"your-actual-api-key\"  # Make sure this is current\n", "subscription_key = os.getenv(\"AZURE_OPENAI_API_KEY\", \"7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL\")\n", "\n", "\n", "client = AzureOpenAI(  \n", "    azure_endpoint=endpoint,  \n", "    api_key=subscription_key,  \n", "    api_version=\"2024-11-20\",\n", ")\n", "\n", "# Simple test first\n", "try:\n", "    response = client.chat.completions.create(\n", "        model=deployment,\n", "        messages=[{\"role\": \"user\", \"content\": \"Hi\"}],\n", "        max_tokens=10\n", "    )\n", "    print(\"✅ Connection works!\")\n", "    print(f\"Response: {response.choices[0].message.content}\")\n", "except Exception as e:\n", "    print(f\"❌ Error: {e}\")"]}, {"cell_type": "code", "execution_count": 19, "id": "0da86f6f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting streaming chat...\n", "Error during streaming: Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}\n", "\n", "Trying alternative streaming method...\n", "Error during streaming: Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}\n"]}], "source": ["import os  \n", "from openai import AzureOpenAI  \n", "  \n", "# Set up environment variables or direct values  \n", "endpoint = os.getenv(\"ENDPOINT_URL\", \"https://mentalhealth-bot.openai.azure.com/\")\n", "deployment = os.getenv(\"DEPLOYMENT_NAME\", \"gpt-4o\")  # Try \"gpt-4o\" or \"gpt-4o-2\"\n", "# subscription_key = os.getenv(\"AZURE_OPENAI_API_KEY\", \"your-api-key-here\")  # Use environment variable for security\n", "\n", "# Initialize Azure OpenAI client  \n", "client = AzureOpenAI(  \n", "    azure_endpoint=endpoint,  \n", "    api_key=subscription_key,  \n", "    api_version=\"2024-11-20\",  # Use stable API version\n", ")  \n", "  \n", "# Prepare the chat prompt  \n", "chat_prompt = [  \n", "    {  \n", "        \"role\": \"system\",  \n", "        \"content\": \"You are an AI assistant that helps people find information.\",  \n", "    },  \n", "    {  \n", "        \"role\": \"user\",  \n", "        \"content\": \"I am going to Paris, what should I see?\",  \n", "    },  \n", "    {  \n", "        \"role\": \"assistant\",  \n", "        \"content\": (  \n", "            \"Paris, the capital of France, is known for its stunning architecture, \"  \n", "            \"art museums, historical landmarks, and romantic atmosphere. Here are \"  \n", "            \"some of the top attractions to see in Paris:\\n\\n\"  \n", "            \"1. The Eiffel Tower: The iconic Eiffel Tower is one of the most recognizable \"  \n", "            \"landmarks in the world and offers breathtaking views of the city.\\n\"  \n", "            \"2. The Louvre Museum: The Louvre is one of the world's largest and most famous \"  \n", "            \"museums, housing an impressive collection of art and artifacts, including the Mona Lisa.\\n\"  \n", "            \"3. Notre-Dame Cathedral: This beautiful cathedral is one of the most famous landmarks \"  \n", "            \"in Paris and is known for its Gothic architecture and stunning stained glass windows.\\n\\n\"  \n", "            \"These are just a few of the many attractions that Paris has to offer. With so much to see \"  \n", "            \"and do, it's no wonder that Paris is one of the most popular tourist destinations in the world.\"  \n", "        ),  \n", "    },  \n", "    {  \n", "        \"role\": \"user\",  \n", "        \"content\": \"What is so great about #1?\",  \n", "    },  \n", "]  \n", "\n", "def stream_chat_response():\n", "    \"\"\"Function to handle streaming chat response\"\"\"\n", "    try:\n", "        # Generate the completion with streaming  \n", "        response = client.chat.completions.create(  \n", "            model=deployment,  \n", "            messages=chat_prompt,  \n", "            max_tokens=800,  \n", "            temperature=0.7,  \n", "            top_p=0.95,  \n", "            frequency_penalty=0,  \n", "            presence_penalty=0,  \n", "            stop=None,  \n", "            stream=True,  # Enable streaming  \n", "        )  \n", "        \n", "        # Handle the streaming response  \n", "        print(\"Streaming response:\")  \n", "        print(\"-\" * 50)\n", "        \n", "        collected_content = \"\"\n", "        \n", "        for chunk in response:  \n", "            # Check if chunk has choices and content\n", "            if chunk.choices and len(chunk.choices) > 0:\n", "                delta = chunk.choices[0].delta\n", "                if hasattr(delta, 'content') and delta.content:\n", "                    content = delta.content\n", "                    print(content, end=\"\", flush=True)\n", "                    collected_content += content\n", "        \n", "        print(\"\\n\" + \"-\" * 50)\n", "        print(\"Streaming complete!\")\n", "        \n", "        return collected_content\n", "        \n", "    except Exception as e:\n", "        print(f\"Error during streaming: {e}\")\n", "        return None\n", "\n", "# Alternative method using dictionary access (if the above doesn't work)\n", "def stream_chat_response_alt():\n", "    \"\"\"Alternative streaming method using dictionary access\"\"\"\n", "    try:\n", "        response = client.chat.completions.create(  \n", "            model=deployment,  \n", "            messages=chat_prompt,  \n", "            max_tokens=800,  \n", "            temperature=0.7,  \n", "            top_p=0.95,  \n", "            frequency_penalty=0,  \n", "            presence_penalty=0,  \n", "            stop=None,  \n", "            stream=True,  \n", "        )  \n", "        \n", "        print(\"Streaming response (Alternative method):\")  \n", "        print(\"-\" * 50)\n", "        \n", "        for chunk in response:\n", "            chunk_dict = chunk.model_dump()  # Convert to dictionary\n", "            if chunk_dict.get(\"choices\"):\n", "                for choice in chunk_dict[\"choices\"]:\n", "                    delta = choice.get(\"delta\", {})\n", "                    if \"content\" in delta and delta[\"content\"]:\n", "                        print(delta[\"content\"], end=\"\", flush=True)\n", "        \n", "        print(\"\\n\" + \"-\" * 50)\n", "        print(\"Streaming complete!\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error during streaming: {e}\")\n", "\n", "if __name__ == \"__main__\":\n", "    # Try the first method\n", "    print(\"Starting streaming chat...\")\n", "    result = stream_chat_response()\n", "    \n", "    # If first method fails, try alternative\n", "    if result is None:\n", "        print(\"\\nTrying alternative streaming method...\")\n", "        stream_chat_response_alt()"]}, {"cell_type": "code", "execution_count": 20, "id": "e822f0e3", "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "asyncio.run() cannot be called from a running event loop", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON>unt<PERSON>E<PERSON><PERSON>\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[20]\u001b[39m\u001b[32m, line 49\u001b[39m\n\u001b[32m     46\u001b[39m                 \u001b[38;5;28;01melif\u001b[39;00m event.type == \u001b[33m\"\u001b[39m\u001b[33mresponse.done\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m     47\u001b[39m                     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m49\u001b[39m \u001b[43mas<PERSON>io\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\anaconda3\\Lib\\asyncio\\runners.py:186\u001b[39m, in \u001b[36mrun\u001b[39m\u001b[34m(main, debug)\u001b[39m\n\u001b[32m    161\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Execute the coroutine and return the result.\u001b[39;00m\n\u001b[32m    162\u001b[39m \n\u001b[32m    163\u001b[39m \u001b[33;03mThis function runs the passed coroutine, taking care of\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    182\u001b[39m \u001b[33;03m    asyncio.run(main())\u001b[39;00m\n\u001b[32m    183\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    184\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m events._get_running_loop() \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    185\u001b[39m     \u001b[38;5;66;03m# fail fast with short traceback\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m186\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntime<PERSON><PERSON>r\u001b[39;00m(\n\u001b[32m    187\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33masyncio.run() cannot be called from a running event loop\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    189\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m Runner(debug=debug) \u001b[38;5;28;01mas\u001b[39;00m runner:\n\u001b[32m    190\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m runner.run(main)\n", "\u001b[31mRuntimeError\u001b[39m: asyncio.run() cannot be called from a running event loop"]}], "source": ["import os\n", "import base64\n", "import asyncio\n", "from openai import AsyncAzureOpenAI\n", "from azure.identity.aio import DefaultAzureCredential, get_bearer_token_provider\n", "\n", "async def main() -> None:\n", "    \"\"\"\n", "    When prompted for user input, type a message and hit enter to send it to the model.\n", "    Enter \"q\" to quit the conversation.\n", "    \"\"\"\n", "\n", "    # client = AsyncAzureOpenAI(\n", "    #     azure_endpoint=os.environ[\"AZURE_OPENAI_ENDPOINT\"],\n", "    #     api_key=os.environ[\"AZURE_OPENAI_API_KEY\"],\n", "    #     api_version=\"2025-04-01-preview\",\n", "    # )\n", "    async with client.beta.realtime.connect(\n", "        model=\"gpt-4o-realtime-preview\",  # deployment name of your model\n", "    ) as connection:\n", "        await connection.session.update(session={\"modalities\": [\"text\", \"audio\"]})  \n", "        while True:\n", "            user_input = input(\"Enter a message: \")\n", "            if user_input == \"q\":\n", "                break\n", "\n", "            await connection.conversation.item.create(\n", "                item={\n", "                    \"type\": \"message\",\n", "                    \"role\": \"user\",\n", "                    \"content\": [{\"type\": \"input_text\", \"text\": user_input}],\n", "                }\n", "            )\n", "            await connection.response.create()\n", "            async for event in connection:\n", "                if event.type == \"response.text.delta\":\n", "                    print(event.delta, flush=True, end=\"\")\n", "                elif event.type == \"response.audio.delta\":\n", "\n", "                    audio_data = base64.b64decode(event.delta)\n", "                    print(f\"Received {len(audio_data)} bytes of audio data.\")\n", "                elif event.type == \"response.audio_transcript.delta\":\n", "                    print(f\"Received text delta: {event.delta}\")\n", "                elif event.type == \"response.text.done\":\n", "                    print()\n", "                elif event.type == \"response.done\":\n", "                    break\n", "\n", "asyncio.run(main())"]}, {"cell_type": "code", "execution_count": null, "id": "0fbb1ace", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Testing connection first...\n", "❌ Regular chat failed: Error code: 404 - {'error': {'code': '404', 'message': 'Resource not found'}}\n", "\n", "❌ Basic connection failed. Fix regular chat first before trying Realtime API.\n"]}], "source": ["import os\n", "import base64\n", "import asyncio\n", "from openai import AsyncAzureOpenAI\n", "\n", "async def main() -> None:\n", "    \"\"\"\n", "    Azure OpenAI Realtime API example for text and audio conversations.\n", "    Enter \"q\" to quit the conversation.\n", "    \"\"\"\n", "    \n", "    # Initialize the Azure OpenAI client\n", "    client = AsyncAzureOpenAI(\n", "        azure_endpoint=os.environ.get(\"AZURE_OPENAI_ENDPOINT\", \"https://mentalhealth-bot.openai.azure.com/\"),\n", "        api_key=os.environ.get(\"AZURE_OPENAI_API_KEY\", \"your-api-key-here\"),\n", "        api_version=\"2024-10-01-preview\",  # Use correct API version for Realtime\n", "    )\n", "    \n", "    print(\"🎤 Azure OpenAI Realtime API Chat\")\n", "    print(\"Enter 'q' to quit\")\n", "    print(\"-\" * 40)\n", "    \n", "    try:\n", "        # Connect to the realtime API\n", "        async with client.beta.realtime.connect(\n", "            model=\"gpt-4o-realtime-preview\",  # Your realtime deployment name\n", "        ) as connection:\n", "            \n", "            # Configure session for text and audio\n", "            await connection.session.update(\n", "                session={\n", "                    \"modalities\": [\"text\", \"audio\"],\n", "                    \"voice\": \"alloy\",  # Options: alloy, echo, fable, onyx, nova, shimmer\n", "                    \"turn_detection\": {\"type\": \"server_vad\"},  # Voice activity detection\n", "                    \"input_audio_format\": \"pcm16\",\n", "                    \"output_audio_format\": \"pcm16\",\n", "                    \"temperature\": 0.8,\n", "                }\n", "            )\n", "            \n", "            print(\"✅ Connected to Realtime API\")\n", "            \n", "            while True:\n", "                try:\n", "                    user_input = input(\"\\n💬 You: \")\n", "                    if user_input.lower() == \"q\":\n", "                        print(\"👋 Goodbye!\")\n", "                        break\n", "                    \n", "                    if not user_input.strip():\n", "                        continue\n", "                    \n", "                    # Send user message\n", "                    await connection.conversation.item.create(\n", "                        item={\n", "                            \"type\": \"message\",\n", "                            \"role\": \"user\",\n", "                            \"content\": [{\"type\": \"input_text\", \"text\": user_input}],\n", "                        }\n", "                    )\n", "                    \n", "                    # Request response\n", "                    await connection.response.create()\n", "                    \n", "                    print(\"🤖 Assistant: \", end=\"\", flush=True)\n", "                    \n", "                    # Handle streaming response\n", "                    async for event in connection:\n", "                        if event.type == \"response.text.delta\":\n", "                            # Stream text response\n", "                            print(event.delta, end=\"\", flush=True)\n", "                            \n", "                        elif event.type == \"response.audio.delta\":\n", "                            # Handle audio data (if you want to play it)\n", "                            audio_data = base64.b64decode(event.delta)\n", "                            print(f\"\\n🔊 [Received {len(audio_data)} bytes of audio]\", end=\"\")\n", "                            # TODO: Add audio playback here if needed\n", "                            \n", "                        elif event.type == \"response.audio_transcript.delta\":\n", "                            # Audio transcript (what the AI is saying)\n", "                            print(f\"🎙️ [Audio transcript: {event.delta}]\", end=\"\")\n", "                            \n", "                        elif event.type == \"response.text.done\":\n", "                            # Text response completed\n", "                            print()  # New line after text is done\n", "                            \n", "                        elif event.type == \"response.done\":\n", "                            # Full response completed\n", "                            break\n", "                            \n", "                        elif event.type == \"error\":\n", "                            print(f\"\\n❌ Error: {event.error}\")\n", "                            break\n", "                            \n", "                except KeyboardInterrupt:\n", "                    print(\"\\n👋 Interrupted by user\")\n", "                    break\n", "                except Exception as e:\n", "                    print(f\"\\n❌ Error during conversation: {e}\")\n", "                    continue\n", "                    \n", "    except Exception as e:\n", "        print(f\"❌ Failed to connect to Realtime API: {e}\")\n", "        print(\"\\nTroubleshooting:\")\n", "        print(\"1. Make sure you have a 'gpt-4o-realtime-preview' deployment\")\n", "        print(\"2. Check your API key and endpoint\")\n", "        print(\"3. Verify the API version supports Realtime API\")\n", "        print(\"4. Ensure your Azure region supports Realtime API\")\n", "\n", "# Alternative version for testing regular chat (non-realtime)\n", "async def test_regular_chat():\n", "    \"\"\"Test regular Azure OpenAI chat to verify connection\"\"\"\n", "    client = AsyncAzureOpenAI(\n", "        azure_endpoint=os.environ.get(\"AZURE_OPENAI_ENDPOINT\", \"https://mentalhealth-bot.openai.azure.com/\"),\n", "        api_key=os.environ.get(\"AZURE_OPENAI_API_KEY\", \"7KpKDJHlKNNjQOyiplQV5QCnb4fwPbUvO6Td9egWRivEF8IVdjRxJQQJ99BFACYeBjFXJ3w3AAABACOGm5oL\"),\n", "        api_version=\"2024-11-20\",\n", "    )\n", "    \n", "    try:\n", "        response = await client.chat.completions.create(\n", "            model=\"gpt-4o\",  # Use your regular deployment name\n", "            messages=[{\"role\": \"user\", \"content\": \"Hello, this is a test\"}],\n", "            max_tokens=50\n", "        )\n", "        print(\"✅ Regular chat works!\")\n", "        print(f\"Response: {response.choices[0].message.content}\")\n", "        return True\n", "    except Exception as e:\n", "        print(f\"❌ Regular chat failed: {e}\")\n", "        return False\n", "\n", "if __name__ == \"__main__\":\n", "    print(\"Testing connection first...\")\n", "    \n", "    # Test regular chat first\n", "    if asyncio.run(test_regular_chat()):\n", "        print(\"\\n\" + \"=\"*50)\n", "        print(\"Starting Realtime API...\")\n", "        asyncio.run(main())\n", "    else:\n", "        print(\"\\n❌ Basic connection failed. Fix regular chat first before trying Realtime API.\")"]}, {"cell_type": "code", "execution_count": 1, "id": "e34688fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Audio saved to dr_tova_response.mp3\n"]}], "source": ["from openai import AzureOpenAI\n", "\n", "OPENAI_CLIENT = AzureOpenAI(\n", "    api_key=\"3UQVkRcpzKYj17XgXR4HCuCMiQDR0CMZNUR4RWqv6bg5rJRMC6p7JQQJ99BFACfhMk5XJ3w3AAAAACOG4Bp7\",\n", "    azure_endpoint=\"https://rohan-mcj7eabj-swedencentral.cognitiveservices.azure.com/\",\n", "    api_version=\"2025-03-01-preview\"\n", ")\n", "\n", "response = OPENAI_CLIENT.audio.speech.create(\n", "    model=\"tts\",          # must match your deployment name\n", "    voice=\"nova\",\n", "    input=\"Hello <PERSON><PERSON><PERSON>, this is <PERSON><PERSON> again. Let's talk about your progress today.\"\n", ")\n", "\n", "with open(\"dr_tova_response.mp3\", \"wb\") as f:\n", "    f.write(response.content)\n", "\n", "print(\"✅ Audio saved to dr_tova_response.mp3\")\n"]}, {"cell_type": "code", "execution_count": 31, "id": "2860bb1d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Audio saved to output.mp3\n"]}], "source": ["import asyncio\n", "from elevenlabs.client import ElevenLabs\n", "\n", "client = ElevenLabs(\n", "    api_key=\"***************************************************\",  # 🔑\n", ")\n", "\n", "\n", "# 🎤 Set voice and model\n", "voice_id = \"pMsXgVXv3BLzUgSXRplE\"  # Replace with the actual voice ID\n", "model_id = \"eleven_multilingual_v2\"\n", "output_format = \"mp3_44100_128\"  # common output format\n", "\n", "# 🧠 Text to convert\n", "text_to_convert = \"Hello <PERSON><PERSON><PERSON>, this is <PERSON><PERSON>. How are you feeling today?\"\n", "\n", "# 🔁 Stream and save the audio\n", "output_path = \"output.mp3\"\n", "with open(output_path, \"wb\") as f:\n", "    for chunk in client.text_to_speech.stream(\n", "        voice_id=voice_id,\n", "        output_format=output_format,\n", "        text=text_to_convert,\n", "        model_id=model_id\n", "    ):\n", "        f.write(chunk)\n", "\n", "print(f\"✅ Audio saved to {output_path}\")"]}, {"cell_type": "code", "execution_count": 35, "id": "1bfbc53b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["data[0]: length=1536, [-0.00721184303984046, 0.007491494063287973, ..., 0.01611734740436077, -0.004887983202934265]\n", "data[1]: length=1536, [-0.003025691257789731, 0.009231699630618095, ..., 0.029947662726044655, 0.020937401801347733]\n", "data[2]: length=1536, [-0.013795719482004642, 0.031857650727033615, ..., 0.017506178468465805, 0.0226223636418581]\n", "Usage(prompt_tokens=6, total_tokens=6)\n"]}], "source": ["import os\n", "from openai import AzureOpenAI\n", "\n", "endpoint = \"https://rohan-mcj7eabj-swedencentral.cognitiveservices.azure.com/\"\n", "model_name = \"text-embedding-3-small\"\n", "deployment = \"text-embedding-3-small\"\n", "\n", "api_version = \"2024-02-01\"\n", "\n", "# client = AzureOpenAI(\n", "#     api_version=\"2024-12-01-preview\",\n", "#     endpoint=endpoint,\n", "#     credential=\"3UQVkRcpzKYj17XgXR4HCuCMiQDR0CMZNUR4RWqv6bg5rJRMC6p7JQQJ99BFACfhMk5XJ3w3AAAAACOG4Bp7\"\n", "# )\n", "client = AzureOpenAI(\n", "    azure_endpoint=endpoint,\n", "    api_key=\"3UQVkRcpzKYj17XgXR4HCuCMiQDR0CMZNUR4RWqv6bg5rJRMC6p7JQQJ99BFACfhMk5XJ3w3AAAAACOG4Bp7\",\n", "    api_version=api_version,\n", ")\n", "\n", "response = client.embeddings.create(\n", "    input=[\"first phrase\",\"second phrase\",\"third phrase\"],\n", "    model=deployment\n", ")\n", "\n", "for item in response.data:\n", "    length = len(item.embedding)\n", "    print(\n", "        f\"data[{item.index}]: length={length}, \"\n", "        f\"[{item.embedding[0]}, {item.embedding[1]}, \"\n", "        f\"..., {item.embedding[length-2]}, {item.embedding[length-1]}]\"\n", "    )\n", "print(response.usage)"]}, {"cell_type": "code", "execution_count": null, "id": "9218819a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}