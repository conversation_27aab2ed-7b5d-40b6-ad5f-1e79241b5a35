import os
import logging
import boto3
from botocore.exceptions import NoCredentialsError, ClientError
from dotenv import load_dotenv

# --- Configuration ---

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

# S3 Configuration from environment variables
S3_BUCKET = os.getenv("PDF_BUCKET_NAME")
AWS_ACCESS_KEY = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
AWS_REGION = os.getenv("AWS_REGION", "us-east-1")

# S3 paths for different FAISS indexes (must match your S3 bucket structure)
S3_PATHS = {
    "general": "faiss_index/general_index",
    "dietician": "faiss_index/dietician_index"
}

# --- Main Functions ---

def download_index_from_s3(index_name: str, local_base_path: str = "downloaded_faiss_indexes"):
    """
    Downloads the specified FAISS index files (index.faiss and index.pkl)
    from an S3 bucket to a local directory.

    Args:
        index_name (str): The key from the S3_PATHS dictionary (e.g., 'general').
        local_base_path (str): The root local directory to save files into.

    Returns:
        bool: True if download was successful, False otherwise.
    """
    # 1. Validate inputs
    if not all([S3_BUCKET, AWS_ACCESS_KEY, AWS_SECRET_KEY]):
        logger.error("❌ Missing S3 configuration. Please check your .env file.")
        return False

    if index_name not in S3_PATHS:
        logger.error(f"❌ Invalid index name '{index_name}'. Available options: {list(S3_PATHS.keys())}")
        return False

    s3_prefix = S3_PATHS[index_name]
    local_download_path = os.path.join(local_base_path, index_name)

    logger.info(f"Attempting to download index '{index_name}' from s3://{S3_BUCKET}/{s3_prefix}")
    logger.info(f"Files will be saved to: {local_download_path}")

    # 2. Initialize S3 client
    try:
        s3_client = boto3.client(
            's3',
            aws_access_key_id=AWS_ACCESS_KEY,
            aws_secret_access_key=AWS_SECRET_KEY,
            region_name=AWS_REGION
        )
        # Test connection by checking bucket existence
        s3_client.head_bucket(Bucket=S3_BUCKET)
        logger.info(f"✅ Successfully connected to S3 bucket '{S3_BUCKET}'.")

    except NoCredentialsError:
        logger.error("❌ AWS credentials not found. Please configure them.")
        return False
    except ClientError as e:
        if e.response['Error']['Code'] == '404':
            logger.error(f"❌ S3 Bucket '{S3_BUCKET}' not found.")
        else:
            logger.error(f"❌ An S3 client error occurred: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ An unexpected error occurred during S3 initialization: {e}")
        return False

    # 3. Find the index files in S3
    try:
        response = s3_client.list_objects_v2(Bucket=S3_BUCKET, Prefix=s3_prefix)
        if 'Contents' not in response:
            logger.error(f"❌ No files found in S3 at prefix: '{s3_prefix}'")
            return False

        keys_to_download = {}
        for obj in response['Contents']:
            key = obj['Key']
            if key.endswith('index.faiss'):
                keys_to_download['index.faiss'] = key
            elif key.endswith('index.pkl'):
                keys_to_download['index.pkl'] = key

        if 'index.faiss' not in keys_to_download or 'index.pkl' not in keys_to_download:
            logger.error(f"❌ Could not find both 'index.faiss' and 'index.pkl' at prefix '{s3_prefix}'.")
            found_files = [os.path.basename(k) for k in keys_to_download.values()]
            logger.info(f"Found files: {found_files if found_files else 'None'}")
            return False

        logger.info(f"Found required index files: {list(keys_to_download.keys())}")

        # 4. Create local directory and download files
        os.makedirs(local_download_path, exist_ok=True)

        for filename, s3_key in keys_to_download.items():
            local_file_path = os.path.join(local_download_path, filename)
            logger.info(f"Downloading {s3_key} -> {local_file_path} ...")
            s3_client.download_file(S3_BUCKET, s3_key, local_file_path)
            logger.info(f"✅ Successfully downloaded {filename}.")

        logger.info("\n🎉 All files downloaded successfully! 🎉")
        return True

    except ClientError as e:
        logger.error(f"❌ A client error occurred during download: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ An unexpected error occurred: {e}")
        return False


if __name__ == "__main__":
    print("--- FAISS Index Downloader ---")
    
    # Get user input for which index to download
    available_indexes = list(S3_PATHS.keys())
    print(f"Available indexes to download: {', '.join(available_indexes)}")
    
    choice = input("Enter the name of the index you want to download: ").strip().lower()

    if choice in available_indexes:
        download_index_from_s3(choice)
    else:
        print(f"\nInvalid choice '{choice}'. Please run the script again and choose from {available_indexes}.")