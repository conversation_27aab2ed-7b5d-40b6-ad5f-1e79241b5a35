#!/usr/bin/env python3
"""
Simple script to debug the 'calm' bias in audio emotion recognition.
"""

import logging
import numpy as np
from transformers import pipeline
import torch

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_calm_bias():
    """Test if the model has a bias towards 'calm' predictions."""
    logger.info("🔍 Testing for 'calm' bias in audio emotion model...")
    
    try:
        # Load the same model you're using (updated to new model)
        model_name = "prithivMLmods/Speech-Emotion-Classification"
        logger.info(f"📥 Loading model: {model_name}")
        
        emotion_pipeline = pipeline(
            "audio-classification",
            model=model_name,
            device=0 if torch.cuda.is_available() else -1
        )
        
        logger.info("✅ Model loaded successfully")
        
        # Test with different audio patterns
        test_cases = [
            ("silence", np.zeros(16000)),
            ("quiet_noise", np.random.randn(16000) * 0.01),
            ("normal_noise", np.random.randn(16000) * 0.1),
            ("loud_noise", np.random.randn(16000) * 0.5),
            ("sine_wave", np.sin(2 * np.pi * 440 * np.linspace(0, 1, 16000)) * 0.3),
            ("high_freq", np.sin(2 * np.pi * 2000 * np.linspace(0, 1, 16000)) * 0.3),
        ]
        
        calm_count = 0
        total_tests = len(test_cases)
        
        for test_name, audio_data in test_cases:
            logger.info(f"🧪 Testing: {test_name}")
            
            # Ensure proper format
            audio_data = audio_data.astype(np.float32)
            
            try:
                predictions = emotion_pipeline(audio_data, top_k=None)

                # Handle different result formats
                if isinstance(predictions, dict):
                    predictions = [predictions]

                if predictions:
                    top_pred = max(predictions, key=lambda x: x['score'])
                    emotion = top_pred['label'].lower()
                    confidence = top_pred['score']
                    
                    logger.info(f"  📊 Result: {emotion} ({confidence:.4f})")
                    
                    if 'calm' in emotion:
                        calm_count += 1
                        
                    # Log all predictions for this test
                    logger.info(f"  📋 All predictions:")
                    for pred in predictions[:3]:  # Top 3
                        logger.info(f"    - {pred['label']}: {pred['score']:.4f}")
                        
                else:
                    logger.warning(f"  ⚠️ No predictions returned for {test_name}")
                    
            except Exception as e:
                logger.error(f"  ❌ Error testing {test_name}: {str(e)}")
        
        # Analyze bias
        bias_percentage = (calm_count / total_tests) * 100
        logger.info(f"\n📊 BIAS ANALYSIS:")
        logger.info(f"  'Calm' predictions: {calm_count}/{total_tests} ({bias_percentage:.1f}%)")
        
        if bias_percentage > 70:
            logger.warning("🚨 SEVERE BIAS DETECTED: Model predicts 'calm' for most inputs")
            logger.warning("🔧 RECOMMENDATION: Consider using a different model or additional preprocessing")
        elif bias_percentage > 50:
            logger.warning("⚠️ MODERATE BIAS: Model has tendency towards 'calm' predictions")
        else:
            logger.info("✅ No significant bias detected")
            
        return bias_percentage
        
    except Exception as e:
        logger.error(f"❌ Failed to test model: {str(e)}")
        return None

def test_audio_characteristics():
    """Test what audio characteristics might trigger 'calm' predictions."""
    logger.info("\n🔬 Testing audio characteristics that might cause 'calm' bias...")
    
    try:
        model_name = "prithivMLmods/Speech-Emotion-Classification"
        emotion_pipeline = pipeline(
            "audio-classification",
            model=model_name,
            device=0 if torch.cuda.is_available() else -1
        )
        
        # Test different audio characteristics
        characteristics_tests = [
            ("very_short", np.random.randn(1000) * 0.1),  # 0.0625 seconds
            ("short", np.random.randn(4000) * 0.1),       # 0.25 seconds  
            ("normal_length", np.random.randn(16000) * 0.1),  # 1 second
            ("very_quiet", np.random.randn(16000) * 0.001),   # Very quiet
            ("normal_volume", np.random.randn(16000) * 0.1),  # Normal
            ("loud", np.random.randn(16000) * 0.8),           # Loud
        ]
        
        for test_name, audio_data in characteristics_tests:
            audio_data = audio_data.astype(np.float32)
            
            # Pad short audio to minimum length
            if len(audio_data) < 16000:
                audio_data = np.pad(audio_data, (0, 16000 - len(audio_data)), mode='constant')
            
            try:
                predictions = emotion_pipeline(audio_data, top_k=None)

                # Handle different result formats
                if isinstance(predictions, dict):
                    predictions = [predictions]

                if predictions:
                    top_pred = max(predictions, key=lambda x: x['score'])
                    logger.info(f"  {test_name}: {top_pred['label']} ({top_pred['score']:.4f})")
                    
            except Exception as e:
                logger.error(f"  ❌ {test_name}: {str(e)}")
                
    except Exception as e:
        logger.error(f"❌ Characteristics test failed: {str(e)}")

if __name__ == "__main__":
    logger.info("🚀 Starting 'calm' bias analysis...")
    
    # Test for bias
    bias_percentage = test_calm_bias()
    
    # Test audio characteristics
    test_audio_characteristics()
    
    logger.info("\n📋 SUMMARY:")
    if bias_percentage is not None:
        if bias_percentage > 70:
            logger.info("🔧 RECOMMENDED ACTIONS:")
            logger.info("  1. Try a different audio emotion model")
            logger.info("  2. Implement audio quality filtering")
            logger.info("  3. Add confidence thresholds")
            logger.info("  4. Consider ensemble methods with multiple models")
        elif bias_percentage > 50:
            logger.info("🔧 SUGGESTED IMPROVEMENTS:")
            logger.info("  1. Add audio preprocessing")
            logger.info("  2. Implement confidence-based filtering")
            logger.info("  3. Consider model fine-tuning")
    
    logger.info("✅ Analysis complete!")
