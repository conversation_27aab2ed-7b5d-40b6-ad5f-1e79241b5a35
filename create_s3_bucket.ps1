# PowerShell script to create an S3 bucket

# Bucket name and region
$bucketName = "prasha-health-pdf"
$region = "us-east-1"

# Create the bucket
Write-Host "Creating S3 bucket: $bucketName in region: $region"
aws s3api create-bucket --bucket $bucketName --region $region

# Configure bucket for PDF storage
Write-Host "Setting up bucket for PDF storage..."

# Create a bucket policy that allows the necessary permissions
$bucketPolicy = @"
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::463470954735:root"
            },
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::$bucketName",
                "arn:aws:s3:::$bucketName/*"
            ]
        }
    ]
}
"@

# Save the policy to a temporary file
$policyFile = "bucket_policy.json"
$bucketPolicy | Out-File -FilePath $policyFile -Encoding utf8

# Apply the policy to the bucket
Write-Host "Applying bucket policy..."
aws s3api put-bucket-policy --bucket $bucketName --policy file://$policyFile

# Create the necessary folders in the bucket
Write-Host "Creating folders in the bucket..."
aws s3api put-object --bucket $bucketName --key "pdfs/"
aws s3api put-object --bucket $bucketName --key "faiss_index/"

# Clean up
Remove-Item -Path $policyFile

Write-Host "S3 bucket '$bucketName' has been created and configured successfully!"
