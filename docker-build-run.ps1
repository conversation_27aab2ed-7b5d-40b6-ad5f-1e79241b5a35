# PowerShell script to build and run the Docker image locally

# Build the Docker image
Write-Host "Building Docker image..." -ForegroundColor Green
docker build -t prasha-health-ai .

# Check if the build was successful
if ($LASTEXITCODE -ne 0) {
    Write-Host "Docker build failed!" -ForegroundColor Red
    exit 1
}

Write-Host "Docker image built successfully!" -ForegroundColor Green

# Run the Docker container
Write-Host "Running Docker container..." -ForegroundColor Green
docker run -p 8000:8000 --env-file .env prasha-health-ai

# Note: The container will keep running until you stop it with Ctrl+C
