# ---------------------------
# PowerShell Script to Deploy FastAPI Container to Azure Container Instance
# ---------------------------

# Define resource group and container settings
$resourceGroup = "myResourceGroup"
$containerName = "myappcontainer"
$image = "chatfinal.azurecr.io/prasha-chat-ai4:latest"az container list --resource-group myResourceGroup --output table

$registryLoginServer = "chatfinal.azurecr.io"
$registryUsername = "chatfinal"
$registryPassword = "****************************************************"  # TODO: Replace with your actual ACR password
$dnsName = "prasha-chat-final"

# Delete container if it exists (ACI does not support update of CPU/mem)
az container delete --resource-group $resourceGroup --name $containerName --yes

# Create the container
az container create `
  --resource-group $resourceGroup `
  --name $containerName `
  --image $image `
  --cpu 2 --memory 3 `
  --registry-login-server $registryLoginServer `
  --registry-username $registryUsername `
  --registry-password $registryPassword `
  --dns-name-label $dnsName `
  --ports 8000 `
  --os-type Linux `
  --environment-variables `
    "OPENAI_API_KEY=***********************************************************************************************************************************************************************" `
    "HF_API_URL=https://router.huggingface.co/hf-inference/models/SamLowe/roberta-base-go_emotions" `
    "HF_API_KEY=*************************************" `
    "postgres_username=postgres" `
    "postgres_password=Prashaind2025" `
    "postgres_host=authenctication.cmb684u0s8ql.us-east-1.rds.amazonaws.com" `
    "postgres_port=5432" `
    "postgres_database=postgres" `
    "USE_S3=True" `
    "PDF_BUCKET_NAME=prasha-healthcare-pdf" `
    "AWS_ACCESS_KEY_ID=********************" `
    "AWS_SECRET_ACCESS_KEY=THEjwqZmsdhhAQpL9uSHfySvSGM8tZPaji6TqEvb" `
    "AWS_REGION=us-east-1" `
    "JWT_SECRET=e3ddffa7eb26539eb449c2f9fbd5bd0a566cf00bef73f37e015d826e0b602f0d" `
    "JWT_ALGORITHM=HS256" `
    "STATIC_JWT_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.3oZ2Ubh5rLBdHvQHd5Qr9GJczA5MXcxaVx5H5xLwvZ4"

Write-Host "`n✅ Deployment triggered. Check logs with:`"
Write-Host "az container logs --resource-group $resourceGroup --name $containerName"
Write-Host "🔗 Visit: http://$dnsName.eastus.azurecontainer.io:8000"
