#!/usr/bin/env python3
"""
Pre-download all models during Docker build time
"""
import os
import logging
import torch
import numpy as np
import cv2
from transformers import pipeline
from deepface import DeepFace
import tempfile
import shutil

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set local model cache directory
MODEL_CACHE_DIR = os.path.abspath('./model_cache')
os.environ['TRANSFORMERS_CACHE'] = MODEL_CACHE_DIR
os.environ['HF_HOME'] = MODEL_CACHE_DIR
os.environ['DEEPFACE_HOME'] = MODEL_CACHE_DIR  # For DeepFace

def download_audio_models():
    """Download audio emotion recognition models"""
    logger.info("🎵 Downloading audio emotion models...")
    
    try:
        # Primary audio model
        logger.info("Downloading primary audio model...")
        pipeline(
            "audio-classification",
            model="ehcalabres/wav2vec2-lg-xlsr-en-speech-emotion-recognition",
            device=0 if torch.cuda.is_available() else -1
        )
        logger.info("✅ Primary audio model downloaded")
        
        # Fallback audio model
        logger.info("Downloading fallback audio model...")
        pipeline(
            "audio-classification",
            model="superb/wav2vec2-base-superb-er",
            device=0 if torch.cuda.is_available() else -1
        )
        logger.info("✅ Fallback audio model downloaded")
        
    except Exception as e:
        logger.error(f"❌ Audio model download failed: {e}")
        raise

def download_deepface_models():
    """Download DeepFace emotion recognition models"""
    logger.info("📸 Downloading DeepFace models...")
    
    # Create dummy image
    dummy_image = np.zeros((224, 224, 3), dtype=np.uint8)
    tmp_file = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
    
    try:
        tmp_file.close()  # Close the file so Windows doesn't lock it
        cv2.imwrite(tmp_file.name, dummy_image)

        # Trigger DeepFace model download
        DeepFace.analyze(
            img_path=tmp_file.name,
            actions=["emotion"],
            enforce_detection=False,
            detector_backend='opencv',
            silent=True
        )

        logger.info("✅ DeepFace models downloaded")
    except Exception as e:
        logger.error(f"❌ DeepFace model download failed: {e}")
        raise
    finally:
        try:
            os.unlink(tmp_file.name)
        except Exception as e:
            logger.warning(f"⚠️ Could not delete temp file: {e}")

def download_text_models():
    """Download any text/NLP models if needed"""
    logger.info("📝 Downloading text models...")
    
    try:
        pipeline(
            "text-classification",
            model="j-hartmann/emotion-english-distilroberta-base",
            device=0 if torch.cuda.is_available() else -1
        )
        logger.info("✅ Text emotion model downloaded")
        
    except Exception as e:
        logger.warning(f"⚠️ Text model download failed (optional): {e}")

def main():
    """Download all models"""
    logger.info("🚀 Starting model downloads...")
    
    # Create model directory
    os.makedirs(MODEL_CACHE_DIR, exist_ok=True)
    
    try:
        # Download models
        download_audio_models()
        download_deepface_models()
        download_text_models()
        
        logger.info("🎉 All models downloaded successfully!")
        
        # Print model cache size
        import subprocess
        try:
            result = subprocess.run(['du', '-sh', MODEL_CACHE_DIR], 
                                    capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                logger.info(f"📦 Model cache size: {result.stdout.strip()}")
        except Exception:
            # For Windows, use shutil
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(MODEL_CACHE_DIR):
                for f in filenames:
                    fp = os.path.join(dirpath, f)
                    total_size += os.path.getsize(fp)
            size_mb = total_size / (1024 * 1024)
            logger.info(f"📦 Model cache size: {size_mb:.2f} MB")

    except Exception as e:
        logger.error(f"❌ Model download failed: {e}")
        raise

if __name__ == "__main__":
    main()
