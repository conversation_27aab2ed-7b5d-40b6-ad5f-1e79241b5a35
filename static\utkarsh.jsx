import { useEffect, useRef, useState } from 'react';

export function useWebSocketChat({
  patientId,
  persona,
  token,
  selectedVoice,
  conversationId, // Add conversation_id support
  setPatientInfo,
  setCompleteAudio,
  onStreamingStart,
  onStreamingText,
  onStreamingAudio,
  onStreamingComplete,
  onStreamingError,
  onTranscription,
  onConversationHistory,
  onAuthSuccess, // Add auth success callback
  onError, // Add error callback
}) {
  const [socket, setSocket] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('Disconnected');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const reconnectAttemptsRef = useRef(0);
  const heartbeatIntervalRef = useRef(null);
  const MAX_RECONNECTS = 5;
  const getActualVoice = (voice) => {
    // Map persona-specific voices to valid OpenAI voice names
    if (voice === 'default' || voice === 'psychologist') {
      return 'onyx'; // Default voice for psychologist
    }
    if (voice === 'dietician') {
      return 'shimmer'; // Default voice for dietician
    }

    // Validate that the voice is one of the allowed OpenAI voices
    const validVoices = ['nova', 'shimmer', 'echo', 'onyx', 'fable', 'alloy'];
    if (validVoices.includes(voice)) {
      return voice;
    }

    // Fallback to persona-specific default
    return persona === 'psychologist' ? 'onyx' : 'shimmer';
  };
  const connect = () => {
    console.log('🔗 Connecting with persona:', persona);
    console.log('🔗 Patient ID:', patientId);
    console.log('🔗 Conversation ID:', conversationId);

    // Reset unmounting flag when connecting
    isUnmountingRef.current = false;

    // Validate required parameters
    if (!patientId || !persona || !token) {
      console.error('❌ Missing required parameters for connection');
      onError?.('Missing required parameters: patientId, persona, or token');
      return;
    }

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    // Use environment variable or fallback to hardcoded URL
    // const baseUrl = process.env.REACT_APP_DOCTOR_URL?.replace(/\/$/, '') ||
    const baseUrl ='prasha-chat-bot.eastus.azurecontainer.io:8000';
    const wsUrl = `${protocol}//${baseUrl}/chat-final3/${patientId}/${persona}`;

    console.log('🔗 Connecting to WebSocket:', wsUrl);

    // Close existing socket if any
    if (socket) {
      console.log('🔌 Closing existing socket');
      socket.close(1000, 'Reconnecting');
    }

    const newSocket = new WebSocket(wsUrl);

    newSocket.onopen = () => {
      console.log('🔌 WebSocket connected successfully');
      setSocket(newSocket);
      setConnectionStatus('Connected');
      setIsAuthenticated(false); // Reset auth status
      reconnectAttemptsRef.current = 0;
      startHeartbeat(newSocket);

      // Send authentication payload with all required fields
      const authPayload = {
        token,
        voice: getActualVoice(selectedVoice),
        ...(conversationId && { conversation_id: conversationId }) // Include conversation_id if provided
      };

      console.log('🔐 Sending auth payload:', authPayload);
      newSocket.send(JSON.stringify(authPayload));
    };
    newSocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        handleWebSocketMessage(data);
      } catch (err) {
        console.error('❌ Failed to parse WebSocket message:', err, 'Raw data:', event.data);
        // Don't call onError for parsing errors as they shouldn't disconnect the socket
        console.warn('⚠️ Continuing with connection despite parsing error');
      }
    };

    newSocket.onclose = (event) => {
      console.log('🔌 WebSocket closed:', {
        code: event.code,
        reason: event.reason || 'No reason provided',
        wasClean: event.wasClean
      });

      setSocket(null);
      setConnectionStatus('Disconnected');
      setIsAuthenticated(false);
      stopHeartbeat();

      // Check if this is an intentional unmount
      if (isUnmountingRef.current) {
        console.log('✅ WebSocket closed due to component unmount - no reconnection needed');
        return;
      }

      // Handle different close codes
      if (event.code === 1000) {
        // Normal closure - check if it's intentional
        if (event.reason === 'Component unmounted' || event.reason === 'Manual disconnect') {
          console.log('✅ WebSocket closed normally (intentional)');
          return;
        }
        console.log('✅ WebSocket closed normally');
        return;
      }

      if (event.code === 1006) {
        // Abnormal closure (network issues)
        console.warn('⚠️ WebSocket closed abnormally (network issue)');
      } else if (event.code === 1011) {
        // Server error
        console.error('❌ WebSocket closed due to server error');
        onError?.('Server error occurred');
      } else if (event.code === 1002) {
        // Protocol error
        console.error('❌ WebSocket protocol error');
        onError?.('Protocol error');
        return; // Don't reconnect on protocol errors
      }

      // Attempt reconnection for recoverable errors only if not unmounting
      if (!isUnmountingRef.current && reconnectAttemptsRef.current < MAX_RECONNECTS) {
        reconnectAttemptsRef.current++;
        const retryDelay = Math.min(2000 * reconnectAttemptsRef.current, 30000); // Cap at 30 seconds
        console.log(`🔄 Reconnecting in ${retryDelay / 1000}s... (attempt ${reconnectAttemptsRef.current}/${MAX_RECONNECTS})`);
        setTimeout(() => {
          if (!isUnmountingRef.current) {
            connect();
          }
        }, retryDelay);
      } else if (!isUnmountingRef.current) {
        console.error('❌ Max reconnection attempts reached');
        onError?.('Connection failed. Please refresh the page.');
      }
    };

    newSocket.onerror = (error) => {
      console.error('❌ WebSocket error:', error);
      setConnectionStatus('Error');
      onError?.('WebSocket connection error');
    };
  };
  const handleWebSocketMessage = (data) => {
    if (!data) {
      console.warn('⚠️ Received empty message data');
      return;
    }

    // Don't process messages if component is unmounting
    if (isUnmountingRef.current) {
      console.log('🚫 Ignoring message - component is unmounting:', data.type);
      return;
    }

    console.log('📨 Received message:', data.type, data);

    switch (data.type) {
      case 'ping':
        console.log('💓 Ping received, sending pong');
        if (socket?.readyState === WebSocket.OPEN) {
          socket.send(JSON.stringify({ type: 'pong' }));
        }
        return;

      case 'pong':
        console.log('💓 Pong received');
        return;

      case 'auth_success':
        console.log('✅ Authentication successful');
        setIsAuthenticated(true);
        setConnectionStatus('Authenticated');

        // Set patient info if provided
        if (data.patient_info) {
          console.log('👤 Setting patient info:', data.patient_info);
          setPatientInfo?.(data.patient_info);
        }

        // Call auth success callback
        onAuthSuccess?.(data);
        return;

      case 'conversation_history':
        console.log('📜 Conversation history received:', data.messages?.length, 'messages');
        onConversationHistory?.(data);
        return;

      case 'streaming_start':
        console.log('🌊 Streaming started for persona:', data.persona, 'voice:', data.voice);
        onStreamingStart?.(data);
        return;

      case 'streaming_text':
        // Validate and sanitize text data
        const textData = {
          ...data,
          text: typeof data.text === 'string' ? data.text : String(data.text || ''),
          persona: data.persona || 'unknown',
          is_complete: Boolean(data.is_complete)
        };

        console.log('📝 Streaming text chunk:', textData.text?.substring(0, 50) + '...');
        onStreamingText?.(textData);
        return;

      case 'streaming_audio':
        console.log('🔊 Streaming audio chunk received, sequence:', data.sequence);
        onStreamingAudio?.(data);
        return;

      case 'complete_audio':
        console.log('🎵 Complete audio received');
        // Handle audio generation errors gracefully
        if (data.error) {
          console.warn('⚠️ Audio generation error:', data.error);
          // Still call the callback but with empty audio
          setCompleteAudio?.('');
        } else {
          setCompleteAudio?.(data.audio);
        }
        return;

      case 'streaming_complete':
        console.log('✅ Streaming completed');
        onStreamingComplete?.(data);
        return;

      case 'streaming_error':
        console.error('❌ Streaming error:', data.error);
        onStreamingError?.(data.error);
        return;

      case 'transcription':
        console.log('🎤 Transcription received:', data.text);
        onTranscription?.(data.text);
        return;

      case 'error':
        console.error('❌ Server error:', data.message || data.error);
        onError?.(data.message || data.error);
        return;

      default:
        console.log('🔍 Unhandled message type:', data.type);
        if (data.processing_time) {
          console.log(`⏱️ Processing time: ${data.processing_time}s`);
        }
        // Handle any other data that might be useful
        if (data.extracted_keywords) {
          console.log('🏷️ Keywords:', data.extracted_keywords);
        }
        if (data.current_persona) {
          console.log('👨‍⚕️ Current persona:', data.current_persona);
        }
    }
  };
  const startHeartbeat = (ws) => {
    stopHeartbeat();
    heartbeatIntervalRef.current = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: 'ping' }));
        console.log(':heartbeat: Ping sent');
      }
    }, 30000);
  };
  const stopHeartbeat = () => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  };

  // Send text message
  const sendMessage = (text, audioEmotion = null, imageEmotion = null) => {
    if (!socket || socket.readyState !== WebSocket.OPEN) {
      console.error('❌ Cannot send message: WebSocket not connected');
      onError?.('WebSocket not connected');
      return false;
    }

    if (!isAuthenticated) {
      console.error('❌ Cannot send message: Not authenticated');
      onError?.('Not authenticated');
      return false;
    }

    if (!text?.trim()) {
      console.error('❌ Cannot send empty message');
      return false;
    }

    const messagePayload = {
      text: text.trim(),
      voice: getActualVoice(selectedVoice),
      ...(audioEmotion && { audio_emotion: audioEmotion }),
      ...(imageEmotion && { image_emotion: imageEmotion })
    };

    console.log('📤 Sending text message:', messagePayload);

    try {
      socket.send(JSON.stringify(messagePayload));
      return true;
    } catch (error) {
      console.error('❌ Error sending message:', error);
      onError?.('Failed to send message');
      return false;
    }
  };

  // Send audio message
  const sendAudio = (audioBlob, audioEmotion = null, imageEmotion = null) => {
    if (!socket || socket.readyState !== WebSocket.OPEN) {
      console.error('❌ Cannot send audio: WebSocket not connected');
      onError?.('WebSocket not connected');
      return false;
    }

    if (!isAuthenticated) {
      console.error('❌ Cannot send audio: Not authenticated');
      onError?.('Not authenticated');
      return false;
    }

    if (!audioBlob) {
      console.error('❌ Cannot send empty audio');
      return false;
    }

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        try {
          const base64Audio = reader.result.split(',')[1]; // Remove data:audio/webm;base64, prefix

          const audioPayload = {
            audio: base64Audio,
            voice: getActualVoice(selectedVoice),
            ...(audioEmotion && { audio_emotion: audioEmotion }),
            ...(imageEmotion && { image_emotion: imageEmotion })
          };

          console.log('📤 Sending audio message, size:', base64Audio.length);
          socket.send(JSON.stringify(audioPayload));
          resolve(true);
        } catch (error) {
          console.error('❌ Error sending audio:', error);
          onError?.('Failed to send audio');
          reject(error);
        }
      };
      reader.onerror = () => {
        console.error('❌ Error reading audio blob');
        onError?.('Failed to read audio');
        reject(new Error('Failed to read audio blob'));
      };
      reader.readAsDataURL(audioBlob);
    });
  };

  // Disconnect function
  const disconnect = () => {
    if (socket) {
      console.log('🔌 Manually disconnecting WebSocket');
      isUnmountingRef.current = true; // Prevent reconnection attempts
      socket.close(1000, 'Manual disconnect');
      setSocket(null);
      setConnectionStatus('Disconnected');
      setIsAuthenticated(false);
      stopHeartbeat();
    }
  };

  // Clean up on unmount - use ref to avoid dependency issues
  const socketRef = useRef(socket);
  const isUnmountingRef = useRef(false);

  // Update socket ref when socket changes
  useEffect(() => {
    socketRef.current = socket;
  }, [socket]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      isUnmountingRef.current = true;
      if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
        console.log('🔌 Closing WebSocket on unmount');
        socketRef.current.close(1000, 'Component unmounted');
      }
      stopHeartbeat();
    };
  }, []); // Empty dependency array to only run on mount/unmount

  return {
    socket,
    connectionStatus,
    isAuthenticated,
    connectWebSocket: connect,
    sendMessage,
    sendAudio,
    disconnect
  };
}