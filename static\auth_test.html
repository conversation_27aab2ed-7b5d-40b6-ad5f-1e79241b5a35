<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            border: none;
        }
        
        .status-message {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
        }
        
        .status-success { background-color: #d4edda; color: #155724; }
        .status-error { background-color: #f8d7da; color: #721c24; }
        .status-info { background-color: #d1ecf1; color: #0c5460; }
        
        .token-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            font-family: monospace;
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white text-center">
                        <h3>🔐 Authentication Test</h3>
                        <p class="mb-0">Test JWT token authentication</p>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <button id="fetchTokenBtn" class="btn btn-primary btn-lg">
                                <i class="fas fa-key me-2"></i>Fetch JWT Token
                            </button>
                        </div>
                        
                        <!-- Status Messages -->
                        <div id="statusContainer"></div>
                        
                        <!-- Token Display -->
                        <div id="tokenContainer" style="display: none;">
                            <h5>JWT Token:</h5>
                            <div id="tokenDisplay" class="token-display"></div>
                            
                            <div class="mt-3">
                                <button id="testAudioBtn" class="btn btn-success me-2">
                                    Test Audio API
                                </button>
                                <button id="testImageBtn" class="btn btn-success">
                                    Test Image API
                                </button>
                            </div>
                        </div>
                        
                        <!-- Test Results -->
                        <div id="testResults"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        let JWT_TOKEN = null;
        
        // DOM Elements
        const fetchTokenBtn = document.getElementById('fetchTokenBtn');
        const statusContainer = document.getElementById('statusContainer');
        const tokenContainer = document.getElementById('tokenContainer');
        const tokenDisplay = document.getElementById('tokenDisplay');
        const testAudioBtn = document.getElementById('testAudioBtn');
        const testImageBtn = document.getElementById('testImageBtn');
        const testResults = document.getElementById('testResults');
        
        // Utility Functions
        function showStatus(message, type = 'info') {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status-message status-${type}`;
            statusDiv.textContent = message;
            statusContainer.innerHTML = '';
            statusContainer.appendChild(statusDiv);
            
            setTimeout(() => {
                statusDiv.remove();
            }, 5000);
        }
        
        function showTestResult(title, result, success = true) {
            const resultDiv = document.createElement('div');
            resultDiv.className = `alert ${success ? 'alert-success' : 'alert-danger'} mt-3`;
            resultDiv.innerHTML = `
                <h6>${title}</h6>
                <pre>${JSON.stringify(result, null, 2)}</pre>
            `;
            testResults.appendChild(resultDiv);
        }
        
        // Fetch JWT Token
        fetchTokenBtn.addEventListener('click', async () => {
            try {
                showStatus('Fetching JWT token...', 'info');
                
                const response = await fetch('/auth/token');
                
                if (response.ok) {
                    const data = await response.json();
                    JWT_TOKEN = data.access_token;
                    
                    tokenDisplay.textContent = JWT_TOKEN;
                    tokenContainer.style.display = 'block';
                    
                    showStatus('JWT token fetched successfully!', 'success');
                    console.log('Token data:', data);
                    
                } else {
                    const error = await response.json();
                    showStatus('Error fetching token: ' + error.detail, 'error');
                }
                
            } catch (error) {
                console.error('Error:', error);
                showStatus('Error: ' + error.message, 'error');
            }
        });
        
        // Test Audio API
        testAudioBtn.addEventListener('click', async () => {
            if (!JWT_TOKEN) {
                showStatus('Please fetch token first', 'error');
                return;
            }
            
            try {
                showStatus('Testing audio API endpoint...', 'info');
                
                // Create a dummy audio base64 (empty WAV file)
                const dummyAudio = "data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT";
                
                const response = await fetch('/conversation-audio/analyze-conversation-audio', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${JWT_TOKEN}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        patient_id: "test-patient-123",
                        conversation_id: "test-conversation-456",
                        audio_base64: dummyAudio,
                        timestamp: new Date().toISOString()
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showTestResult('Audio API Test - SUCCESS', result, true);
                    showStatus('Audio API test successful!', 'success');
                } else {
                    const error = await response.json();
                    showTestResult('Audio API Test - FAILED', error, false);
                    showStatus('Audio API test failed: ' + error.detail, 'error');
                }
                
            } catch (error) {
                console.error('Audio API test error:', error);
                showTestResult('Audio API Test - ERROR', { error: error.message }, false);
                showStatus('Audio API test error: ' + error.message, 'error');
            }
        });
        
        // Test Image API
        testImageBtn.addEventListener('click', async () => {
            if (!JWT_TOKEN) {
                showStatus('Please fetch token first', 'error');
                return;
            }
            
            try {
                showStatus('Testing image API endpoint...', 'info');
                
                // Create a dummy image base64 (1x1 pixel PNG)
                const dummyImage = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
                
                const response = await fetch('/conversation-image/analyze-conversation-image', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${JWT_TOKEN}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        patient_id: "test-patient-123",
                        conversation_id: "test-conversation-456",
                        image_base64: dummyImage,
                        timestamp: new Date().toISOString()
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showTestResult('Image API Test - SUCCESS', result, true);
                    showStatus('Image API test successful!', 'success');
                } else {
                    const error = await response.json();
                    showTestResult('Image API Test - FAILED', error, false);
                    showStatus('Image API test failed: ' + error.detail, 'error');
                }
                
            } catch (error) {
                console.error('Image API test error:', error);
                showTestResult('Image API Test - ERROR', { error: error.message }, false);
                showStatus('Image API test error: ' + error.message, 'error');
            }
        });
        
        // Auto-fetch token on page load
        window.addEventListener('load', () => {
            fetchTokenBtn.click();
        });
    </script>
</body>
</html>
