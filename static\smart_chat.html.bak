<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Mental Health Chat</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .chat-container {
            max-width: 800px;
            margin: 30px auto;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .chat-header {
            background-color: #4a6fa5;
            color: white;
            padding: 15px;
            text-align: center;
        }
        .chat-messages {
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            background-color: white;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 18px;
            max-width: 75%;
            position: relative;
        }
        .user-message {
            background-color: #e9f0ff;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }
        .ai-message {
            background-color: #f0f0f0;
            margin-right: auto;
            border-bottom-left-radius: 5px;
        }
        .message-time {
            font-size: 0.7rem;
            color: #888;
            margin-top: 5px;
            text-align: right;
        }
        .chat-input {
            display: flex;
            padding: 15px;
            background-color: #f0f0f0;
        }
        .chat-input input {
            flex-grow: 1;
            padding: 10px 15px;
            border: none;
            border-radius: 20px;
            margin-right: 10px;
        }
        .chat-input button {
            border: none;
            border-radius: 20px;
            padding: 10px 15px;
            background-color: #4a6fa5;
            color: white;
            cursor: pointer;
        }
        .chat-input button:hover {
            background-color: #3a5a8f;
        }
        .keywords {
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
        }
        .keyword-tag {
            display: inline-block;
            background-color: #e1e8f0;
            padding: 2px 8px;
            border-radius: 12px;
            margin-right: 5px;
            margin-bottom: 5px;
            font-size: 0.75rem;
        }
        .login-container {
            max-width: 500px;
            margin: 100px auto;
            padding: 20px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .record-button {
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 10px 15px;
            margin-right: 10px;
            cursor: pointer;
        }
        .record-button.recording {
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { background-color: #dc3545; }
            50% { background-color: #e17a84; }
            100% { background-color: #dc3545; }
        }
    </style>
</head>
<body>
    <div id="login-section" class="login-container">
        <h2 class="text-center mb-4">Smart Mental Health Chat</h2>
        <div class="mb-3">
            <label for="patient-id" class="form-label">Enter Patient ID:</label>
            <input type="text" class="form-control" id="patient-id" value="f31a95c6-76ef-4bb2-936c-b258285682d9" placeholder="e.g., f31a95c6-76ef-4bb2-936c-b258285682d9">
            <small class="form-text text-muted">Using static JWT token for authentication</small>
        </div>
        <button id="login-button" class="btn btn-primary w-100">Start Chat</button>
        <div id="login-error" class="mt-3 text-danger"></div>
    </div>

    <div id="chat-section" class="chat-container" style="display: none;">
        <div class="chat-header">
            <h2>Smart Mental Health Chat</h2>
            <p id="patient-name">Patient: Loading...</p>
        </div>
        <div id="chat-messages" class="chat-messages"></div>
        <div class="chat-input">
            <button id="record-button" class="record-button">
                <i class="bi bi-mic-fill"></i> Record
            </button>
            <input type="text" id="message-input" placeholder="Type your message here...">
            <button id="send-button">Send</button>
        </div>
    </div>

    <script>
        let ws;
        let patientId;
        let isRecording = false;
        let mediaRecorder;
        let audioChunks = [];

        // Static JWT token for authentication - must match the STATIC_JWT_TOKEN in .env file
        const jwtToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.3oZ2Ubh5rLBdHvQHd5Qr9GJczA5MXcxaVx5H5xLwvZ4';

        document.getElementById('login-button').addEventListener('click', async () => {
            patientId = document.getElementById('patient-id').value.trim();
            if (!patientId) {
                document.getElementById('login-error').textContent = 'Please enter a patient ID';
                return;
            }

            try {
                console.log(`Using patient ID: ${patientId}`);
                console.log('Using JWT token:', jwtToken);

                // Skip validation and directly connect to WebSocket
                // Hide login, show chat
                document.getElementById('login-section').style.display = 'none';
                document.getElementById('chat-section').style.display = 'block';

                // Set patient name (we'll use the ID since we're skipping validation)
                document.getElementById('patient-name').textContent = `Patient ID: ${patientId}`;

                // Connect WebSocket
                connectWebSocket();

                // Add welcome message
                addMessage('Welcome to Smart Mental Health Chat. How can I help you today?', 'ai');
            } catch (error) {
                console.error('Error in login process:', error);
                document.getElementById('login-error').textContent = error.message;
            }
        });

        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/smart-chat/${patientId}?token=${jwtToken}`;

            console.log(`Connecting to WebSocket with JWT token: ${wsUrl}`);

            try {
                ws = new WebSocket(wsUrl);
                console.log('WebSocket object created:', ws);
            } catch (error) {
                console.error('Error creating WebSocket:', error);
                addMessage('Error connecting to chat server. Please try again.', 'ai');
            }

            ws.onopen = () => {
                console.log('WebSocket connected successfully');
                addMessage('Connected to chat server successfully!', 'ai');
            };

            ws.onmessage = (event) => {
                console.log('Received message:', event.data);
                const data = JSON.parse(event.data);
                addMessage(data.response, 'ai', data.extracted_keywords);
            };

            ws.onclose = (event) => {
                console.log(`WebSocket disconnected. Code: ${event.code}, Reason: ${event.reason}`);

                // Check if it was an authentication error (code 1000 with auth error message)
                if (event.code === 1000 && event.reason && event.reason.includes('auth')) {
                    console.error('Authentication error in WebSocket connection');
                    addMessage('Authentication error. Please check your JWT token.', 'ai');
                    return; // Don't reconnect on auth errors
                }

                setTimeout(() => {
                    console.log('Attempting to reconnect...');
                    connectWebSocket();
                }, 3000);
            };

            ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                document.getElementById('login-error').textContent = 'WebSocket connection error. Please try again.';
                addMessage('Connection error. Please refresh the page and try again.', 'ai');
            };
        }

        document.getElementById('send-button').addEventListener('click', sendMessage);
        document.getElementById('message-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        function sendMessage() {
            const messageInput = document.getElementById('message-input');
            const message = messageInput.value.trim();

            if (!message) return;

            addMessage(message, 'user');

            ws.send(JSON.stringify({
                text: message
            }));

            messageInput.value = '';
        }

        function addMessage(message, sender, keywords = []) {
            const chatMessages = document.getElementById('chat-messages');
            const messageElement = document.createElement('div');
            messageElement.classList.add('message', sender === 'user' ? 'user-message' : 'ai-message');

            // Message text
            const messageText = document.createElement('div');
            messageText.textContent = message;
            messageElement.appendChild(messageText);

            // Keywords (for AI messages only)
            if (sender === 'ai' && keywords && keywords.length > 0) {
                const keywordsElement = document.createElement('div');
                keywordsElement.classList.add('keywords');

                keywords.forEach(keyword => {
                    const keywordTag = document.createElement('span');
                    keywordTag.classList.add('keyword-tag');
                    keywordTag.textContent = keyword;
                    keywordsElement.appendChild(keywordTag);
                });

                messageElement.appendChild(keywordsElement);
            }

            // Timestamp
            const timeElement = document.createElement('div');
            timeElement.classList.add('message-time');
            const now = new Date();
            timeElement.textContent = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            messageElement.appendChild(timeElement);

            chatMessages.appendChild(messageElement);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Voice recording functionality
        document.getElementById('record-button').addEventListener('click', toggleRecording);

        async function toggleRecording() {
            const recordButton = document.getElementById('record-button');

            if (!isRecording) {
                // Start recording
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    mediaRecorder = new MediaRecorder(stream);
                    audioChunks = [];

                    mediaRecorder.addEventListener('dataavailable', event => {
                        audioChunks.push(event.data);
                    });

                    mediaRecorder.addEventListener('stop', sendAudioMessage);

                    mediaRecorder.start();
                    isRecording = true;
                    recordButton.textContent = 'Stop Recording';
                    recordButton.classList.add('recording');
                } catch (error) {
                    console.error('Error accessing microphone:', error);
                    alert('Could not access microphone. Please check permissions.');
                }
            } else {
                // Stop recording
                mediaRecorder.stop();
                isRecording = false;
                recordButton.textContent = 'Record';
                recordButton.classList.remove('recording');
            }
        }

        async function sendAudioMessage() {
            addMessage('Processing audio...', 'user');

            const audioBlob = new Blob(audioChunks, { type: 'audio/mp3' });
            const reader = new FileReader();

            reader.onloadend = () => {
                const base64Audio = reader.result.split(',')[1];

                ws.send(JSON.stringify({
                    audio: base64Audio
                }));
            };

            reader.readAsDataURL(audioBlob);
        }
    </script>
</body>
</html>
