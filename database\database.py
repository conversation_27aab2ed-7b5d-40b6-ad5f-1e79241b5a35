from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.exc import DisconnectionError, OperationalError
from sqlalchemy.pool import QueuePool
import os
import logging
from dotenv import load_dotenv
load_dotenv()
from sqlalchemy.engine.url import URL

# Set up logging
logger = logging.getLogger(__name__)
# DATABASE_URL = "***************************************************"

postgres_username = os.getenv("postgres_username")
postgres_password = os.getenv("postgres_password")
postgres_host = os.getenv("postgres_host")
postgres_port = os.getenv("postgres_port")
postgres_database = os.getenv("postgres_database")



DATABASE_URL = URL.create(
    drivername="postgresql+psycopg2",
    username=postgres_username,
    password=postgres_password,
    host=postgres_host,
    port=postgres_port,
    database=postgres_database
)

# print(f"Database URL: {DATABASE_URL}")

# Fallback to environment variable if URL creation fails
if not DATABASE_URL:
    DATABASE_URL = os.getenv("DATABASE_URL")
    print(f"Using fallback DATABASE_URL from environment: {DATABASE_URL}")

# Create engine with connection pooling and error handling
engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=10,                    # Number of connections to maintain
    max_overflow=20,                 # Additional connections beyond pool_size
    pool_pre_ping=True,              # Validate connections before use
    pool_recycle=3600,               # Recycle connections after 1 hour
    connect_args={
        "connect_timeout": 10,       # Connection timeout in seconds
        "application_name": "mental_health_chatbot"
    }
)

# Add connection event listeners for better error handling
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """Set connection parameters for better reliability."""
    if hasattr(dbapi_connection, 'autocommit'):
        dbapi_connection.autocommit = False

@event.listens_for(engine, "checkout")
def receive_checkout(dbapi_connection, connection_record, connection_proxy):
    """Log when connections are checked out from the pool."""
    logger.debug("Connection checked out from pool")

@event.listens_for(engine, "checkin")
def receive_checkin(dbapi_connection, connection_record):
    """Log when connections are returned to the pool."""
    logger.debug("Connection returned to pool")

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Dependency for getting DB session with error handling
def get_db():
    """Get database session with proper error handling and cleanup."""
    db = SessionLocal()
    try:
        # Test the connection
        from sqlalchemy import text
        db.execute(text("SELECT 1"))
        yield db
    except (DisconnectionError, OperationalError) as e:
        logger.error(f"Database connection error: {e}")
        db.rollback()
        # Try to close the session gracefully
        try:
            db.close()
        except Exception as close_error:
            logger.error(f"Error closing database session: {close_error}")
        # Re-raise the original error
        raise e
    except Exception as e:
        logger.error(f"Unexpected database error: {e}")
        db.rollback()
        raise e
    finally:
        # Safe cleanup
        try:
            if db:
                db.close()
        except Exception as cleanup_error:
            logger.warning(f"Error during database cleanup: {cleanup_error}")

def get_db_safe():
    """Get database session with retry logic for critical operations."""
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            db = SessionLocal()
            # Test the connection
            from sqlalchemy import text
            db.execute(text("SELECT 1"))
            return db
        except (DisconnectionError, OperationalError) as e:
            retry_count += 1
            logger.warning(f"Database connection attempt {retry_count} failed: {e}")
            if retry_count >= max_retries:
                logger.error(f"Failed to connect to database after {max_retries} attempts")
                raise e
            # Wait a bit before retrying
            import time
            time.sleep(1)
        except Exception as e:
            logger.error(f"Unexpected error getting database session: {e}")
            raise e

    return None

def check_database_health():
    """Check if database connection is healthy."""
    try:
        from sqlalchemy import text
        db = SessionLocal()
        result = db.execute(text("SELECT 1")).fetchone()
        db.close()
        return True, "Database connection healthy"
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False, str(e)

def get_connection_pool_status():
    """Get connection pool status for monitoring."""
    try:
        pool = engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }
    except Exception as e:
        logger.error(f"Error getting pool status: {e}")
        return {"error": str(e)}
