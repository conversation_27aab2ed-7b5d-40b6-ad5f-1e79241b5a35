import React, { useState, useEffect, useRef } from 'react';
import './ChatFinal3.css'; // We'll create this CSS file

const ChatFinal3 = ({ 
  patientId = "f31a95c6-76ef-4bb2-936c-b258285682d9", 
  persona = "psychologist",
  token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.Ij0BnOgMwKFwB7VOzRdL-qdpEXNF7YvZZ8R_c-vKbcM",
  conversationId = null
}) => {
  // State variables
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('Disconnected');
  const [messages, setMessages] = useState([]);
  const [messageInput, setMessageInput] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [patientInfo, setPatientInfo] = useState(null);
  const [statusMessage, setStatusMessage] = useState('Ready to connect');
  const [selectedVoice, setSelectedVoice] = useState('default');
  const [currentPersona, setCurrentPersona] = useState(persona);
  
  // Streaming state
  const [isStreamingActive, setIsStreamingActive] = useState(false);
  const [currentStreamingMessage, setCurrentStreamingMessage] = useState(null);
  const [streamingAudioQueue, setStreamingAudioQueue] = useState([]);
  const [isPlayingStreamingAudio, setIsPlayingStreamingAudio] = useState(false);
  const [completeAudio, setCompleteAudio] = useState(null);

  // Refs
  const chatMessagesRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const currentAudioRef = useRef(null);
  const heartbeatIntervalRef = useRef(null);
  const reconnectAttemptsRef = useRef(0);

  // Constants
  const MAX_RECONNECTS = 5;
  const VOICE_OPTIONS = [
    { value: 'default', label: 'Default (Auto-select by specialist)' },
    { value: 'nova', label: 'Nova (Female, Soft)' },
    { value: 'alloy', label: 'Alloy (Neutral)' },
    { value: 'echo', label: 'Echo (Male)' },
    { value: 'fable', label: 'Fable (Female)' },
    { value: 'onyx', label: 'Onyx (Male, Deep)' },
    { value: 'shimmer', label: 'Shimmer (Female, Clear)' }
  ];

  // Helper function to get actual voice to send
  const getActualVoice = (voice) => {
    if (voice === 'default') {
      return currentPersona === 'psychologist' ? 'onyx' : 'shimmer';
    }
    return voice;
  };

  // Helper function to get default voice description
  const getDefaultVoiceDescription = () => {
    const defaultVoice = currentPersona === 'psychologist' ? 'Onyx (Male, Deep)' : 'Shimmer (Female, Clear)';
    const specialistName = currentPersona === 'psychologist' ? 'Psychologist' : 'Dietician';
    return `Default: ${specialistName} uses ${defaultVoice}`;
  };

  // Update current persona when prop changes
  useEffect(() => {
    setCurrentPersona(persona);
  }, [persona]);

  // Auto-connect on component mount
  useEffect(() => {
    connectToWebSocket();
    return () => {
      if (socket) {
        socket.close();
      }
      stopHeartbeat();
    };
  }, [patientId, persona]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Utility functions
  const scrollToBottom = () => {
    setTimeout(() => {
      if (chatMessagesRef.current) {
        chatMessagesRef.current.scrollTop = chatMessagesRef.current.scrollHeight;
      }
    }, 100);
  };

  const showStatusMessage = (message, autoHide = true) => {
    setStatusMessage(message);
    if (autoHide) {
      setTimeout(() => {
        setStatusMessage('');
      }, 3000);
    }
  };

  const base64ToBlob = (base64, mimeType) => {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  };

  // Heartbeat functionality
  const startHeartbeat = () => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }
    heartbeatIntervalRef.current = setInterval(() => {
      if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({ type: 'ping' }));
        console.log('💓 Heartbeat ping sent');
      }
    }, 30000);
  };

  const stopHeartbeat = () => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  };

  // Audio functions
  const playStreamingAudio = (audioBase64) => {
    if (!audioBase64) return;

    setIsPlayingStreamingAudio(true);
    const audioBlob = base64ToBlob(audioBase64, 'audio/mpeg');
    const audioUrl = URL.createObjectURL(audioBlob);
    const audio = new Audio(audioUrl);

    audio.onended = () => {
      setIsPlayingStreamingAudio(false);
      URL.revokeObjectURL(audioUrl);
      
      // Play next audio in queue
      setStreamingAudioQueue(prev => {
        const newQueue = [...prev];
        if (newQueue.length > 0) {
          const nextAudio = newQueue.shift();
          playStreamingAudio(nextAudio);
        }
        return newQueue;
      });
    };

    audio.onerror = (e) => {
      console.error('🎵 Error playing streaming audio:', e);
      setIsPlayingStreamingAudio(false);
      URL.revokeObjectURL(audioUrl);
    };

    audio.play().catch(e => {
      console.error('🎵 Failed to play streaming audio:', e);
      setIsPlayingStreamingAudio(false);
      URL.revokeObjectURL(audioUrl);
    });
  };

  const playCompleteAudio = (audioBase64, messageId) => {
    if (!audioBase64) return;

    // Stop current audio if playing
    if (currentAudioRef.current) {
      currentAudioRef.current.pause();
      currentAudioRef.current = null;
    }

    const audioBlob = base64ToBlob(audioBase64, 'audio/mpeg');
    const audioUrl = URL.createObjectURL(audioBlob);
    currentAudioRef.current = new Audio(audioUrl);

    currentAudioRef.current.onended = () => {
      URL.revokeObjectURL(audioUrl);
      currentAudioRef.current = null;
      // Update button state in messages
      setMessages(prev => prev.map(msg => 
        msg.id === messageId ? { ...msg, isPlaying: false } : msg
      ));
    };

    currentAudioRef.current.onerror = (e) => {
      console.error('🎵 Error playing complete audio:', e);
      URL.revokeObjectURL(audioUrl);
      currentAudioRef.current = null;
      setMessages(prev => prev.map(msg => 
        msg.id === messageId ? { ...msg, isPlaying: false } : msg
      ));
    };

    // Update button state
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, isPlaying: true } : msg
    ));

    currentAudioRef.current.play().catch(e => {
      console.error('🎵 Failed to play complete audio:', e);
      URL.revokeObjectURL(audioUrl);
      currentAudioRef.current = null;
      setMessages(prev => prev.map(msg => 
        msg.id === messageId ? { ...msg, isPlaying: false } : msg
      ));
    });
  };

  // Streaming functions
  const startStreamingMessage = () => {
    if (currentStreamingMessage) {
      finishStreamingMessage();
    }

    const newMessage = {
      id: Date.now(),
      text: '',
      sender: 'ai',
      timestamp: new Date(),
      isStreaming: true,
      keywords: [],
      audio: null
    };

    setCurrentStreamingMessage(newMessage);
    setMessages(prev => [...prev, newMessage]);
    setIsStreamingActive(true);
    setStreamingAudioQueue([]);
  };

  const appendToStreamingMessage = (text) => {
    if (!currentStreamingMessage || !text) return;

    // Skip JSON metadata
    const jsonFields = ['extracted_keywords', 'suggested_specialist', 'current_persona', 'response_id'];
    const cleanText = text.toLowerCase().trim();
    
    if (jsonFields.some(field => cleanText.includes(field)) || /^[\{\}\[\],:"'\s]*$/.test(cleanText)) {
      return;
    }

    setMessages(prev => prev.map(msg => 
      msg.id === currentStreamingMessage.id 
        ? { ...msg, text: msg.text + text }
        : msg
    ));
  };

  const finishStreamingMessage = (keywords = [], audio = null) => {
    if (!currentStreamingMessage) return;

    setMessages(prev => prev.map(msg => 
      msg.id === currentStreamingMessage.id 
        ? { 
            ...msg, 
            isStreaming: false, 
            keywords: keywords || [], 
            audio: audio || completeAudio 
          }
        : msg
    ));

    setCurrentStreamingMessage(null);
    setIsStreamingActive(false);
    setCompleteAudio(null);
  };

  // WebSocket connection
  const connectToWebSocket = () => {
    if (socket) {
      socket.close();
    }

    setConnectionStatus('Connecting...');
    showStatusMessage('Connecting to server...', false);

    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/chat-final3/${patientId}/${currentPersona}`;
      
      const newSocket = new WebSocket(wsUrl);

      newSocket.onopen = (event) => {
        console.log('🔌 WebSocket connected');
        setSocket(newSocket);
        setIsConnected(true);
        setConnectionStatus('Connected');
        reconnectAttemptsRef.current = 0;
        startHeartbeat();

        // Send authentication with actual voice
        const actualVoice = getActualVoice(selectedVoice);
        const authData = {
          token: token,
          voice: actualVoice
        };

        console.log(`🎵 Authenticating with voice: ${actualVoice} (selected: ${selectedVoice})`);
        
        if (conversationId) {
          authData.conversation_id = conversationId;
        }

        newSocket.send(JSON.stringify(authData));
        showStatusMessage(`Connected to ${currentPersona} specialist`);
      };

      newSocket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleWebSocketMessage(data);
        } catch (error) {
          console.error('❌ Error parsing message:', error);
          showStatusMessage(`Message processing error: ${error.message}`);
          if (isStreamingActive) {
            setIsStreamingActive(false);
            setCurrentStreamingMessage(null);
          }
        }
      };

      newSocket.onclose = (event) => {
        console.log('🔌 WebSocket disconnected:', event.code, event.reason);
        setSocket(null);
        setIsConnected(false);
        setConnectionStatus('Disconnected');
        stopHeartbeat();

        if (isStreamingActive) {
          setIsStreamingActive(false);
          setCurrentStreamingMessage(null);
        }

        if (event.code !== 1000 && reconnectAttemptsRef.current < MAX_RECONNECTS) {
          reconnectAttemptsRef.current++;
          showStatusMessage(`Reconnecting... (${reconnectAttemptsRef.current}/${MAX_RECONNECTS})`, false);
          setTimeout(() => {
            connectToWebSocket();
          }, 2000 * reconnectAttemptsRef.current);
        } else if (event.code !== 1000) {
          showStatusMessage('Connection failed. Please refresh the page.', false);
        } else {
          showStatusMessage('Disconnected from server');
        }
      };

      newSocket.onerror = (error) => {
        console.error('🔌 WebSocket error:', error);
        setConnectionStatus('Connection Error');
        showStatusMessage('Connection error occurred', true);
      };

    } catch (error) {
      console.error('❌ Error creating WebSocket:', error);
      setConnectionStatus('Connection Failed');
      showStatusMessage('Failed to create connection', true);
    }
  };

  // Handle WebSocket messages
  const handleWebSocketMessage = (data) => {
    // Handle server heartbeat ping
    if (data.type === 'ping') {
      console.log('💓 Received server ping, sending pong');
      socket.send(JSON.stringify({"type": "pong"}));
      return;
    }

    // Handle heartbeat pong responses
    if (data.type === 'pong') {
      console.log('💓 Heartbeat pong received');
      return;
    }

    // Handle error
    if (data.error) {
      showStatusMessage(`Error: ${data.error}`, true);
      return;
    }

    // Handle authentication success
    if (data.type === 'auth_success') {
      console.log('✅ Authentication successful');
      if (data.patient_info) {
        setPatientInfo(data.patient_info);
      }
      showStatusMessage(`Connected to ${currentPersona} specialist - Ready to chat!`);
      return;
    }

    // Handle conversation history
    if (data.type === 'conversation_history') {
      console.log('📜 Loading conversation history');
      if (data.messages && data.messages.length > 0) {
        const historyMessages = data.messages.map((msg, index) => ({
          id: `history-${index}`,
          text: msg.message,
          sender: msg.sender === 'user' ? 'user' : 'ai',
          timestamp: new Date(msg.timestamp),
          isStreaming: false,
          keywords: [],
          audio: null
        }));
        setMessages(historyMessages);
      }
      if (data.title) {
        showStatusMessage(`Loaded conversation: ${data.title}`);
      }
      return;
    }

    // Handle streaming start
    if (data.type === 'streaming_start') {
      const voice = data.voice || 'unknown';
      const persona = data.persona || currentPersona;
      console.log(`🌊 Streaming started - Persona: ${persona}, Voice: ${voice}`);
      startStreamingMessage();
      return;
    }

    // Handle streaming text
    if (data.type === 'streaming_text') {
      if (!currentStreamingMessage) {
        startStreamingMessage();
      }
      appendToStreamingMessage(data.text);
      return;
    }

    // Handle streaming audio
    if (data.type === 'streaming_audio') {
      if (streamingAudioQueue.length > 100) {
        setStreamingAudioQueue(prev => prev.slice(1));
        console.warn('⚠️ Audio queue full, dropping oldest audio');
      }

      if (isPlayingStreamingAudio) {
        setStreamingAudioQueue(prev => [...prev, data.audio]);
      } else {
        playStreamingAudio(data.audio);
      }
      return;
    }

    // Handle complete audio
    if (data.type === 'complete_audio') {
      setCompleteAudio(data.audio);
      console.log('🎵 Received complete audio for full playback');
      return;
    }

    // Handle streaming completion
    if (data.type === 'streaming_complete') {
      console.log('🌊 Streaming completed');
      setTimeout(() => {
        finishStreamingMessage(
          data.extracted_keywords || [],
          completeAudio || null
        );
      }, 50);
      return;
    }

    // Handle streaming error
    if (data.type === 'streaming_error') {
      if (currentStreamingMessage) {
        finishStreamingMessage();
      }
      showStatusMessage(`Streaming error: ${data.error}`, true);
      setIsStreamingActive(false);
      setCurrentStreamingMessage(null);
      return;
    }

    // Handle transcription
    if (data.type === 'transcription') {
      const transcription = data.text.trim();
      if (transcription) {
        addMessage(transcription, 'user');
        setMessageInput('');
      }
      return;
    }

    // Handle general error messages
    if (data.type === 'error') {
      showStatusMessage(`Error: ${data.message}`, true);
      return;
    }

    // Handle processing time info
    if (data.processing_time) {
      console.log(`⏱️ Request processed in: ${data.processing_time}`);
    }
  };

  // Add regular message
  const addMessage = (text, sender, keywords = [], audio = null) => {
    const newMessage = {
      id: Date.now(),
      text,
      sender,
      timestamp: new Date(),
      isStreaming: false,
      keywords: keywords || [],
      audio,
      isPlaying: false
    };

    setMessages(prev => [...prev, newMessage]);
  };

  // Send message function
  const sendMessage = () => {
    const message = messageInput.trim();
    if (!message || !socket || socket.readyState !== WebSocket.OPEN) {
      return;
    }

    // Add user message to chat
    addMessage(message, 'user');

    // Send to server with actual voice
    const actualVoice = getActualVoice(selectedVoice);
    socket.send(JSON.stringify({
      text: message,
      voice: actualVoice
    }));

    console.log(`🎵 Sending message with voice: ${actualVoice} (selected: ${selectedVoice})`);

    // Clear input
    setMessageInput('');
  };

  // Audio recording functions
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      audioChunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64Audio = reader.result.split(',')[1];
          if (socket && socket.readyState === WebSocket.OPEN) {
            const actualVoice = getActualVoice(selectedVoice);
            socket.send(JSON.stringify({
              audio: base64Audio,
              voice: actualVoice
            }));

            console.log(`🎵 Sending audio with voice: ${actualVoice} (selected: ${selectedVoice})`);
          }
        };
        reader.readAsDataURL(audioBlob);

        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);
      showStatusMessage('Recording... Click again to stop');

    } catch (error) {
      console.error('Error starting recording:', error);
      showStatusMessage('Error accessing microphone');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      showStatusMessage('Processing audio...');
    }
  };

  const toggleRecording = () => {
    if (!socket || socket.readyState !== WebSocket.OPEN) {
      showStatusMessage('Please connect to the server first');
      return;
    }

    if (!isRecording) {
      startRecording();
    } else {
      stopRecording();
    }
  };

  // Handle key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      sendMessage();
    }
  };

  // Render patient info
  const renderPatientInfo = () => {
    if (!patientInfo) {
      return (
        <div className="d-flex justify-content-center align-items-center h-100">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      );
    }

    return (
      <div>
        {patientInfo.patient && (
          <div className="info-section">
            <h4>Basic Info</h4>
            <div className="info-item">
              <strong>Name:</strong> {patientInfo.patient.name || 'N/A'}
            </div>
            <div className="info-item">
              <strong>Age:</strong> {patientInfo.patient.age || 'N/A'}
            </div>
            <div className="info-item">
              <strong>Gender:</strong> {patientInfo.patient.gender || 'N/A'}
            </div>
          </div>
        )}

        {patientInfo.medical_history && patientInfo.medical_history.length > 0 && (
          <div className="info-section">
            <h4>Medical History</h4>
            {patientInfo.medical_history.slice(0, 3).map((mh, index) => (
              <div key={index} className="info-item">
                <strong>{mh.condition || 'Condition'}:</strong> {mh.notes || 'N/A'}
                {mh.diagnosis_date && (
                  <div className="text-muted small">{mh.diagnosis_date}</div>
                )}
              </div>
            ))}
          </div>
        )}

        {patientInfo.onboarding_questions && patientInfo.onboarding_questions.length > 0 && (
          <div className="info-section">
            <h4>Health Information</h4>
            {patientInfo.onboarding_questions.slice(0, 3).map((q, index) => (
              <div key={index} className="info-item">
                <strong>Q:</strong> {q.question || 'N/A'}
                <div><strong>A:</strong> {q.answer || 'N/A'}</div>
              </div>
            ))}
          </div>
        )}

        {patientInfo.emotion_analysis && patientInfo.emotion_analysis.length > 0 && (
          <div className="info-section">
            <h4>Recent Mood</h4>
            {patientInfo.emotion_analysis.slice(0, 2).map((e, index) => (
              <div key={index} className="info-item">
                <strong>Emotion:</strong> {e.dominant_emotion || 'N/A'}
                <div>Confidence: {e.confidence ? (e.confidence * 100).toFixed(1) + '%' : 'N/A'}</div>
                {e.created_at && (
                  <div className="text-muted small">{new Date(e.created_at).toLocaleDateString()}</div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  // Render message
  const renderMessage = (message) => {
    const isUser = message.sender === 'user';
    const isStreaming = message.isStreaming;

    return (
      <div
        key={message.id}
        className={`message ${isUser ? 'user-message' : 'ai-message'} ${isStreaming ? 'streaming-message' : ''}`}
      >
        <div className={isStreaming ? 'streaming-text' : ''}>
          {message.text}
        </div>

        <div className="message-time">
          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </div>

        {isStreaming && (
          <div className="streaming-indicator">
            <i className="fas fa-circle-notch fa-spin"></i> Generating response...
          </div>
        )}

        {message.keywords && message.keywords.length > 0 && (
          <div className="message-keywords">
            {message.keywords.map((keyword, index) => (
              <span key={index} className="keyword-tag">
                {keyword}
              </span>
            ))}
          </div>
        )}

        {message.audio && !isStreaming && (
          <div className="audio-controls">
            <button
              className={`audio-btn ${message.isPlaying ? 'playing' : ''}`}
              onClick={() => playCompleteAudio(message.audio, message.id)}
              title={message.isPlaying ? 'Stop audio' : 'Play audio'}
            >
              <i className={`fas ${message.isPlaying ? 'fa-stop' : 'fa-play'}`}></i>
            </button>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="chat-container">
      {/* Header */}
      <div className="chat-header">
        <h2>
          <i className="fas fa-hospital-user me-2"></i> Healthcare Chat
          <span className={`persona-indicator persona-${currentPersona}`}>
            {currentPersona === 'psychologist' ? 'Psychologist (Dr. Ori)' : 'Dietician (Dr. Maya)'}
          </span>
        </h2>
        <div className="d-flex align-items-center">
          <span className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
            {connectionStatus}
          </span>
        </div>
      </div>

      {/* Status Bar */}
      {statusMessage && (
        <div className="status-bar">
          <div>{statusMessage}</div>
        </div>
      )}

      {/* Chat Body */}
      <div className="chat-body">
        {/* Sidebar */}
        <div className="sidebar">
          <div className="patient-info">
            <h3>Patient Information</h3>
            <div className="patient-info-content">
              {renderPatientInfo()}
            </div>
          </div>
        </div>

        {/* Messages */}
        <div className="chat-messages" ref={chatMessagesRef}>
          {messages.map(message => renderMessage(message))}
        </div>
      </div>

      {/* Input */}
      <div className="chat-input">
        <input
          type="text"
          value={messageInput}
          onChange={(e) => setMessageInput(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Type your message here..."
          disabled={!isConnected}
        />

        <div className="voice-selection-container">
          <select
            value={selectedVoice}
            onChange={(e) => setSelectedVoice(e.target.value)}
            className="voice-select"
            title="Select AI voice"
          >
            {VOICE_OPTIONS.map(voice => (
              <option key={voice.value} value={voice.value}>
                {voice.label}
              </option>
            ))}
          </select>
          {selectedVoice === 'default' && (
            <small className="voice-helper-text">
              {getDefaultVoiceDescription()}
            </small>
          )}
        </div>

        <button
          className={`btn-mic ${isRecording ? 'recording' : ''}`}
          onClick={toggleRecording}
          disabled={!isConnected}
          title="Record audio"
        >
          <i className="fas fa-microphone"></i>
        </button>

        <button
          className="btn-send"
          onClick={sendMessage}
          disabled={!isConnected || !messageInput.trim()}
          title="Send message"
        >
          <i className="fas fa-paper-plane"></i>
        </button>
      </div>
    </div>
  );
};

export default ChatFinal3;
