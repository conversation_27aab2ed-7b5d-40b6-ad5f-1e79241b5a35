import os
import boto3
import logging
from botocore.exceptions import ClientError
import uuid
from pathlib import Path

class PDFS3Service:
    """Service for handling PDF uploads and FAISS index storage in S3"""
    
    def __init__(self, bucket_name="prasha-health-pdfs", region="us-east-1"):
        """Initialize the S3 service with the bucket name and region"""
        self.bucket_name = bucket_name
        self.region = region
        self.s3_client = boto3.client('s3', region_name=region)
        self.logger = logging.getLogger(__name__)
        
    def upload_pdf(self, file_path, custom_filename=None):
        """
        Upload a PDF file to the S3 bucket
        
        Args:
            file_path (str): Path to the PDF file
            custom_filename (str, optional): Custom filename to use in S3. If None, uses a UUID.
            
        Returns:
            str: The S3 key of the uploaded file
        """
        try:
            # Generate a UUID-based filename if not provided
            if custom_filename is None:
                filename = f"{uuid.uuid4()}.pdf"
            else:
                filename = custom_filename
                
            # Ensure filename has .pdf extension
            if not filename.lower().endswith('.pdf'):
                filename += '.pdf'
                
            # S3 key for the PDF
            s3_key = f"pdfs/{filename}"
            
            # Upload the file
            self.s3_client.upload_file(file_path, self.bucket_name, s3_key)
            
            self.logger.info(f"Successfully uploaded PDF to {s3_key}")
            return s3_key
            
        except ClientError as e:
            self.logger.error(f"Error uploading PDF to S3: {e}")
            raise
            
    def download_pdf(self, s3_key, local_path):
        """
        Download a PDF file from the S3 bucket
        
        Args:
            s3_key (str): S3 key of the file to download
            local_path (str): Local path to save the file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            # Download the file
            self.s3_client.download_file(self.bucket_name, s3_key, local_path)
            
            self.logger.info(f"Successfully downloaded {s3_key} to {local_path}")
            return True
            
        except ClientError as e:
            self.logger.error(f"Error downloading PDF from S3: {e}")
            return False
            
    def save_faiss_index(self, index_path, index_metadata_path):
        """
        Upload FAISS index and metadata to S3
        
        Args:
            index_path (str): Path to the FAISS index file
            index_metadata_path (str): Path to the index metadata file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Upload the index file
            index_key = "faiss_index/index.faiss"
            self.s3_client.upload_file(index_path, self.bucket_name, index_key)
            
            # Upload the metadata file
            metadata_key = "faiss_index/index.pkl"
            self.s3_client.upload_file(index_metadata_path, self.bucket_name, metadata_key)
            
            self.logger.info("Successfully uploaded FAISS index to S3")
            return True
            
        except ClientError as e:
            self.logger.error(f"Error uploading FAISS index to S3: {e}")
            return False
            
    def load_faiss_index(self, local_index_path, local_metadata_path):
        """
        Download FAISS index and metadata from S3
        
        Args:
            local_index_path (str): Local path to save the index file
            local_metadata_path (str): Local path to save the metadata file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure the directories exist
            os.makedirs(os.path.dirname(local_index_path), exist_ok=True)
            os.makedirs(os.path.dirname(local_metadata_path), exist_ok=True)
            
            # Download the index file
            index_key = "faiss_index/index.faiss"
            self.s3_client.download_file(self.bucket_name, index_key, local_index_path)
            
            # Download the metadata file
            metadata_key = "faiss_index/index.pkl"
            self.s3_client.download_file(self.bucket_name, metadata_key, local_metadata_path)
            
            self.logger.info("Successfully downloaded FAISS index from S3")
            return True
            
        except ClientError as e:
            self.logger.error(f"Error downloading FAISS index from S3: {e}")
            return False
            
    def list_pdfs(self):
        """
        List all PDFs in the bucket
        
        Returns:
            list: List of PDF keys in the bucket
        """
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix="pdfs/"
            )
            
            if 'Contents' in response:
                # Filter out the directory itself
                pdf_keys = [item['Key'] for item in response['Contents'] 
                           if not item['Key'].endswith('/')]
                return pdf_keys
            else:
                return []
                
        except ClientError as e:
            self.logger.error(f"Error listing PDFs in S3: {e}")
            return []

# Example usage
if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Initialize the service
    s3_service = PDFS3Service()
    
    # Example: Upload a PDF
    # s3_service.upload_pdf("path/to/your/document.pdf")
    
    # Example: List all PDFs
    pdfs = s3_service.list_pdfs()
    print(f"Found {len(pdfs)} PDFs in the bucket:")
    for pdf in pdfs:
        print(f"  - {pdf}")
